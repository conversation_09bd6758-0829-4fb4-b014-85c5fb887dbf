-- Create the areadocs-dev database if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT FROM pg_database WHERE datname = 'areadocsdev'
    ) THEN
        PERFORM pg_sleep(2); -- Wait briefly to ensure PostgreSQL is ready
        CREATE DATABASE areadocsdev;
    END IF;
END $$;

-- Optional: Create a table or perform additional setup
CREATE TABLE IF NOT EXISTS public.test_table (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert a test record
INSERT INTO public.test_table (id, created_at) VALUES (1, CURRENT_TIMESTAMP)
ON CONFLICT (id) DO NOTHING;

-- Grant privileges to the default user
GRANT ALL PRIVILEGES ON DATABASE areadocsdev TO areadocs;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO areadocs;
