### ✅ Checklist

Please ensure the following have been addressed before requesting a review.  

#### 🔍 Code Quality
- [ ] Ran `lint`
- [ ] No hardcoded values (use constants or enums)
- [ ] No direct strings/messages (use constants/localization)
- [ ] No double quotes in `.ts` files (use single quotes)
- [ ] No commented code without a valid reason
- [ ] End of file (EOF) handled correctly
- [ ] Code is properly formatted (e.g., Prettier/ESLint)
- [ ] Naming conventions are followed
- [ ] Import order is correct and organized
- [ ] Clear and concise PR description added
- [ ] No code repeatation
- [ ] Max 400 LOC for a file

#### 🧪 Testing
- [ ] Unit tests added or updated where applicable
- [ ] Legacy tests updated or commented with reason

#### 🎥 Visual Proof
- [ ] Before and after videos/screenshots attached

#### 🔗 Project Hygiene
- [ ] Jira link(s) included
- [ ] Any linked PRs mentioned (if applicable)

---

### 🔗 Link to Issue

**Required**  
**Resolves**
- #IssueID or Jira Ticket (e.g., `#148`, `PROJ-1234`)

---

### ❓ What problem does this PR solve?

**Required**  
Briefly summarize the issue or need that prompted this PR.

---

### 💡 What is the solution?

**Required**  
Explain the approach or logic used to solve the problem.  
Note: The ultimate source of truth should be the code.

---

### 🧭 Where should the reviewer start?

**Optional**  
If this PR is large or touches multiple areas, suggest a starting point.

---

### 🧠 Any background context?

**Optional**  
Provide any additional information (e.g., library links, API references, design context) that helps reviewers understand the changes better.

---

### 🎬 Behavior Demo

**Required**  
Attach relevant screenshots or videos showing before & after behavior.

- Before:

- After:


---

### 🔗 Linked PRs

**Optional**  
Mention if this PR depends on or is connected to another PR.

- Frontend/Backend Sync PR: [link]
- Related Feature/Module PR: [link]
