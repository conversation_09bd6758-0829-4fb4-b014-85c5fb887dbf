name: Sonarqube scan for areadocs-be
on:
  workflow_dispatch:

jobs:
  sonarqube:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          java-version: '21'
          distribution: 'temurin'

      - name: Build with <PERSON><PERSON>
        run: |
          mvn clean compile -DskipTests &&
          # Debug: Show current directory and list contents
          pwd &&
          ls -la
          # Check if target/classes exists
          if [ -d "target/classes" ]; then
            echo "✅ target/classes found:"
            ls -la target/classes &&
            cd target &&
            pwd &&
            ls -la
          else
            echo "❌ target/classes not found"
            find . -name "*.class" -type f || echo "No .class files found"
          fi

      - name: SonarQube <PERSON><PERSON>
        uses: sonarsource/sonarqube-scan-action@master
        with:
          projectBaseDir: .
          args: >
            -Dsonar.organization=arealytics
            -Dsonar.projectKey=ar-app-areadocs-be
            -Dsonar.exclusions=.github/**,README.md,.gitignore,**/target/**/test/**,**/.git/**,**/*.log,**/*.jar
            -Dsonar.sources=src
            -Dsonar.java.binaries=target/classes
            -Dsonar.sourceEncoding=UTF-8
            -Dsonar.scm.provider=git
            -Dsonar.languages=java,sql
            -Dsonar.sql.dialect=mysql
            -Dsonar.verbose=true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

      - name: Quality Gate Check
        uses: sonarsource/sonarqube-quality-gate-action@master
        timeout-minutes: 5
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
