name: Deploying areadocs dev api
on:
  workflow_dispatch:
    #push:
    #branches: [main, develop]  # Optional: Auto-deploy on pushes to specific branches

jobs:
  deploy:
    name: Deploying areadocs dev api
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0  # Fetch all history for proper versioning

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Generate deployment version
        id: version
        run: echo "VERSION=$(date +'%Y%m%d%H%M%S')-$(git rev-parse --short HEAD)" >> $GITHUB_ENV

      - name: Build & Deploy
        env:
          SSH_PRIVATE_KEY: ${{ secrets.DEV_EC2_PRIVATE_KEY }}
          REMOTE_HOST: ${{ secrets.DEV_HOSTNAME }}
          REMOTE_USER: ${{ secrets.DEV_USERNAME }}
        run: |
          echo "$SSH_PRIVATE_KEY" > private_key && chmod 600 private_key
          echo "Starting deployment version $VERSION..."
          
          ssh -o StrictHostKeyChecking=no -i private_key ${REMOTE_USER}@${REMOTE_HOST} '
            # Now we have access to EC2 and will start the deploy
            set -e  # Exit immediately if a command exits with non-zero status
            
            echo "Pulling latest code..."
            cd /home/<USER>
            git fetch --all
            git checkout $(echo ${{ github.ref }} | sed -e "s#refs/heads/##g")
            git pull
            
            # Store the previous images for potential rollback
            echo "Creating backup of current images..."
            if [ -f docker-compose.yml ]; then
              sudo docker compose images > previous_images.txt
              # Tag current images with a backup tag
              if sudo docker images areadocs-backend | grep latest; then
                sudo docker tag areadocs-backend:latest areadocs-backend:backup-$VERSION
              fi
              # Note: postgres:17-alpine is an external image, so no need to tag it
            fi
            
            echo "Building and deploying new version..."
            export DEPLOY_VERSION=${{ env.VERSION }}
            sudo docker compose down
            sudo docker compose build --pull --no-cache
            sudo docker compose up -d
            
            # Simple health check
            echo "Performing health check..."
            sleep 10  # Give the container time to start
            
            # Check if service is running
            if ! sudo docker compose ps | grep "Up"; then
              echo "Deployment failed! Service is not running."
              echo "Rolling back to previous images..."
              
              # Rollback logic
              if [ -f previous_images.txt ]; then
                echo "Restoring previous images..."
                # Stop failed deployment
                sudo docker compose down
                
                # Run containers with backup images
                # Update docker-compose.yml to use backup image for app-backend
                if [ -f docker-compose.yml ]; then
                  sed -i "s|image: areadocs-backend:latest|image: areadocs-backend:backup-$VERSION|g" docker-compose.yml
                  # Ensure postgres image remains as is (postgres:17-alpine)
                  sudo docker compose up -d
                  
                  # Verify rollback
                  echo "Verifying rollback..."
                  sleep 10
                  if sudo docker compose ps | grep "Up"; then
                    echo "Rollback successful! Previous version restored."
                  else
                    echo "Rollback failed! No running services detected."
                    exit 1
                  fi
                else
                  echo "docker-compose.yml not found!"
                  exit 1
                fi
              else
                echo "No previous images found for rollback!"
                exit 1
              fi
              
              # Clean up failed deployment images
              echo "Cleaning up failed deployment..."
              sudo docker system prune -af --filter "until=1h"
              
              exit 1
            fi
            
            echo "Cleaning up old images..."
            sudo docker system prune -af --filter "until=24h"
            
            echo "Deployment successful!"
          '
          
      - name: Notify on success
        if: success()
        run: echo "Deployment succeeded! Version $VERSION deployed."
      
      - name: Notify on failure
        if: failure()
        run: echo "Deployment failed! Please check the logs."
