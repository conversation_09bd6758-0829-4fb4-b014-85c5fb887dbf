FROM maven:3.9.9-eclipse-temurin-21-jammy AS builder

WORKDIR /app

# Copy pom.xml and maven wrapper (optional if you don't need mvnw)
COPY pom.xml .
COPY .mvn/ .mvn/
#COPY .env ./.env

# Download dependencies first (cached layer)
RUN --mount=type=cache,target=/root/.m2 mvn dependency:go-offline -B

# Copy source code
COPY src ./src

# Build the application with cache mount for m2
RUN --mount=type=cache,target=/root/.m2 mvn clean package -Dmaven.test.skip=true -Pdev -Dspotless.skip=true

FROM eclipse-temurin:21-jre-jammy AS runner

WORKDIR /app
#COPY .env ./.env
# Copy the built jar from the builder stage
COPY --from=builder /app/target/app.jar app.jar

# Expose the application port
EXPOSE 8080

# Healthcheck to ensure app is running
HEALTHCHECK --interval=30s --timeout=3s \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# Add non-root user for security (optional, uncomment to enable)
# USER 1001

# Set entrypoint for the application
ENTRYPOINT ["java", "-jar", "app.jar", "--spring.profiles.active=dev"]
