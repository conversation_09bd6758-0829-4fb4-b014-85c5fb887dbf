# ar-app-areadocs-be

**AreaDocs App Backend**
Spring Boot backend application for AreaDocs, supporting REST APIs, PostgreSQL integration, spatial queries, and clean code practices.

---

## 🚀 Tech Stack

- **Java**: 21
- **Spring Boot**: 3.2.5
- **PostgreSQL** with spatial support
- **Hibernate Spatial**, **JTS Geometry**
- **MapStruct** + **Lombok** for DTO mapping
- **OpenAPI / Swagger** via SpringDoc
- **Logbook** for API logging
- **Docker + Docker Compose**

---

## 📁 Project Structure

```
src/
├── main/
│   ├── java/
│   └── resources/
│       ├── application.yml
│       ├── application-dev.yml
│       └── application-prod.yml
└── test/
```

---

## ⚙️ Environment Configuration

This project uses a comprehensive environment configuration setup with Spring profiles and environment variables.

### 📄 Configuration Files

- **application.yml**: Common configuration shared across all environments
- **application-dev.yml**: Development environment configuration
- **application-uat.yml**: UAT/Staging environment configuration
- **application-prod.yml**: Production environment configuration
- **.env**: Environment variables and secrets (not committed to version control)
- **.env.example**: Template for environment variables (safe to commit)

### 🔐 Environment Variables

Create a `.env` file in the project root with the following variables:

```properties
# Application Name
APP_NAME=areadocs

# Server Configuration
SERVER_PORT=8080

# Environment Selection
SPRING_PROFILES_ACTIVE=dev

# Development Database Configuration
DB_DEV_USERNAME=postgres
DB_DEV_PASSWORD=postgres
DB_DEV_HOST=localhost
DB_DATABASE_NAME=areadocs

# UAT Database Configuration
# DB_UAT_USERNAME=uat_user
# DB_UAT_PASSWORD=uat_pass
# DB_UAT_HOST=uat-db
# DB_DATABASE_NAME=areadocs_uat


# Production Database Configuration (these should be set in the production environment)
# DB_PROD_USERNAME=prod_user
# DB_PROD_PASSWORD=prod_pass
# DB_PROD_HOST=prod-db
# DB_DATABASE_NAME=areadocs_prod

# CORS Configuration - Which websites can access our API
CORS_ALLOWED_ORIGIN=http://localhost:4200,https://areadocs-dev.arealytics.com.au,http://localhost:8080/

# Logging Configuration
LOG_LEVEL=DEBUG

# JWT Secret (for authentication)
JWT_SECRET=your-secret-key-here-change-in-production
JWT_EXPIRATION_MS=86400000

# pgAdmin Configuration
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin123
PGADMIN_PORT=5050

# Other Application Settings
APP_UPLOAD_DIR=/home/<USER>/Desktop/Arealytics/ar-app-areadocs-be/temp/areadocs/uploads

```

### 🔄 Switching Environments

Change the active profile using one of these methods:

```bash
# Option 1: Update .env file
SPRING_PROFILES_ACTIVE=prod

# Option 2: Environment variable when running
export SPRING_PROFILES_ACTIVE=prod
./mvnw spring-boot:run

# Option 3: JVM argument
./mvnw spring-boot:run -Dspring.profiles.active=prod

# Option 4: Docker Compose environment variable
SPRING_PROFILES_ACTIVE=prod docker-compose up
```

---

## 🐳 Docker Setup

Run locally with Docker:

```bash
# Start with default environment variables dev config
docker compose -f docker-compose.dev.yml up --build

# Or specify environment file
docker compose --env-file .env.prod up --build
```

Services started:
- Spring Boot backend (port 8080)
- PostgreSQL (port 5432)
- pgAdmin (port 5050)

The Docker setup uses environment variables from your `.env` file. You can create different environment files (e.g., `.env.prod`, `.env.uat`) for different deployment scenarios.

---

## 🛠️ Build & Run

### Using Maven

```bash
# Run with default profile
./mvnw spring-boot:run

# Run with dev or prod
./mvnw spring-boot:run -Dspring-boot.run.profiles=dev
```

### With Tests

```bash
./mvnw clean test
```

---

## 📦 Notable Dependencies

- `spring-boot-starter-data-jpa`
- `spring-boot-starter-web`
- `spring-boot-starter-validation`
- `spring-boot-starter-actuator`
- `springdoc-openapi-starter-webmvc-ui`
- `hibernate-spatial`, `jts-core`
- `mapstruct`, `lombok`, `logbook-spring-boot-starter`
- `spring-boot-devtools`

---

## 🧪 Testing

- JUnit 5
- Mockito
- Test scope dependencies included in `pom.xml`

---

## 🔍 API Docs

Swagger UI available at:

```
http://localhost:8080/swagger-ui/index.html
```

---

## 🔒 Security Notes

- Use environment variables for sensitive credentials in production
- Don't commit `.env` or `application-prod.yml` with secrets to version control
- Add `.env` to your `.gitignore` file
- Only commit `.env.example` as a template
- Consider using vaults like AWS Secrets Manager or HashiCorp Vault for production secrets
- Rotate JWT secrets and database credentials regularly in production

---

## ✅ Author

Made with ❤️ by [ **Siddhartha Chinthapally** , **Chandrashekar Bemagoni** ]

---

## 📄 License

This project is licensed under the **MIT License**

Copyright (c) 2025 [Arealytics]

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
