package com.arealytics.areadocs.service;

import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import com.arealytics.areadocs.domain.Role;
import com.arealytics.areadocs.dto.requestDTO.RoleFilterDTO;
import com.arealytics.areadocs.dto.requestDTO.RoleRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.RoleDTO;
import com.arealytics.areadocs.exception.RoleException;
import com.arealytics.areadocs.mapper.RoleMapper;
import com.arealytics.areadocs.repository.PermissionRepository;
import com.arealytics.areadocs.repository.RolePermissionMappingRepository;
import com.arealytics.areadocs.repository.RoleRepository;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class RoleServiceTest {

    @Mock private RoleRepository roleRepository;

    @Mock private RoleMapper roleMapper;

    @Mock private PermissionRepository permissionRepository;

    @Mock private RolePermissionMappingRepository rolePermissionMappingRepository;

    @InjectMocks private RoleService roleService;

    private Role role;
    private RoleRequestDTO roleRequestDTO;
    private RoleDTO roleDTO;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        role = new Role();
        role.setId(1L);
        role.setName("Admin");

        roleRequestDTO = new RoleRequestDTO();
        roleRequestDTO.setName("Admin");

        roleRequestDTO.setPermissionIds(List.of(1L, 2L));

        roleDTO = new RoleDTO();
        roleDTO.setId(1L);
        roleDTO.setName("Admin");
    }

    @Test
    void testCreateRole_Success() {
        when(roleRepository.existsByName(roleRequestDTO.getName())).thenReturn(false);
        when(roleMapper.toEntity(roleRequestDTO)).thenReturn(role);
        when(roleRepository.save(role)).thenReturn(role);
        when(roleMapper.toDto(role)).thenReturn(roleDTO);

        RoleDTO result = roleService.createRole(roleRequestDTO);

        assertNotNull(result);
        assertEquals(roleDTO.getName(), result.getName());
        verify(roleRepository).existsByName(roleRequestDTO.getName());
        verify(roleRepository).save(role);
    }

    @Test
    void testCreateRole_DuplicateRoleName() {
        when(roleRepository.existsByName(roleRequestDTO.getName())).thenReturn(true);

        RoleException.DuplicateRoleNameException exception =
                assertThrows(
                        RoleException.DuplicateRoleNameException.class,
                        () -> roleService.createRole(roleRequestDTO));

        assertEquals("Role name already exists: Admin", exception.getMessage());
        verify(roleRepository).existsByName(roleRequestDTO.getName());
        verify(roleRepository, never()).save(any());
    }

    @Test
    void testGetRoleById_Success() {
        when(roleRepository.findById(1L)).thenReturn(Optional.of(role));
        when(roleMapper.toDto(role)).thenReturn(roleDTO);

        RoleDTO result = roleService.getRoleById(1L);

        assertNotNull(result);
        assertEquals(roleDTO.getId(), result.getId());
        verify(roleRepository).findById(1L);
    }

    @Test
    void testGetRoleById_NotFound() {
        when(roleRepository.findById(1L)).thenReturn(Optional.empty());

        RoleException.RoleNotFoundException exception =
                assertThrows(
                        RoleException.RoleNotFoundException.class,
                        () -> roleService.getRoleById(1L));

        assertEquals("Role not found with id: 1", exception.getMessage());
        verify(roleRepository).findById(1L);
    }

    @Test
    void testUpdateRole_Success() {
        Role updatedRole = new Role();
        updatedRole.setId(1L);
        updatedRole.setName("SuperAdmin");

        RoleRequestDTO updatedRoleRequest = new RoleRequestDTO();
        updatedRoleRequest.setName("SuperAdmin");

        RoleDTO updatedRoleDTO = new RoleDTO();
        updatedRoleDTO.setId(1L);
        updatedRoleDTO.setName("SuperAdmin");

        when(roleRepository.findById(1L)).thenReturn(Optional.of(role));
        when(roleRepository.existsByName("SuperAdmin")).thenReturn(false);

        // Update the mock to modify the role entity
        doAnswer(
                        invocation -> {
                            RoleRequestDTO dto = invocation.getArgument(0);
                            Role entity = invocation.getArgument(1);
                            entity.setName(dto.getName());
                            return null;
                        })
                .when(roleMapper)
                .updateEntityFromDto(any(RoleRequestDTO.class), any(Role.class));

        when(roleRepository.save(role)).thenReturn(updatedRole);
        when(roleMapper.toDto(updatedRole)).thenReturn(updatedRoleDTO);

        RoleDTO result = roleService.updateRole(1L, updatedRoleRequest);

        assertNotNull(result);
        assertEquals("SuperAdmin", result.getName());
        verify(roleRepository).findById(1L);
        verify(roleRepository).save(role);
        verify(roleMapper).updateEntityFromDto(updatedRoleRequest, role);
    }

    @Test
    void testDeleteRole_Success() {
        when(roleRepository.existsById(1L)).thenReturn(true);

        roleService.deleteRole(1L);

        verify(roleRepository).existsById(1L);
        verify(roleRepository).deleteById(1L);
    }

    @Test
    void testDeleteRole_NotFound() {
        when(roleRepository.existsById(1L)).thenReturn(false);

        RoleException.RoleNotFoundException exception =
                assertThrows(
                        RoleException.RoleNotFoundException.class,
                        () -> roleService.deleteRole(1L));

        assertEquals("Role not found with id: 1", exception.getMessage());
        verify(roleRepository).existsById(1L);
        verify(roleRepository, never()).deleteById(anyLong());
    }

    @Test
    void testGetRolesWithFilters() {
        RoleFilterDTO filter = new RoleFilterDTO();
        filter.setName("Admin");

        Page<Role> rolesPage = new PageImpl<>(List.of(role));
        when(roleRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(rolesPage);
        when(roleMapper.toDto(role)).thenReturn(roleDTO);

        Page<RoleDTO> result = roleService.getRolesWithFilters(filter, Pageable.unpaged());

        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals("Admin", result.getContent().get(0).getName());
        verify(roleRepository).findAll(any(Specification.class), any(Pageable.class));
    }
}
