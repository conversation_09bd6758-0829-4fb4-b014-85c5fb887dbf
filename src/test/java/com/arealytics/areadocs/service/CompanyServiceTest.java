package com.arealytics.areadocs.service;

import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import com.arealytics.areadocs.domain.Address;
import com.arealytics.areadocs.domain.Company;
import com.arealytics.areadocs.dto.requestDTO.AddressRequestDTO;
import com.arealytics.areadocs.dto.requestDTO.CompanyFilterDTO;
import com.arealytics.areadocs.dto.requestDTO.CompanyRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.CompanyDTO;
import com.arealytics.areadocs.enumeration.AddressType;
import com.arealytics.areadocs.exception.CompanyException;
import com.arealytics.areadocs.mapper.AddressMapper;
import com.arealytics.areadocs.mapper.CompanyMapper;
import com.arealytics.areadocs.mapper.CompanyRequestMapper;
import com.arealytics.areadocs.repository.CompanyRepository;
import com.arealytics.areadocs.repository.UserRepository;

import jakarta.persistence.EntityNotFoundException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CompanyServiceTest {

    @Mock private CompanyRepository companyRepository;
    @Mock private CompanyMapper companyMapper;
    @Mock private AccountService accountService;
    @Mock private AddressMapper addressMapper;
    @Mock private CompanyRequestMapper companyRequestMapper;
    @Mock private UserRepository userRepository;

    @InjectMocks private CompanyService companyService;

    private Company company;
    private CompanyDTO companyDTO;
    private CompanyRequestDTO companyRequestDTO;
    private Address primaryAddress;
    private AddressRequestDTO primaryAddressDTO;

    @BeforeEach
    void setUp() {
        company = new Company();
        company.setId(1L);
        company.setName("Original Company");
        company.setABN("**********");
        company.setIndustry("Tech");
        company.setEmployeeCount(100);

        companyDTO = new CompanyDTO();
        companyDTO.setId(1L);
        companyDTO.setName("Test Company");
        companyDTO.setABN("**********");
        companyDTO.setIndustry("Tech");
        companyDTO.setEmployeeCount(100);

        companyRequestDTO = new CompanyRequestDTO();
        companyRequestDTO.setName("Test Company");
        companyRequestDTO.setABN("**********");
        companyRequestDTO.setIndustry("Tech");
        companyRequestDTO.setEmployeeCount(100);

        primaryAddress = new Address();
        primaryAddress.setId(1L);
        primaryAddress.setAddressType(AddressType.PRIMARY);
        primaryAddress.setAddressLine1("123 Main St");
        company.setPrimaryAddress(primaryAddress);

        primaryAddressDTO = new AddressRequestDTO();
        primaryAddressDTO.setAddressLine1("123 Main St");
        companyRequestDTO.setPrimaryAddress(primaryAddressDTO);
    }

    @Test
    void createCompany_Success() {
        // Set up billing address to avoid NullPointerException
        AddressRequestDTO billingAddressDTO = new AddressRequestDTO();
        billingAddressDTO.setAddressLine1("789 Oak Ave");
        companyRequestDTO.setBillingAddress(billingAddressDTO);
        Address billingAddress = new Address();
        when(companyRepository.existsByName(anyString())).thenReturn(false);
        when(companyRequestMapper.toEntity(any(CompanyRequestDTO.class))).thenReturn(company);
        when(addressMapper.toEntity(any(AddressRequestDTO.class)))
                .thenReturn(primaryAddress, billingAddress);
        when(companyRepository.save(any(Company.class))).thenReturn(company);
        when(companyMapper.toDto(any(Company.class))).thenReturn(companyDTO);

        CompanyDTO result = companyService.createCompany(companyRequestDTO);

        assertNotNull(result);
        assertEquals(companyDTO.getId(), result.getId());
        verify(companyRepository).existsByName(companyRequestDTO.getName());
        verify(companyRequestMapper).toEntity(companyRequestDTO);
        verify(addressMapper, times(2))
                .toEntity(any(AddressRequestDTO.class)); // Primary and billing
        verify(companyRepository).save(company);
        verify(accountService).createCompanyAccount(company);
        verify(companyMapper).toDto(company);
    }

    @Test
    void createCompany_WithExistingName_ShouldThrowException() {
        when(companyRepository.existsByName(anyString())).thenReturn(true);

        CompanyException exception =
                assertThrows(
                        CompanyException.class,
                        () -> companyService.createCompany(companyRequestDTO));

        assertTrue(
                exception
                        .getMessage()
                        .equalsIgnoreCase("Company with name 'Test Company' already exists"));
        verify(companyRepository).existsByName(companyRequestDTO.getName());
        verifyNoInteractions(companyRequestMapper, addressMapper, companyMapper, accountService);
    }

    @Test
    void createCompany_WithIsBillingPrimary_Success() {
        companyRequestDTO.setIsBillingPrimary(true);
        when(companyRepository.existsByName(anyString())).thenReturn(false);
        when(companyRequestMapper.toEntity(any(CompanyRequestDTO.class))).thenReturn(company);
        when(addressMapper.toEntity(any(AddressRequestDTO.class))).thenReturn(primaryAddress);
        when(companyRepository.save(any(Company.class))).thenReturn(company);
        when(companyMapper.toDto(any(Company.class))).thenReturn(companyDTO);

        CompanyDTO result = companyService.createCompany(companyRequestDTO);

        assertNotNull(result);
        verify(addressMapper, times(1))
                .toEntity(any(AddressRequestDTO.class)); // Only primary address mapped
        verify(companyRepository).save(any(Company.class));
        verify(accountService).createCompanyAccount(any(Company.class));
    }

    @Test
    void updateCompany_Success() {
        companyRequestDTO.setName("Updated Company");
        AddressRequestDTO updatedAddressDTO = new AddressRequestDTO();
        updatedAddressDTO.setAddressLine1("456 New St");
        companyRequestDTO.setPrimaryAddress(updatedAddressDTO);
        when(companyRepository.findById(anyLong())).thenReturn(Optional.of(company));
        when(companyRepository.existsByName(anyString())).thenReturn(false);
        when(companyRepository.save(any(Company.class))).thenReturn(company);
        when(companyMapper.toDto(any(Company.class))).thenReturn(companyDTO);
        doNothing()
                .when(companyRequestMapper)
                .updateCompanyFromDto(any(CompanyRequestDTO.class), any(Company.class));
        doNothing()
                .when(addressMapper)
                .updateAddressFromDTO(any(AddressRequestDTO.class), any(Address.class));

        CompanyDTO result = companyService.updateCompany(1L, companyRequestDTO);

        assertNotNull(result);
        assertEquals(companyDTO.getId(), result.getId());
        verify(companyRepository).findById(1L);
        verify(companyRepository).existsByName(companyRequestDTO.getName());
        verify(companyRequestMapper).updateCompanyFromDto(companyRequestDTO, company);
        verify(addressMapper)
                .updateAddressFromDTO(any(AddressRequestDTO.class), any(Address.class));
        verify(companyRepository).save(company);
        verify(companyMapper).toDto(company);
    }

    @Test
    void updateCompany_NotFound_ShouldThrowException() {
        when(companyRepository.findById(anyLong())).thenReturn(Optional.empty());

        EntityNotFoundException exception =
                assertThrows(
                        EntityNotFoundException.class,
                        () -> companyService.updateCompany(1L, companyRequestDTO));

        assertEquals("Company not found with id: 1", exception.getMessage());
        verify(companyRepository).findById(1L);
        verifyNoMoreInteractions(companyRepository, companyRequestMapper);
    }

    @Test
    void updateCompany_WithExistingName_ShouldThrowException() {
        companyRequestDTO.setName("Updated Company");
        when(companyRepository.findById(anyLong())).thenReturn(Optional.of(company));
        when(companyRepository.existsByName("updated company")).thenReturn(true);

        CompanyException exception =
                assertThrows(
                        CompanyException.class,
                        () -> companyService.updateCompany(1L, companyRequestDTO));

        assertTrue(
                exception
                        .getMessage()
                        .equalsIgnoreCase("Company with name 'Updated Company' already exists"));
        verify(companyRepository).findById(1L);
        verifyNoInteractions(companyRequestMapper, companyMapper);
    }

    @Test
    void updateCompany_WithIsBillingPrimary_Success() {
        companyRequestDTO.setIsBillingPrimary(true);
        AddressRequestDTO updatedAddressDTO = new AddressRequestDTO();
        updatedAddressDTO.setAddressLine1("456 New St");
        companyRequestDTO.setPrimaryAddress(updatedAddressDTO);
        when(companyRepository.findById(anyLong())).thenReturn(Optional.of(company));
        when(companyRepository.save(any(Company.class))).thenReturn(company);
        when(companyMapper.toDto(any(Company.class))).thenReturn(companyDTO);
        doNothing()
                .when(companyRequestMapper)
                .updateCompanyFromDto(any(CompanyRequestDTO.class), any(Company.class));
        doNothing()
                .when(addressMapper)
                .updateAddressFromDTO(any(AddressRequestDTO.class), any(Address.class));

        CompanyDTO result = companyService.updateCompany(1L, companyRequestDTO);

        assertNotNull(result);
        verify(companyRepository).findById(1L);
        verify(companyRequestMapper).updateCompanyFromDto(companyRequestDTO, company);
        verify(addressMapper)
                .updateAddressFromDTO(any(AddressRequestDTO.class), any(Address.class));
        verify(companyRepository).save(any(Company.class));
    }

    @Test
    void getCompanyById_Success() {
        when(companyRepository.findById(anyLong())).thenReturn(Optional.of(company));
        when(companyMapper.toDto(any(Company.class))).thenReturn(companyDTO);

        CompanyDTO result = companyService.getCompanyById(1L);

        assertNotNull(result);
        assertEquals(companyDTO.getId(), result.getId());
        verify(companyRepository).findById(1L);
        verify(companyMapper).toDto(company);
    }

    @Test
    void getCompanyById_NotFound_ShouldThrowException() {
        when(companyRepository.findById(anyLong())).thenReturn(Optional.empty());

        EntityNotFoundException exception =
                assertThrows(
                        EntityNotFoundException.class, () -> companyService.getCompanyById(1L));

        assertEquals("Company not found with id: 1", exception.getMessage());
        verify(companyRepository).findById(1L);
    }

    @Test
    void getCompanyByName_Success() {
        when(companyRepository.findByName(anyString())).thenReturn(Optional.of(company));
        when(companyMapper.toDto(any(Company.class))).thenReturn(companyDTO);

        CompanyDTO result = companyService.getCompanyByName("Test Company");

        assertNotNull(result);
        assertEquals(companyDTO.getId(), result.getId());
        verify(companyRepository).findByName("Test Company");
        verify(companyMapper).toDto(company);
    }

    @Test
    void getCompanyByName_NotFound_ShouldThrowException() {
        when(companyRepository.findByName(anyString())).thenReturn(Optional.empty());

        EntityNotFoundException exception =
                assertThrows(
                        EntityNotFoundException.class,
                        () -> companyService.getCompanyByName("Test Company"));

        assertEquals("Company not found with name: Test Company", exception.getMessage());
        verify(companyRepository).findByName("Test Company");
    }

    @Test
    void getCompanyByABN_Success() {
        when(companyRepository.findByABN(anyString())).thenReturn(Optional.of(company));
        when(companyMapper.toDto(any(Company.class))).thenReturn(companyDTO);

        CompanyDTO result = companyService.getCompanyByABN("**********");

        assertNotNull(result);
        assertEquals(companyDTO.getId(), result.getId());
        verify(companyRepository).findByABN("**********");
        verify(companyMapper).toDto(company);
    }

    @Test
    void getCompanyByABN_NotFound_ShouldThrowException() {
        when(companyRepository.findByABN(anyString())).thenReturn(Optional.empty());

        EntityNotFoundException exception =
                assertThrows(
                        EntityNotFoundException.class,
                        () -> companyService.getCompanyByABN("**********"));

        assertEquals("Company not found with abn: **********", exception.getMessage());
        verify(companyRepository).findByABN("**********");
    }

    @Test
    void getCompanyByACN_Success() {
        when(companyRepository.findByACN(anyString())).thenReturn(Optional.of(company));
        when(companyMapper.toDto(any(Company.class))).thenReturn(companyDTO);

        CompanyDTO result = companyService.getCompanyByACN("*********");

        assertNotNull(result);
        assertEquals(companyDTO.getId(), result.getId());
        verify(companyRepository).findByACN("*********");
        verify(companyMapper).toDto(company);
    }

    @Test
    void getCompanyByACN_NotFound_ShouldThrowException() {
        when(companyRepository.findByACN(anyString())).thenReturn(Optional.empty());

        EntityNotFoundException exception =
                assertThrows(
                        EntityNotFoundException.class,
                        () -> companyService.getCompanyByACN("*********"));

        assertEquals("Company not found with acn: *********", exception.getMessage());
        verify(companyRepository).findByACN("*********");
    }

    @Test
    void getCompaniesByIndustry_Success() {
        when(companyRepository.findByIndustry(anyString())).thenReturn(List.of(company));
        when(companyMapper.toDto(any(Company.class))).thenReturn(companyDTO);

        List<CompanyDTO> result = companyService.getCompaniesByIndustry("Tech");

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(companyDTO.getId(), result.get(0).getId());
        verify(companyRepository).findByIndustry("Tech");
        verify(companyMapper).toDto(company);
    }

    @Test
    void getCompaniesByEmployeeCountGreaterThan_Success() {
        when(companyRepository.findByEmployeeCountGreaterThan(anyInt()))
                .thenReturn(List.of(company));
        when(companyMapper.toDto(any(Company.class))).thenReturn(companyDTO);

        List<CompanyDTO> result = companyService.getCompaniesByEmployeeCountGreaterThan(50);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(companyDTO.getId(), result.get(0).getId());
        verify(companyRepository).findByEmployeeCountGreaterThan(50);
        verify(companyMapper).toDto(company);
    }

    @Test
    void getCompaniesByEmployeeCountLessThan_Success() {
        when(companyRepository.findByEmployeeCountLessThan(anyInt())).thenReturn(List.of(company));
        when(companyMapper.toDto(any(Company.class))).thenReturn(companyDTO);

        List<CompanyDTO> result = companyService.getCompaniesByEmployeeCountLessThan(150);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(companyDTO.getId(), result.get(0).getId());
        verify(companyRepository).findByEmployeeCountLessThan(150);
        verify(companyMapper).toDto(company);
    }

    @Test
    void getAllCompanies_Success() {
        when(companyRepository.findAll()).thenReturn(List.of(company));
        when(companyMapper.toDto(any(Company.class))).thenReturn(companyDTO);

        List<CompanyDTO> result = companyService.getAllCompanies();

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(companyDTO.getId(), result.get(0).getId());
        verify(companyRepository).findAll();
        verify(companyMapper).toDto(company);
    }

    @Test
    void deleteCompany_Success() {
        when(companyRepository.existsById(anyLong())).thenReturn(true);

        companyService.deleteCompany(1L);

        verify(companyRepository).existsById(1L);
        verify(companyRepository).deleteById(1L);
    }

    @Test
    void deleteCompany_NotFound_ShouldThrowException() {
        when(companyRepository.existsById(anyLong())).thenReturn(false);

        EntityNotFoundException exception =
                assertThrows(EntityNotFoundException.class, () -> companyService.deleteCompany(1L));

        assertEquals("Company not found with id: 1", exception.getMessage());
        verify(companyRepository).existsById(1L);
        verifyNoMoreInteractions(companyRepository);
    }

    @Test
    void getCompaniesWithFilters_Success() {
        Page<Company> companyPage = new PageImpl<>(List.of(company));
        when(companyRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(companyPage)
                .thenReturn(companyPage);
        when(companyMapper.toDto(any(Company.class))).thenReturn(companyDTO);
        when(userRepository
                        .countByIsActiveTrueAndCompanyMemberships_IsActiveTrueAndCompanyMemberships_Company_Id(
                                anyLong()))
                .thenReturn(5L);

        CompanyFilterDTO filterDTO = new CompanyFilterDTO();
        filterDTO.setName("Test");

        Page<CompanyDTO> result =
                companyService.getCompaniesWithFilters(filterDTO, PageRequest.of(0, 10));

        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(companyDTO.getId(), result.getContent().get(0).getId());
        verify(companyRepository, atLeastOnce())
                .findAll(any(Specification.class), any(Pageable.class));
        verify(companyMapper, atLeastOnce()).toDto(company);
    }

    @Test
    void findCompanyById_Success() {
        when(companyRepository.findById(anyLong())).thenReturn(Optional.of(company));
        when(companyMapper.toDto(any(Company.class))).thenReturn(companyDTO);

        Optional<CompanyDTO> result = companyService.findCompanyById(1L);

        assertTrue(result.isPresent());
        assertEquals(companyDTO.getId(), result.get().getId());
        verify(companyRepository).findById(1L);
        verify(companyMapper).toDto(company);
    }

    @Test
    void findCompanyById_NotFound() {
        when(companyRepository.findById(anyLong())).thenReturn(Optional.empty());

        Optional<CompanyDTO> result = companyService.findCompanyById(1L);

        assertFalse(result.isPresent());
        verify(companyRepository).findById(1L);
        verifyNoInteractions(companyMapper);
    }
}
