package com.arealytics.areadocs.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import static com.arealytics.areadocs.constants.SERVURLs.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

public class ServApiServiceTest {

    @Mock private RestTemplate restTemplate;

    @Mock private MockServApiService mockServApiService;

    @Mock private DocumentPricingService documentPricingService;

    @InjectMocks private ServApiService servApiService;

    private final String baseUrl = "http://test-base-url.com";
    private final String clientId = "test-client-id";
    private final String clientSecret = "test-client-secret";
    private final String authToken = "Bearer test-auth-token";
    private final String alertBaseUrl = "http://alert-base-url.com";
    private final String verifyBaseUrl = "http://verify-base-url.com";
    private final String ordersBaseUrl = "http://orders-base-url.com";

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);

        servApiService =
                new ServApiService(
                        restTemplate,
                        baseUrl,
                        clientId,
                        clientSecret,
                        authToken,
                        alertBaseUrl,
                        verifyBaseUrl,
                        ordersBaseUrl,
                        mockServApiService,
                        documentPricingService);
    }

        // --- PROPERTY SEARCH TESTS ---

    @Test
    public void testGetProperties_withParams_callsRestTemplateWithCorrectUrl() {
        String params = "key1=value1&key2=value2";
        Class<String> responseType = String.class;

        String expectedUrl = baseUrl + DISCOVERY_PROPERTIES_SEARCH_API_URL + '?' + params;

        ResponseEntity<String> mockResponse = new ResponseEntity<>("response body", HttpStatus.OK);

        when(restTemplate.exchange(
                        startsWith(expectedUrl),
                        eq(HttpMethod.GET),
                        any(HttpEntity.class),
                        eq(responseType)))
                .thenReturn(mockResponse);

        ResponseEntity<String> response = servApiService.getProperties(params, responseType);

        verify(restTemplate, times(1))
                .exchange(
                        startsWith(expectedUrl),
                        eq(HttpMethod.GET),
                        any(HttpEntity.class),
                        eq(responseType));

        assertEquals(mockResponse, response);
    }

    @Test
    public void testGetProperties_withoutParams_callsRestTemplateWithBaseUrlOnly() {
        String params = "";
        Class<String> responseType = String.class;

        String expectedUrl = baseUrl + DISCOVERY_PROPERTIES_SEARCH_API_URL;

        ResponseEntity<String> mockResponse = new ResponseEntity<>("response body", HttpStatus.OK);

        when(restTemplate.exchange(
                        eq(expectedUrl),
                        eq(HttpMethod.GET),
                        any(HttpEntity.class),
                        eq(responseType)))
                .thenReturn(mockResponse);

        ResponseEntity<String> response = servApiService.getProperties(params, responseType);

        verify(restTemplate, times(1))
                .exchange(
                        eq(expectedUrl),
                        eq(HttpMethod.GET),
                        any(HttpEntity.class),
                        eq(responseType));

        assertEquals(mockResponse, response);
    }

    // --- PARCEL SEARCH TESTS ---

    @Test
    public void testGetParcel_successfulCall_returnsResponseEntity() {
        String parcelIdentifier = "testParcel123";
        Class<String> responseType = String.class;

        String expectedUrl =
                UriComponentsBuilder.fromHttpUrl(baseUrl + DISCOVERY_PARCELS_SEARCH_API_URL)
                        .queryParam("spi", parcelIdentifier)
                        .toUriString();

        ResponseEntity<String> mockResponse = new ResponseEntity<>("parcel data", HttpStatus.OK);

        when(restTemplate.exchange(
                        eq(expectedUrl),
                        eq(HttpMethod.GET),
                        any(HttpEntity.class),
                        eq(responseType)))
                .thenReturn(mockResponse);

        ResponseEntity<String> actualResponse =
                servApiService.getParcel(parcelIdentifier, responseType);

        verify(restTemplate, times(1))
                .exchange(
                        eq(expectedUrl),
                        eq(HttpMethod.GET),
                        any(HttpEntity.class),
                        eq(responseType));

        assertEquals(mockResponse, actualResponse);
    }

    @Test
    public void testGetParcel_apiReturnsNotFound_returnsNotFoundResponseEntity() {
        String parcelIdentifier = "nonExistentParcel";
        Class<String> responseType = String.class;

        String expectedUrl =
                UriComponentsBuilder.fromHttpUrl(baseUrl + DISCOVERY_PARCELS_SEARCH_API_URL)
                        .queryParam("spi", parcelIdentifier)
                        .toUriString();

        ResponseEntity<String> mockNotFoundResponse = new ResponseEntity<>(HttpStatus.NOT_FOUND);

        when(restTemplate.exchange(
                        eq(expectedUrl),
                        eq(HttpMethod.GET),
                        any(HttpEntity.class),
                        eq(responseType)))
                .thenReturn(mockNotFoundResponse);

        ResponseEntity<String> actualResponse =
                servApiService.getParcel(parcelIdentifier, responseType);

        verify(restTemplate, times(1))
                .exchange(
                        eq(expectedUrl),
                        eq(HttpMethod.GET),
                        any(HttpEntity.class),
                        eq(responseType));

        assertEquals(HttpStatus.NOT_FOUND, actualResponse.getStatusCode());
        assertEquals(mockNotFoundResponse, actualResponse);
    }

    @Test
    public void testGetParcel_apiReturnsEmptyBody_returnsOkResponseEntityWithEmptyBody() {
        String parcelIdentifier = "parcelWithNoData";
        Class<String> responseType = String.class;

        String expectedUrl =
                UriComponentsBuilder.fromHttpUrl(baseUrl + DISCOVERY_PARCELS_SEARCH_API_URL)
                        .queryParam("spi", parcelIdentifier)
                        .toUriString();

        ResponseEntity<String> mockEmptyBodyResponse = new ResponseEntity<>("", HttpStatus.OK);

        when(restTemplate.exchange(
                        eq(expectedUrl),
                        eq(HttpMethod.GET),
                        any(HttpEntity.class),
                        eq(responseType)))
                .thenReturn(mockEmptyBodyResponse);

        ResponseEntity<String> actualResponse =
                servApiService.getParcel(parcelIdentifier, responseType);

        verify(restTemplate, times(1))
                .exchange(
                        eq(expectedUrl),
                        eq(HttpMethod.GET),
                        any(HttpEntity.class),
                        eq(responseType));

        assertEquals(HttpStatus.OK, actualResponse.getStatusCode());
        assertTrue(actualResponse.getBody() == null || actualResponse.getBody().isEmpty());
        assertEquals(mockEmptyBodyResponse, actualResponse);
    }

    @Test
    public void testGetParcel_restTemplateThrowsException_propagatesException() {
        String parcelIdentifier = "errorParcel";
        Class<String> responseType = String.class;

        String expectedUrl =
                UriComponentsBuilder.fromHttpUrl(baseUrl + DISCOVERY_PARCELS_SEARCH_API_URL)
                        .queryParam("spi", parcelIdentifier)
                        .toUriString();

        when(restTemplate.exchange(
                        eq(expectedUrl),
                        eq(HttpMethod.GET),
                        any(HttpEntity.class),
                        eq(responseType)))
                .thenThrow(
                        new org.springframework.web.client.HttpClientErrorException(
                                HttpStatus.BAD_REQUEST, "Bad Request from API"));

        org.springframework.web.client.HttpClientErrorException thrownException =
                assertThrows(
                        org.springframework.web.client.HttpClientErrorException.class,
                        () -> {
                            servApiService.getParcel(parcelIdentifier, responseType);
                        });

        assertEquals(HttpStatus.BAD_REQUEST, thrownException.getStatusCode());
        assertEquals("400 Bad Request from API", thrownException.getMessage());

        verify(restTemplate, times(1))
                .exchange(
                        eq(expectedUrl),
                        eq(HttpMethod.GET),
                        any(HttpEntity.class),
                        eq(responseType));
    }
}
