package com.arealytics.areadocs.service;

import com.arealytics.areadocs.domain.Permission;
import com.arealytics.areadocs.dto.requestDTO.PermissionFilterDTO;
import com.arealytics.areadocs.dto.requestDTO.PermissionRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.PermissionDTO;
import com.arealytics.areadocs.exception.PermissionException;
import com.arealytics.areadocs.mapper.PermissionMapper;
import com.arealytics.areadocs.repository.PermissionRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class PermissionServiceTest {

    @InjectMocks
    private PermissionService permissionService;

    @Mock
    private PermissionRepository permissionRepository;

    @Mock
    private PermissionMapper permissionMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCreatePermission_Success() {
        PermissionRequestDTO requestDTO = new PermissionRequestDTO();
        requestDTO.setPermissionCode("CODE");
        requestDTO.setPermissionName("Name");
        Permission entity = new Permission();
        Permission saved = new Permission();
        PermissionDTO dto = new PermissionDTO();

        when(permissionRepository.existsByPermissionCode("CODE")).thenReturn(false);
        when(permissionMapper.toEntity(requestDTO)).thenReturn(entity);
        when(permissionRepository.save(entity)).thenReturn(saved);
        when(permissionMapper.toDto(saved)).thenReturn(dto);

        PermissionDTO result = permissionService.createPermission(requestDTO);

        assertNotNull(result);
        verify(permissionRepository).save(entity);
    }

    @Test
    void testCreatePermission_DuplicateCode() {
        PermissionRequestDTO requestDTO = new PermissionRequestDTO();
        requestDTO.setPermissionCode("CODE");
        requestDTO.setPermissionName("Name");

        when(permissionRepository.existsByPermissionCode("CODE")).thenReturn(true);

        assertThrows(PermissionException.DuplicatePermissionCodeException.class,
                () -> permissionService.createPermission(requestDTO));
    }

    @Test
    void testGetAllPermissions() {
        List<Permission> permissions = List.of(new Permission(), new Permission());
        when(permissionRepository.findAll()).thenReturn(permissions);
        when(permissionMapper.toDto(any())).thenReturn(new PermissionDTO());

        List<PermissionDTO> result = permissionService.getAllPermissions();

        assertEquals(2, result.size());
    }

    @Test
    void testGetPermissionById_Found() {
        Permission permission = new Permission();
        when(permissionRepository.findById(1L)).thenReturn(Optional.of(permission));
        when(permissionMapper.toDto(permission)).thenReturn(new PermissionDTO());

        PermissionDTO result = permissionService.getPermissionById(1L);

        assertNotNull(result);
    }

    @Test
    void testGetPermissionById_NotFound() {
        when(permissionRepository.findById(1L)).thenReturn(Optional.empty());

        assertThrows(PermissionException.PermissionNotFoundException.class,
                () -> permissionService.getPermissionById(1L));
    }

    @Test
    void testGetPermissionByCode_Found() {
        Permission permission = new Permission();
        when(permissionRepository.findByPermissionCode("CODE")).thenReturn(Optional.of(permission));
        when(permissionMapper.toDto(permission)).thenReturn(new PermissionDTO());

        PermissionDTO result = permissionService.getPermissionByCode("CODE");

        assertNotNull(result);
    }

    @Test
    void testGetPermissionByCode_NotFound() {
        when(permissionRepository.findByPermissionCode("CODE")).thenReturn(Optional.empty());

        assertThrows(PermissionException.PermissionNotFoundException.class,
                () -> permissionService.getPermissionByCode("CODE"));
    }

    @Test
    void testUpdatePermission_Success() {
        Permission existing = new Permission();
        existing.setPermissionCode("OLD_CODE");

        PermissionRequestDTO requestDTO = new PermissionRequestDTO();
        requestDTO.setPermissionCode("CODE");
        requestDTO.setPermissionName("Name");
        Permission saved = new Permission();

        when(permissionRepository.findById(1L)).thenReturn(Optional.of(existing));
        when(permissionRepository.existsByPermissionCode("NEW_CODE")).thenReturn(false);
        doNothing().when(permissionMapper).updateEntityFromDto(requestDTO, existing);
        when(permissionRepository.save(existing)).thenReturn(saved);
        when(permissionMapper.toDto(saved)).thenReturn(new PermissionDTO());

        PermissionDTO result = permissionService.updatePermission(1L, requestDTO);

        assertNotNull(result);
    }

    @Test
    void testUpdatePermission_CodeConflict() {
        Long permissionId = 1L;
    
        Permission existingPermission = new Permission();
        existingPermission.setId(permissionId);
        existingPermission.setPermissionCode("EXISTING_CODE");
        existingPermission.setPermissionName("Some Permission");
    
        PermissionRequestDTO requestDTO = new PermissionRequestDTO();
        requestDTO.setPermissionCode("DUPLICATE_CODE"); 
        requestDTO.setPermissionName("Updated Name");
    
        when(permissionRepository.findById(permissionId)).thenReturn(Optional.of(existingPermission));
    
        when(permissionRepository.existsByPermissionCode("DUPLICATE_CODE")).thenReturn(true);
    
        assertThrows(PermissionException.DuplicatePermissionCodeException.class, () -> {
            permissionService.updatePermission(permissionId, requestDTO);
        });
    
        verify(permissionRepository).findById(permissionId);
        verify(permissionRepository).existsByPermissionCode("DUPLICATE_CODE");
        verifyNoMoreInteractions(permissionRepository);
    }
    

    @Test
    void testDeletePermission_Success() {
        when(permissionRepository.existsById(1L)).thenReturn(true);

        permissionService.deletePermission(1L);

        verify(permissionRepository).deleteById(1L);
    }

    @Test
    void testDeletePermission_NotFound() {
        when(permissionRepository.existsById(1L)).thenReturn(false);

        assertThrows(PermissionException.PermissionNotFoundException.class,
                () -> permissionService.deletePermission(1L));
    }

    @Test
    void testGetPermissionsWithFilters_Empty() {
        PermissionFilterDTO filterDTO = new PermissionFilterDTO();
        Page<Permission> emptyPage = new PageImpl<>(Collections.emptyList());
        when(permissionRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(emptyPage);

        Page<PermissionDTO> result = permissionService.getPermissionsWithFilters(filterDTO, PageRequest.of(0, 10));

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verifyNoInteractions(permissionMapper);
    }

    @Test
    void testFindPermissionById_Found() {
        Permission permission = new Permission();
        PermissionDTO dto = new PermissionDTO();

        when(permissionRepository.findById(1L)).thenReturn(Optional.of(permission));
        when(permissionMapper.toDto(permission)).thenReturn(dto);

        Optional<PermissionDTO> result = permissionService.findPermissionById(1L);

        assertTrue(result.isPresent());
        assertEquals(dto, result.get());
    }

    @Test
    void testFindPermissionById_NotFound() {
        when(permissionRepository.findById(1L)).thenReturn(Optional.empty());

        Optional<PermissionDTO> result = permissionService.findPermissionById(1L);

        assertTrue(result.isEmpty());
    }
}
