package com.arealytics.areadocs.service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import com.arealytics.areadocs.domain.Document;
import com.arealytics.areadocs.domain.DocumentCategory;
import com.arealytics.areadocs.domain.DocumentFolder;
import com.arealytics.areadocs.domain.DocumentSource;
import com.arealytics.areadocs.dto.requestDTO.DocumentFilterDTO;
import com.arealytics.areadocs.dto.requestDTO.DocumentRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.DocumentDTO;
import com.arealytics.areadocs.exception.DocumentNotFoundException;
import com.arealytics.areadocs.mapper.DocumentMapper;
import com.arealytics.areadocs.repository.DocumentCategoryRepository;
import com.arealytics.areadocs.repository.DocumentFolderRepository;
import com.arealytics.areadocs.repository.DocumentRepository;
import com.arealytics.areadocs.repository.DocumentSourceRepository;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DocumentServiceTest {

    @Mock private DocumentRepository documentRepository;

    @Mock private DocumentMapper documentMapper;

    @Mock private DocumentCategoryRepository documentCategoryRepository;

    @Mock private DocumentFolderRepository documentFolderRepository;

    @Mock private DocumentSourceRepository documentSourceRepository;

    @InjectMocks private DocumentService documentService;

    private Document document;
    private DocumentDTO documentDTO;
    private DocumentRequestDTO documentRequestDTO; // Add DocumentRequestDTO
    private DocumentCategory category;
    private DocumentFolder folder;
    private DocumentSource source;

    @BeforeEach
    void setUp() {
        document = new Document();
        document.setId(1L);
        document.setTitle("HR Docs");

        documentDTO = new DocumentDTO();
        documentDTO.setId(1L);
        documentDTO.setTitle("HR Docs");
        documentDTO.setCategoryId(1L);
        documentDTO.setFolderId(1L);
        documentDTO.setSourceId(1L);

        documentRequestDTO = new DocumentRequestDTO(); // Initialize DocumentRequestDTO
        documentRequestDTO.setTitle("New HR Policy");
        documentRequestDTO.setCategoryId(1L);
        documentRequestDTO.setFolderId(1L);
        documentRequestDTO.setSourceId(1L);
        documentRequestDTO.setBasePrice(BigDecimal.TEN);
        documentRequestDTO.setMarkupPercentage(BigDecimal.ONE);
        documentRequestDTO.setFinalPrice(BigDecimal.valueOf(11));
        documentRequestDTO.setDocumentType("CERTIFICATE");
        documentRequestDTO.setFilePath("/path/to/hr_policy.pdf");
        // ... set other fields as needed for your tests

        category = new DocumentCategory();
        category.setId(1L);

        folder = new DocumentFolder();
        folder.setId(1L);

        source = new DocumentSource();
        source.setId(1L);
    }

    // Tests for getDocumentById
    @Test
    void getDocumentById_Success() {
        when(documentRepository.findById(1L)).thenReturn(Optional.of(document));
        when(documentMapper.toDTO(document)).thenReturn(documentDTO);

        DocumentDTO result = documentService.getDocumentById(1L);

        assertEquals(documentDTO, result);
        verify(documentRepository, times(1)).findById(1L);
        verify(documentMapper, times(1)).toDTO(document);
    }

    @Test
    void getDocumentById_NotFound() {
        when(documentRepository.findById(999L)).thenReturn(Optional.empty());

        DocumentNotFoundException exception =
                assertThrows(
                        DocumentNotFoundException.class,
                        () -> documentService.getDocumentById(999L));

        assertEquals("Document not found with id: 999", exception.getMessage());
        verify(documentRepository, times(1)).findById(999L);
        verifyNoInteractions(documentMapper);
    }

    // Tests for getAllDocuments
    @Test
    void getAllDocuments_Success() {
        when(documentRepository.findAll()).thenReturn(List.of(document));
        when(documentMapper.toDTO(document)).thenReturn(documentDTO);

        List<DocumentDTO> result = documentService.getAllDocuments();

        assertEquals(1, result.size());
        assertEquals(documentDTO, result.get(0));
        verify(documentRepository, times(1)).findAll();
        verify(documentMapper, times(1)).toDTO(document);
    }

    @Test
    void getAllDocuments_Empty() {
        when(documentRepository.findAll()).thenReturn(Collections.emptyList());

        List<DocumentDTO> result = documentService.getAllDocuments();

        assertTrue(result.isEmpty());
        verify(documentRepository, times(1)).findAll();
        verifyNoInteractions(documentMapper);
    }

    // Tests for createDocument
    @Test
    void createDocument_Success() {
        when(documentCategoryRepository.findById(1L)).thenReturn(Optional.of(category));
        when(documentSourceRepository.findById(1L)).thenReturn(Optional.of(source));
        when(documentFolderRepository.findById(1L)).thenReturn(Optional.of(folder));
        when(documentMapper.toEntity(documentRequestDTO)).thenReturn(document);
        when(documentRepository.save(document)).thenReturn(document);
        when(documentMapper.toDTO(document)).thenReturn(documentDTO);

        DocumentDTO result = documentService.createDocument(documentRequestDTO);

        assertEquals(documentDTO, result);
        verify(documentCategoryRepository, times(1)).findById(1L);
        verify(documentSourceRepository, times(1)).findById(1L);
        verify(documentFolderRepository, times(1)).findById(1L);
        verify(documentMapper, times(1)).toEntity(documentRequestDTO);
        verify(documentRepository, times(1)).save(document);
        verify(documentMapper, times(1)).toDTO(document);

        // Verify the saved document has the correct category, source, and folder
        ArgumentCaptor<Document> documentCaptor = ArgumentCaptor.forClass(Document.class);
        verify(documentRepository).save(documentCaptor.capture());
        Document savedDocument = documentCaptor.getValue();
        assertEquals(category, savedDocument.getCategory());
        assertEquals(source, savedDocument.getSource());
        assertEquals(folder, savedDocument.getFolder());
    }

    @Test
    void createDocument_CategoryNotFound() {
        documentRequestDTO.setCategoryId(999L);
        when(documentCategoryRepository.findById(999L)).thenReturn(Optional.empty());

        DocumentNotFoundException exception =
                assertThrows(
                        DocumentNotFoundException.class,
                        () -> documentService.createDocument(documentRequestDTO));

        assertEquals("DocumentCategory not found", exception.getMessage());
        verify(documentCategoryRepository, times(1)).findById(999L);
        verifyNoInteractions(
                documentSourceRepository,
                documentFolderRepository,
                documentRepository,
                documentMapper);
    }

    @Test
    void createDocument_SourceNotFound() {
        documentRequestDTO.setSourceId(999L);
        when(documentCategoryRepository.findById(1L)).thenReturn(Optional.of(category));
        when(documentSourceRepository.findById(999L)).thenReturn(Optional.empty());

        DocumentNotFoundException exception =
                assertThrows(
                        DocumentNotFoundException.class,
                        () -> documentService.createDocument(documentRequestDTO));

        assertEquals("DocumentSource not found", exception.getMessage());
        verify(documentCategoryRepository, times(1)).findById(1L);
        verify(documentSourceRepository, times(1)).findById(999L);
        verifyNoInteractions(documentFolderRepository, documentRepository, documentMapper);
    }

    @Test
    void createDocument_FolderNotFound() {
        documentRequestDTO.setFolderId(999L);
        when(documentCategoryRepository.findById(1L)).thenReturn(Optional.of(category));
        when(documentSourceRepository.findById(1L)).thenReturn(Optional.of(source));
        when(documentFolderRepository.findById(999L)).thenReturn(Optional.empty());

        DocumentNotFoundException exception =
                assertThrows(
                        DocumentNotFoundException.class,
                        () -> documentService.createDocument(documentRequestDTO));

        assertEquals("DocumentFolder not found", exception.getMessage());
        verify(documentCategoryRepository, times(1)).findById(1L);
        verify(documentSourceRepository, times(1)).findById(1L);
        verify(documentFolderRepository, times(1)).findById(999L);
        verifyNoInteractions(documentRepository, documentMapper);
    }

    // Tests for updateDocument
    @Test
    void updateDocument_Success() {
        when(documentRepository.findById(1L)).thenReturn(Optional.of(document));
        when(documentCategoryRepository.findById(1L)).thenReturn(Optional.of(category));
        when(documentSourceRepository.findById(1L)).thenReturn(Optional.of(source));
        when(documentFolderRepository.findById(1L)).thenReturn(Optional.of(folder));
        when(documentRepository.save(any(Document.class))).thenReturn(document);
        when(documentMapper.toDTO(document)).thenReturn(documentDTO);
        DocumentDTO result = documentService.updateDocument(1L, documentRequestDTO);
        assertEquals(documentDTO, result);
        verify(documentRepository, times(1)).findById(1L);
        verify(documentCategoryRepository, times(1)).findById(1L);
        verify(documentSourceRepository, times(1)).findById(1L);
        verify(documentFolderRepository, times(1)).findById(1L);

        ArgumentCaptor<Document> documentCaptor = ArgumentCaptor.forClass(Document.class);
        verify(documentRepository, times(1)).save(documentCaptor.capture());
        Document savedDocument = documentCaptor.getValue();
        assertEquals(1L, savedDocument.getId());
        assertEquals(category, savedDocument.getCategory());
        assertEquals(source, savedDocument.getSource());
        assertEquals(folder, savedDocument.getFolder());
        assertEquals(documentRequestDTO.getTitle(), savedDocument.getTitle());
        assertEquals(documentRequestDTO.getBasePrice(), savedDocument.getBasePrice());
        assertEquals(documentRequestDTO.getMarkupPercentage(), savedDocument.getMarkupPercentage());
        assertEquals(documentRequestDTO.getFinalPrice(), savedDocument.getFinalPrice());
        assertEquals(
                com.arealytics.areadocs.enumeration.DocumentType.CERTIFICATE,
                savedDocument.getDocumentType());
        assertEquals(documentRequestDTO.getFilePath(), savedDocument.getFilePath());
        verify(documentMapper, times(1)).toDTO(document);
    }

    @Test
    void updateDocument_NotFound() {
        when(documentRepository.findById(999L)).thenReturn(Optional.empty());

        DocumentNotFoundException exception =
                assertThrows(
                        DocumentNotFoundException.class,
                        () -> documentService.updateDocument(999L, documentRequestDTO));

        assertEquals("Document not found with id: 999", exception.getMessage());
        verify(documentRepository, times(1)).findById(999L);
        verifyNoInteractions(
                documentMapper,
                documentCategoryRepository,
                documentSourceRepository,
                documentFolderRepository);
        verify(documentRepository, never()).save(any());
    }

    @Test
    void updateDocument_CategoryNotFound() {
        documentRequestDTO.setCategoryId(999L);
        when(documentRepository.findById(1L)).thenReturn(Optional.of(document));
        when(documentCategoryRepository.findById(999L)).thenReturn(Optional.empty());

        DocumentNotFoundException exception =
                assertThrows(
                        DocumentNotFoundException.class,
                        () -> documentService.updateDocument(1L, documentRequestDTO));

        assertEquals("DocumentCategory not found with id: 999", exception.getMessage());
        verify(documentRepository, times(1)).findById(1L);
        verify(documentCategoryRepository, times(1)).findById(999L);
        verify(documentSourceRepository, never()).findById(any());
        verify(documentFolderRepository, never()).findById(any());
        verify(documentRepository, never()).save(any());
        verifyNoInteractions(documentSourceRepository, documentFolderRepository, documentMapper);
    }

    @Test
    void updateDocument_SourceNotFound() {
        documentRequestDTO.setSourceId(999L);
        when(documentRepository.findById(1L)).thenReturn(Optional.of(document));
        when(documentCategoryRepository.findById(1L)).thenReturn(Optional.of(category));
        when(documentSourceRepository.findById(999L)).thenReturn(Optional.empty());

        DocumentNotFoundException exception =
                assertThrows(
                        DocumentNotFoundException.class,
                        () -> documentService.updateDocument(1L, documentRequestDTO));

        assertEquals("DocumentSource not found with id: 999", exception.getMessage());
        verify(documentRepository, times(1)).findById(1L);
        verify(documentCategoryRepository, times(1)).findById(1L);
        verify(documentSourceRepository, times(1)).findById(999L);
        verifyNoInteractions(documentMapper);
    }

    @Test
    void updateDocument_FolderNotFound() {
        documentRequestDTO.setFolderId(999L);
        when(documentRepository.findById(1L)).thenReturn(Optional.of(document));
        when(documentCategoryRepository.findById(1L)).thenReturn(Optional.of(category));
        when(documentSourceRepository.findById(1L)).thenReturn(Optional.of(source));
        when(documentFolderRepository.findById(999L)).thenReturn(Optional.empty());

        DocumentNotFoundException exception =
                assertThrows(
                        DocumentNotFoundException.class,
                        () -> documentService.updateDocument(1L, documentRequestDTO));

        assertEquals("DocumentFolder not found with id: 999", exception.getMessage());
        verify(documentRepository, times(1)).findById(1L);
        verify(documentCategoryRepository, times(1)).findById(1L);
        verify(documentSourceRepository, times(1)).findById(1L);
        verify(documentFolderRepository, times(1)).findById(999L);
        verifyNoInteractions(documentMapper);
    }

    // Tests for deleteDocument
    @Test
    void deleteDocument_Success() {
        when(documentRepository.existsById(1L)).thenReturn(true);

        documentService.deleteDocument(1L);

        verify(documentRepository, times(1)).existsById(1L);
        verify(documentRepository, times(1)).deleteById(1L);
    }

    @Test
    void deleteDocument_NotFound() {
        when(documentRepository.existsById(999L)).thenReturn(false);

        DocumentNotFoundException exception =
                assertThrows(
                        DocumentNotFoundException.class,
                        () -> documentService.deleteDocument(999L));

        assertEquals("Document not found with id: 999", exception.getMessage());
        verify(documentRepository, times(1)).existsById(999L);
        verify(documentRepository, never()).deleteById(999L);
    }

    // Tests for getDocumentsWithFilters
    @Test
    @SuppressWarnings("unchecked")
    void getDocumentsWithFilters_ByTitle() {
        DocumentFilterDTO filter = new DocumentFilterDTO();
        filter.setTitle("Test");
        Pageable pageable = PageRequest.of(0, 10);
        Page<Document> page = new PageImpl<>(List.of(document));

        when(documentRepository.findAll(any(Specification.class), eq(pageable))).thenReturn(page);
        when(documentMapper.toDTO(document)).thenReturn(documentDTO);

        Page<DocumentDTO> result = documentService.getDocumentsWithFilters(filter, pageable);

        assertEquals(1, result.getContent().size());
        assertEquals(documentDTO, result.getContent().get(0));
        verify(documentRepository, times(1)).findAll(any(Specification.class), eq(pageable));
        verify(documentMapper, times(1)).toDTO(document);
    }

    @Test
    @SuppressWarnings("unchecked")
    void getDocumentsWithFilters_ByCategoryAndPrice() {
        DocumentFilterDTO filter = new DocumentFilterDTO();
        filter.setCategoryId(1L);
        filter.setMinBasePrice(BigDecimal.valueOf(100.0));
        filter.setMaxBasePrice(BigDecimal.valueOf(500.0));
        Pageable pageable = PageRequest.of(0, 10);
        Page<Document> page = new PageImpl<>(List.of(document));

        when(documentRepository.findAll(any(Specification.class), eq(pageable))).thenReturn(page);
        when(documentMapper.toDTO(document)).thenReturn(documentDTO);

        Page<DocumentDTO> result = documentService.getDocumentsWithFilters(filter, pageable);

        assertEquals(1, result.getContent().size());
        assertEquals(documentDTO, result.getContent().get(0));
        verify(documentRepository, times(1)).findAll(any(Specification.class), eq(pageable));
        verify(documentMapper, times(1)).toDTO(document);
    }

    @Test
    @SuppressWarnings("unchecked")
    void getDocumentsWithFilters_NoFilters() {
        Pageable pageable = PageRequest.of(0, 10);
        Page<Document> page = new PageImpl<>(List.of(document));

        when(documentRepository.findAll(any(Specification.class), eq(pageable))).thenReturn(page);
        when(documentMapper.toDTO(document)).thenReturn(documentDTO);

        Page<DocumentDTO> result = documentService.getDocumentsWithFilters(null, pageable);

        assertEquals(1, result.getContent().size());
        assertEquals(documentDTO, result.getContent().get(0));
        verify(documentRepository, times(1)).findAll(any(Specification.class), eq(pageable));
        verify(documentMapper, times(1)).toDTO(document);
    }

    @Test
    @SuppressWarnings("unchecked")
    void getDocumentsWithFilters_EmptyResult() {
        DocumentFilterDTO filter = new DocumentFilterDTO();
        filter.setTitle("NonExistent");
        Pageable pageable = PageRequest.of(0, 10);
        Page<Document> emptyPage = new PageImpl<>(Collections.emptyList());

        when(documentRepository.findAll(any(Specification.class), eq(pageable)))
                .thenReturn(emptyPage);

        Page<DocumentDTO> result = documentService.getDocumentsWithFilters(filter, pageable);

        assertTrue(result.getContent().isEmpty());
        verify(documentRepository, times(1)).findAll(any(Specification.class), eq(pageable));
        verifyNoInteractions(documentMapper);
    }
}
