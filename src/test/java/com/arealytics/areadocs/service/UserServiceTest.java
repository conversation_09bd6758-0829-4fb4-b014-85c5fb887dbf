package com.arealytics.areadocs.service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import com.arealytics.areadocs.domain.Address;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.dto.requestDTO.UserRequestDTO;
import com.arealytics.areadocs.dto.requestDTO.UserSchemaFilterDTO;
import com.arealytics.areadocs.dto.responseDTO.UserResponseDTO;
import com.arealytics.areadocs.enumeration.UserType;
import com.arealytics.areadocs.mapper.AddressMapper;
import com.arealytics.areadocs.mapper.UserMapper;
import com.arealytics.areadocs.repository.CompanyMembershipRepository;
import com.arealytics.areadocs.repository.RoleRepository;
import com.arealytics.areadocs.repository.UserRepository;
import com.arealytics.areadocs.repository.UserRoleRepository;

import jakarta.persistence.EntityNotFoundException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserServiceTest {

    @Mock private UserRepository userRepository;
    @Mock private UserMapper userMapper;
    @Mock private KeycloakUserService keycloakUserService;
    @Mock private AccountService accountService;
    @Mock private AddressMapper addressMapper;
    @Mock private RoleRepository roleRepository;
    @Mock private UserRoleRepository userRoleRepository;
    @Mock private CompanyMembershipRepository companyMembershipRepository;
    @Mock private CompanyMembershipService companyMembershipService;
    @Mock private S3Service s3Service;

    @InjectMocks private UserService userService;

    private User user;
    private UserResponseDTO userDTO;
    private UserRequestDTO userRequestDTO;
    private UserRequestDTO updatedUserRequestDTO;
    private Address primaryAddress;
    private Address billingAddress;
    private String profilePictureUrl;

    @BeforeEach
    void setUp() {
        primaryAddress = new Address();
        primaryAddress.setId(10L);
        primaryAddress.setAddressLine1("123 Main St");
        billingAddress = new Address();
        billingAddress.setId(11L);
        billingAddress.setAddressLine1("456 Oak Ave");
        user = new User();
        user.setId(1L);
        user.setEmail("<EMAIL>");
        user.setFirstName("John");
        user.setLastName("Doe");
        user.setUserType(UserType.INDIVIDUAL);
        user.setPrimaryAddress(primaryAddress);
        user.setBillingAddress(billingAddress);

        userDTO = new UserResponseDTO();
        userDTO.setId(1L);
        userDTO.setEmail("<EMAIL>");
        userDTO.setFirstName("John");
        userDTO.setLastName("Doe");
        userDTO.setUserType(UserType.INDIVIDUAL.name());

        userRequestDTO = new UserRequestDTO();
        userRequestDTO.setEmail("<EMAIL>");
        userRequestDTO.setFirstName("John");
        userRequestDTO.setLastName("Doe");
        userRequestDTO.setUserType(UserType.INDIVIDUAL);
        userRequestDTO.setPassword("password123");
        userRequestDTO.setRoleId(1L);
        updatedUserRequestDTO = new UserRequestDTO();
        updatedUserRequestDTO.setEmail("<EMAIL>");
        updatedUserRequestDTO.setFirstName("John_Updated");
        updatedUserRequestDTO.setLastName("Doe_Updated");
        updatedUserRequestDTO.setUserType(UserType.INDIVIDUAL);
        updatedUserRequestDTO.setPrimaryAddress(
                new com.arealytics.areadocs.dto.requestDTO.AddressRequestDTO());
        updatedUserRequestDTO.getPrimaryAddress().setAddressLine1("789 New St");
        updatedUserRequestDTO.setBillingAddress(
                new com.arealytics.areadocs.dto.requestDTO.AddressRequestDTO());
        updatedUserRequestDTO.getBillingAddress().setAddressLine1("101 Old Rd");
        profilePictureUrl =
                "https://areadocs-test.s3.amazonaws.com/user-profile-images/<EMAIL>";
    }

    @Test
    void createUser_ShouldReturnCreatedUser() {
        // Setup role mocking
        com.arealytics.areadocs.domain.Role role = new com.arealytics.areadocs.domain.Role();
        role.setId(1L);
        role.setName("USER");
        com.arealytics.areadocs.domain.UserRole userRole =
                new com.arealytics.areadocs.domain.UserRole();
        userRole.setId(1L);
        userRole.setUser(user);
        userRole.setRole(role);

        when(userRepository.existsByEmail(anyString())).thenReturn(false);
        when(userMapper.toEntity(any(UserRequestDTO.class))).thenReturn(user);
        user.setPrimaryAddress(primaryAddress);
        user.setBillingAddress(billingAddress);
        when(userRepository.save(any(User.class))).thenReturn(user);
        when(userMapper.toDto(any(User.class))).thenReturn(userDTO);
        when(keycloakUserService.createUser(any(UserRequestDTO.class)))
                .thenReturn("keycloak-user-id");
        // when(addressMapper.toEntity(any())).thenReturn(primaryAddress).thenReturn(billingAddress);
        when(roleRepository.findById(eq(1L))).thenReturn(Optional.of(role));
        when(userRoleRepository.save(any(com.arealytics.areadocs.domain.UserRole.class)))
                .thenReturn(userRole);

        UserResponseDTO result = userService.createUser(userRequestDTO, profilePictureUrl);

        assertNotNull(result);
        assertEquals(userDTO.getId(), result.getId());
        verify(userRepository).existsByEmail(userRequestDTO.getEmail());
        verify(userMapper).toEntity(userRequestDTO);
        verify(userRepository).save(user);
        verify(keycloakUserService).createUser(userRequestDTO);
        verify(accountService).createUserAccount(user);
    }

    @Test
    void createUser_WithCompanyType_ShouldSkipAccountCreation() {
        // Setup role and company mocking
        com.arealytics.areadocs.domain.Role role = new com.arealytics.areadocs.domain.Role();
        role.setId(1L);
        role.setName("COMPANY_ADMIN");
        com.arealytics.areadocs.domain.UserRole userRole =
                new com.arealytics.areadocs.domain.UserRole();
        userRole.setId(1L);
        userRole.setRole(role);

        User companyUser = new User();
        companyUser.setId(2L);
        companyUser.setEmail("<EMAIL>");
        companyUser.setUserType(UserType.COMPANY);
        UserRequestDTO companyRequestDTO = new UserRequestDTO();
        companyRequestDTO.setEmail("<EMAIL>");
        companyRequestDTO.setUserType(UserType.COMPANY);
        companyRequestDTO.setPassword("companypass");
        companyRequestDTO.setCompanyId(1L); // Add required company ID
        companyRequestDTO.setRoleId(1L); // Add required role ID
        UserResponseDTO companyDTO = new UserResponseDTO();
        companyDTO.setId(2L);
        companyDTO.setEmail("<EMAIL>");
        companyDTO.setUserType(UserType.COMPANY.name());

        when(userRepository.existsByEmail(anyString())).thenReturn(false);
        when(userMapper.toEntity(any(UserRequestDTO.class))).thenReturn(companyUser);
        companyUser.setPrimaryAddress(primaryAddress);
        companyUser.setBillingAddress(billingAddress);
        when(userRepository.save(any(User.class))).thenReturn(companyUser);
        when(userMapper.toDto(any(User.class))).thenReturn(companyDTO);
        when(keycloakUserService.createUser(any(UserRequestDTO.class)))
                .thenReturn("keycloak-company-id");
        // when(addressMapper.toEntity(any())).thenReturn(primaryAddress).thenReturn(billingAddress);
        when(roleRepository.findById(anyLong())).thenReturn(Optional.of(role));
        when(userRoleRepository.save(any(com.arealytics.areadocs.domain.UserRole.class)))
                .thenReturn(userRole);
        when(companyMembershipRepository.existsByCompanyIdAndRoleNameAndIsActiveTrue(
                        anyLong(), anyString()))
                .thenReturn(false);
        when(companyMembershipService.createCompanyMembership(
                        any(
                                com.arealytics
                                        .areadocs
                                        .dto
                                        .requestDTO
                                        .CompanyMembershipRequestDTO
                                        .class)))
                .thenReturn(null);

        UserResponseDTO result = userService.createUser(companyRequestDTO, profilePictureUrl);

        assertNotNull(result);
        assertEquals(companyDTO.getId(), result.getId());
        verify(userRepository).existsByEmail(companyRequestDTO.getEmail());
        verify(userMapper).toEntity(companyRequestDTO);
        verify(userRepository).save(companyUser);
        verify(keycloakUserService).createUser(companyRequestDTO);
        verify(accountService, never()).createUserAccount(any(User.class));
        // verify(addressMapper, atLeastOnce()).toEntity(any());
    }

    @Test
    void createUser_WithExistingEmail_ShouldThrowException() {
        when(userRepository.existsByEmail(anyString())).thenReturn(true);

        IllegalArgumentException exception =
                assertThrows(
                        IllegalArgumentException.class,
                        () -> userService.createUser(userRequestDTO, null));

        assertEquals("Email already exists: " + userRequestDTO.getEmail(), exception.getMessage());
        verify(userRepository).existsByEmail(userRequestDTO.getEmail());
        verify(userMapper, never()).toEntity(any(UserRequestDTO.class));
    }

    @Test
    void createUser_WhenKeycloakFails_ShouldThrowException() {
        when(userRepository.existsByEmail(anyString())).thenReturn(false);
        when(userMapper.toEntity(any(UserRequestDTO.class))).thenReturn(user);
        when(keycloakUserService.createUser(any(UserRequestDTO.class)))
                .thenThrow(new RuntimeException("Keycloak error"));
        // when(addressMapper.toEntity(any())).thenReturn(primaryAddress);

        RuntimeException exception =
                assertThrows(
                        RuntimeException.class, () -> userService.createUser(userRequestDTO, null));

        assertTrue(exception.getMessage().contains("User creation failed: Keycloak error"));
        verify(userRepository).existsByEmail(userRequestDTO.getEmail());
        verify(userMapper).toEntity(userRequestDTO);
        verify(userRepository, never()).save(any(User.class));
        verify(keycloakUserService).createUser(userRequestDTO);
    }

    @Test
    void getAllUsers_ShouldReturnAllUsers() {
        Page<User> userPage = new PageImpl<>(List.of(user));
        when(userRepository.findAll(any(Pageable.class))).thenReturn(userPage);
        when(userMapper.toDto(any(User.class))).thenReturn(userDTO);
        when(companyMembershipService.getCompanyMembershipsByUserIds(anyList()))
                .thenReturn(Map.of());

        Pageable pageable = PageRequest.of(0, 10);
        Page<UserResponseDTO> result = userService.getAllUsers(pageable);

        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        verify(userRepository).findAll(pageable);
        verify(userMapper).toDto(user);
    }

    @Test
    void getUserById_ShouldReturnUser() {
        when(userRepository.findById(anyLong())).thenReturn(Optional.of(user));
        when(userMapper.toDto(any(User.class))).thenReturn(userDTO);

        UserResponseDTO result = userService.getUserById(1L);

        assertNotNull(result);
        assertEquals(userDTO.getId(), result.getId());
        verify(userRepository).findById(1L);
        verify(userMapper).toDto(user);
    }

    @Test
    void getUserById_WhenUserNotFound_ShouldThrowException() {
        when(userRepository.findById(anyLong())).thenReturn(Optional.empty());

        EntityNotFoundException exception =
                assertThrows(EntityNotFoundException.class, () -> userService.getUserById(999L));

        assertEquals("User not found with id: 999", exception.getMessage());
        verify(userRepository).findById(999L);
    }

    @Test
    void getUserByEmail_ShouldReturnUser() {
        when(userRepository.findByEmail(anyString())).thenReturn(Optional.of(user));
        when(userMapper.toDto(any(User.class))).thenReturn(userDTO);

        UserResponseDTO result = userService.getUserByEmail("<EMAIL>");

        assertNotNull(result);
        assertEquals(userDTO.getId(), result.getId());
        verify(userRepository).findByEmail("<EMAIL>");
        verify(userMapper).toDto(user);
    }

    @Test
    void getUserByEmail_WhenUserNotFound_ShouldThrowException() {
        when(userRepository.findByEmail(anyString())).thenReturn(Optional.empty());

        EntityNotFoundException exception =
                assertThrows(
                        EntityNotFoundException.class,
                        () -> userService.getUserByEmail("<EMAIL>"));

        assertEquals("User not found with email: <EMAIL>", exception.getMessage());
        verify(userRepository).findByEmail("<EMAIL>");
    }

    @Test
    void updateUser_ShouldReturnUpdatedUser() {
        User existingUser = new User();
        existingUser.setId(1L);
        existingUser.setEmail("<EMAIL>");
        existingUser.setFirstName("Old");
        existingUser.setLastName("User");
        existingUser.setPrimaryAddress(primaryAddress);
        existingUser.setBillingAddress(billingAddress);
        UserResponseDTO expectedUpdatedUserDTO = new UserResponseDTO();
        expectedUpdatedUserDTO.setId(1L);
        expectedUpdatedUserDTO.setEmail(updatedUserRequestDTO.getEmail());
        expectedUpdatedUserDTO.setFirstName(updatedUserRequestDTO.getFirstName());
        expectedUpdatedUserDTO.setLastName(updatedUserRequestDTO.getLastName());

        when(userRepository.findById(anyLong())).thenReturn(Optional.of(existingUser));
        when(userRepository.existsByEmail(updatedUserRequestDTO.getEmail())).thenReturn(false);
        // Mock the updateAddressFromDTO method if it returns something.
        // If it's a void method, use doNothing().
        doNothing()
                .when(addressMapper)
                .updateAddressFromDTO(
                        eq(updatedUserRequestDTO.getPrimaryAddress()), any(Address.class));
        doNothing()
                .when(addressMapper)
                .updateAddressFromDTO(
                        eq(updatedUserRequestDTO.getBillingAddress()), any(Address.class));

        when(userRepository.save(any(User.class))).thenReturn(existingUser);
        when(userMapper.toDto(any(User.class))).thenReturn(expectedUpdatedUserDTO);

        UserResponseDTO result =
                userService.updateUser(1L, updatedUserRequestDTO, profilePictureUrl);

        assertNotNull(result);
        assertEquals(expectedUpdatedUserDTO.getId(), result.getId());
        assertEquals(expectedUpdatedUserDTO.getEmail(), result.getEmail());
        assertEquals(expectedUpdatedUserDTO.getFirstName(), result.getFirstName());
        assertEquals(expectedUpdatedUserDTO.getLastName(), result.getLastName());

        verify(userRepository).findById(1L);
        verify(userRepository).existsByEmail(updatedUserRequestDTO.getEmail());
        // Corrected verify statements for updateAddressFromDTO
        verify(addressMapper)
                .updateAddressFromDTO(
                        eq(updatedUserRequestDTO.getPrimaryAddress()), any(Address.class));
        verify(addressMapper)
                .updateAddressFromDTO(
                        eq(updatedUserRequestDTO.getBillingAddress()), any(Address.class));
        verify(userRepository).save(existingUser);
        verify(userMapper).toDto(existingUser);
    }

    @Test
    void updateUser_WithExistingEmail_ShouldThrowException() {
        User existingUser = new User();
        existingUser.setId(1L);
        existingUser.setEmail("<EMAIL>");

        UserRequestDTO updateRequestWithExistingEmail = new UserRequestDTO();
        updateRequestWithExistingEmail.setEmail("<EMAIL>");

        when(userRepository.findById(anyLong())).thenReturn(Optional.of(existingUser));
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(true);

        IllegalArgumentException exception =
                assertThrows(
                        IllegalArgumentException.class,
                        () -> userService.updateUser(1L, updateRequestWithExistingEmail, null));

        assertEquals("Email already exists: <EMAIL>", exception.getMessage());
        verify(userRepository).findById(1L);
        verify(userRepository).existsByEmail("<EMAIL>");
        verify(userRepository, never()).save(any(User.class));
        verify(addressMapper, never()).toEntity(any());
        // No need to verify updateAddressFromDTO as the service method should throw an
        // exception
        // before calling it
        verify(addressMapper, never()).updateAddressFromDTO(any(), any());
    }

    @Test
    void updateUser_WithSameEmail_ShouldUpdateUser() {
        User existingUser = new User();
        existingUser.setId(1L);
        existingUser.setEmail("<EMAIL>");
        existingUser.setFirstName("Original");
        existingUser.setLastName("User");
        existingUser.setPrimaryAddress(primaryAddress);
        existingUser.setBillingAddress(billingAddress);

        UserRequestDTO updateRequestWithSameEmail = new UserRequestDTO();
        updateRequestWithSameEmail.setEmail("<EMAIL>");
        updateRequestWithSameEmail.setFirstName("Updated");
        updateRequestWithSameEmail.setLastName("User");
        updateRequestWithSameEmail.setPrimaryAddress(
                new com.arealytics.areadocs.dto.requestDTO.AddressRequestDTO());
        updateRequestWithSameEmail.getPrimaryAddress().setAddressLine1("Same Address Line 1");
        updateRequestWithSameEmail.setBillingAddress(
                new com.arealytics.areadocs.dto.requestDTO.AddressRequestDTO());
        updateRequestWithSameEmail.getBillingAddress().setAddressLine1("Same Billing Line 1");

        UserResponseDTO expectedResponseDTO = new UserResponseDTO();
        expectedResponseDTO.setId(1L);
        expectedResponseDTO.setEmail("<EMAIL>");
        expectedResponseDTO.setFirstName("Updated");
        expectedResponseDTO.setLastName("User");

        when(userRepository.findById(anyLong())).thenReturn(Optional.of(existingUser));
        // Mock the updateAddressFromDTO method if it returns something.
        // If it's a void method, use doNothing().
        doNothing()
                .when(addressMapper)
                .updateAddressFromDTO(
                        eq(updateRequestWithSameEmail.getPrimaryAddress()), any(Address.class));
        doNothing()
                .when(addressMapper)
                .updateAddressFromDTO(
                        eq(updateRequestWithSameEmail.getBillingAddress()), any(Address.class));

        when(userRepository.save(any(User.class))).thenReturn(existingUser);
        when(userMapper.toDto(any(User.class))).thenReturn(expectedResponseDTO);

        UserResponseDTO result =
                userService.updateUser(1L, updateRequestWithSameEmail, profilePictureUrl);

        assertNotNull(result);
        assertEquals(expectedResponseDTO.getId(), result.getId());
        assertEquals(expectedResponseDTO.getEmail(), result.getEmail());
        assertEquals(expectedResponseDTO.getFirstName(), result.getFirstName());
        verify(userRepository).findById(1L);
        // Corrected verify statements for updateAddressFromDTO
        verify(addressMapper)
                .updateAddressFromDTO(
                        eq(updateRequestWithSameEmail.getPrimaryAddress()), any(Address.class));
        verify(addressMapper)
                .updateAddressFromDTO(
                        eq(updateRequestWithSameEmail.getBillingAddress()), any(Address.class));
        verify(userRepository).save(existingUser);
        verify(userMapper).toDto(existingUser);
    }

    @Test
    void updateUser_WhenUserNotFound_ShouldThrowException() {
        when(userRepository.findById(anyLong())).thenReturn(Optional.empty());

        EntityNotFoundException exception =
                assertThrows(
                        EntityNotFoundException.class,
                        () -> userService.updateUser(999L, updatedUserRequestDTO, null));

        assertEquals("User not found with id: 999", exception.getMessage());
        verify(userRepository).findById(999L);
        verify(userRepository, never()).save(any(User.class));
        verify(addressMapper, never()).toEntity(any());
        // No need to verify updateAddressFromDTO as the service method should throw an
        // exception
        // before calling it
        verify(addressMapper, never()).updateAddressFromDTO(any(), any());
    }

    @Test
    void deleteUser_ShouldDeleteUser() {
        when(userRepository.findById(anyLong())).thenReturn(Optional.of(user));
        doNothing().when(keycloakUserService).deleteUser(any(User.class));

        userService.deleteUser(1L);

        verify(userRepository).findById(1L);
        verify(keycloakUserService).deleteUser(user);
        verify(userRepository).save(user);
    }

    @Test
    void deleteUser_WhenUserNotFound_ShouldThrowException() {
        when(userRepository.findById(anyLong())).thenReturn(Optional.empty());

        EntityNotFoundException exception =
                assertThrows(EntityNotFoundException.class, () -> userService.deleteUser(999L));

        assertEquals("User not found with id: 999", exception.getMessage());
        verify(userRepository).findById(999L);
    }

    @Test
    void getUsers_ShouldReturnFilteredUsers() {
        Page<User> userPage = new PageImpl<>(List.of(user));
        when(userRepository.findAll(any(Pageable.class))).thenReturn(userPage);
        when(userMapper.toDto(any(User.class))).thenReturn(userDTO);
        when(companyMembershipService.getCompanyMembershipsByUserIds(anyList()))
                .thenReturn(Map.of());

        UserSchemaFilterDTO filterDTO = new UserSchemaFilterDTO();
        Pageable pageable = PageRequest.of(0, 10);
        Page<UserResponseDTO> result = userService.getUsers(filterDTO, pageable);

        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        verify(userRepository).findAll(pageable);
        verify(userMapper).toDto(user);
    }

    @Test
    void getUsers_WithUserType_ShouldReturnFilteredUsers() {
        Page<User> userPage = new PageImpl<>(List.of(user));
        when(userRepository.findByUserType(any(UserType.class), any(Pageable.class)))
                .thenReturn(userPage);
        when(userMapper.toDto(any(User.class))).thenReturn(userDTO);
        when(companyMembershipService.getCompanyMembershipsByUserIds(anyList()))
                .thenReturn(Map.of());

        UserSchemaFilterDTO filterDTO = new UserSchemaFilterDTO();
        filterDTO.setUserType(UserType.INDIVIDUAL);
        Pageable pageable = PageRequest.of(0, 10);
        Page<UserResponseDTO> result = userService.getUsers(filterDTO, pageable);

        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        verify(userRepository).findByUserType(UserType.INDIVIDUAL, pageable);
        verify(userMapper).toDto(user);
    }
}
