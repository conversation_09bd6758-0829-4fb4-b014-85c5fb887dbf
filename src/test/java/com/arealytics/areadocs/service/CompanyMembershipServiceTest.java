package com.arealytics.areadocs.service;

import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.arealytics.areadocs.domain.Company;
import com.arealytics.areadocs.domain.CompanyMembership;
import com.arealytics.areadocs.domain.Role;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.dto.requestDTO.CompanyMembershipRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.CompanyMembershipDTO;
import com.arealytics.areadocs.exception.CompanyException;
import com.arealytics.areadocs.mapper.CompanyMembershipMapper;
import com.arealytics.areadocs.repository.CompanyMembershipRepository;
import com.arealytics.areadocs.repository.CompanyRepository;
import com.arealytics.areadocs.repository.RoleRepository;
import com.arealytics.areadocs.repository.UserRepository;

import jakarta.persistence.EntityNotFoundException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CompanyMembershipServiceTest {

    @Mock private CompanyMembershipRepository companyMembershipRepository;
    @Mock private UserRepository userRepository;
    @Mock private CompanyRepository companyRepository;
    @Mock private RoleRepository roleRepository;
    @Mock private CompanyMembershipMapper companyMembershipMapper;

    @InjectMocks private CompanyMembershipService companyMembershipService;

    private CompanyMembership companyMembership;
    private CompanyMembershipDTO companyMembershipDTO;
    private CompanyMembershipRequestDTO companyMembershipRequestDTO;
    private User user;
    private Company company;
    private Role role;

    @BeforeEach
    void setUp() {
        user = new User();
        user.setId(1L);

        company = new Company();
        company.setId(1L);

        role = new Role();
        role.setId(1L);
        role.setName("SDE");

        companyMembership = new CompanyMembership();
        companyMembership.setId(1L);
        companyMembership.setUser(user);
        companyMembership.setCompany(company);
        companyMembership.setRole(role);
        companyMembership.setStatus("ACTIVE");

        companyMembershipDTO = new CompanyMembershipDTO();
        companyMembershipDTO.setId(1L);
        companyMembershipDTO.setUserId(1L);
        companyMembershipDTO.setCompanyId(1L);
        companyMembershipDTO.setRoleId(1L);

        companyMembershipRequestDTO = new CompanyMembershipRequestDTO();
        companyMembershipRequestDTO.setUserId(1L);
        companyMembershipRequestDTO.setCompanyId(1L);
        companyMembershipRequestDTO.setRoleId(1L);
    }

    @Test
    void testCreateCompanyMembership_Success() {
        when(userRepository.findById(anyLong())).thenReturn(Optional.of(user));
        when(companyRepository.findById(anyLong())).thenReturn(Optional.of(company));
        when(roleRepository.findById(anyLong())).thenReturn(Optional.of(role));
        when(companyMembershipRepository.findByUserIdAndCompanyId(anyLong(), anyLong()))
                .thenReturn(Optional.empty());
        when(companyMembershipMapper.toEntity(any(CompanyMembershipRequestDTO.class)))
                .thenReturn(companyMembership);
        when(companyMembershipRepository.save(any(CompanyMembership.class)))
                .thenReturn(companyMembership);
        when(companyMembershipMapper.toDto(any(CompanyMembership.class)))
                .thenReturn(companyMembershipDTO);

        CompanyMembershipDTO result =
                companyMembershipService.createCompanyMembership(companyMembershipRequestDTO);

        assertNotNull(result);
        assertEquals(companyMembershipDTO, result);
        verify(userRepository, times(1)).findById(anyLong());
        verify(companyRepository, times(1)).findById(anyLong());
        verify(companyMembershipRepository, times(1))
                .findByUserIdAndCompanyId(anyLong(), anyLong());
        verify(companyMembershipRepository, times(1)).save(any(CompanyMembership.class));
    }

    @Test
    void testCreateCompanyMembership_UserNotFound() {
        when(userRepository.findById(anyLong())).thenReturn(Optional.empty());

        EntityNotFoundException exception =
                assertThrows(
                        EntityNotFoundException.class,
                        () ->
                                companyMembershipService.createCompanyMembership(
                                        companyMembershipRequestDTO));

        assertEquals("User not found with id: 1", exception.getMessage());
        verify(userRepository, times(1)).findById(anyLong());
        verifyNoInteractions(
                companyRepository, companyMembershipRepository, companyMembershipMapper);
    }

    @Test
    void testCreateCompanyMembership_CompanyNotFound() {
        when(userRepository.findById(anyLong())).thenReturn(Optional.of(user));
        when(companyRepository.findById(anyLong())).thenReturn(Optional.empty());

        EntityNotFoundException exception =
                assertThrows(
                        EntityNotFoundException.class,
                        () ->
                                companyMembershipService.createCompanyMembership(
                                        companyMembershipRequestDTO));

        assertEquals("Company not found with id: 1", exception.getMessage());
        verify(userRepository, times(1)).findById(anyLong());
        verify(companyRepository, times(1)).findById(anyLong());
        verifyNoInteractions(companyMembershipRepository, companyMembershipMapper);
    }

    @Test
    void testCreateCompanyMembership_AlreadyExists() {
        when(userRepository.findById(anyLong())).thenReturn(Optional.of(user));
        when(companyRepository.findById(anyLong())).thenReturn(Optional.of(company));
        when(companyMembershipRepository.findByUserIdAndCompanyId(anyLong(), anyLong()))
                .thenReturn(Optional.of(companyMembership));

        CompanyException exception =
                assertThrows(
                        CompanyException.class,
                        () ->
                                companyMembershipService.createCompanyMembership(
                                        companyMembershipRequestDTO));

        assertEquals("User is already a member of this company", exception.getMessage());
        verify(userRepository, times(1)).findById(anyLong());
        verify(companyRepository, times(1)).findById(anyLong());
        verify(companyMembershipRepository, times(1))
                .findByUserIdAndCompanyId(anyLong(), anyLong());
        verifyNoInteractions(companyMembershipMapper);
    }

    @Test
    void testGetCompanyMembershipById_Success() {
        when(companyMembershipRepository.findById(anyLong()))
                .thenReturn(Optional.of(companyMembership));
        when(companyMembershipMapper.toDto(any(CompanyMembership.class)))
                .thenReturn(companyMembershipDTO);

        CompanyMembershipDTO result = companyMembershipService.getCompanyMembershipById(1L);

        assertNotNull(result);
        assertEquals(companyMembershipDTO, result);
        verify(companyMembershipRepository, times(1)).findById(anyLong());
        verify(companyMembershipMapper, times(1)).toDto(any(CompanyMembership.class));
    }

    @Test
    void testGetCompanyMembershipById_NotFound() {
        when(companyMembershipRepository.findById(anyLong())).thenReturn(Optional.empty());

        EntityNotFoundException exception =
                assertThrows(
                        EntityNotFoundException.class,
                        () -> companyMembershipService.getCompanyMembershipById(1L));

        assertEquals("Company membership not found with id: 1", exception.getMessage());
        verify(companyMembershipRepository, times(1)).findById(anyLong());
        verifyNoInteractions(companyMembershipMapper);
    }

    @Test
    void testUpdateCompanyMembership_Success() {
        when(companyMembershipRepository.findById(anyLong()))
                .thenReturn(Optional.of(companyMembership));
        when(roleRepository.findById(anyLong())).thenReturn(Optional.of(role));
        when(companyMembershipRepository.save(any(CompanyMembership.class)))
                .thenReturn(companyMembership);
        when(companyMembershipMapper.toDto(any(CompanyMembership.class)))
                .thenReturn(companyMembershipDTO);

        CompanyMembershipDTO result =
                companyMembershipService.updateCompanyMembership(1L, companyMembershipRequestDTO);

        assertNotNull(result);
        assertEquals(companyMembershipDTO, result);
        verify(companyMembershipRepository, times(1)).findById(anyLong());
        verify(roleRepository, times(1)).findById(anyLong());
        verify(companyMembershipRepository, times(1)).save(any(CompanyMembership.class));
        verify(companyMembershipMapper, times(1)).toDto(any(CompanyMembership.class));
    }

    @Test
    void testUpdateCompanyMembership_NotFound() {
        when(companyMembershipRepository.findById(anyLong())).thenReturn(Optional.empty());

        EntityNotFoundException exception =
                assertThrows(
                        EntityNotFoundException.class,
                        () ->
                                companyMembershipService.updateCompanyMembership(
                                        1L, companyMembershipRequestDTO));

        assertEquals("Company membership not found with id: 1", exception.getMessage());
        verify(companyMembershipRepository, times(1)).findById(anyLong());
        verifyNoMoreInteractions(companyMembershipRepository);
        verifyNoInteractions(companyMembershipMapper);
    }

    @Test
    void testDeleteCompanyMembership_Success() {
        when(companyMembershipRepository.existsById(anyLong())).thenReturn(true);

        companyMembershipService.deleteCompanyMembership(1L);

        verify(companyMembershipRepository, times(1)).existsById(anyLong());
        verify(companyMembershipRepository, times(1)).deleteById(anyLong());
    }

    @Test
    void testDeleteCompanyMembership_NotFound() {
        when(companyMembershipRepository.existsById(anyLong())).thenReturn(false);

        EntityNotFoundException exception =
                assertThrows(
                        EntityNotFoundException.class,
                        () -> companyMembershipService.deleteCompanyMembership(1L));

        assertEquals("Company membership not found with id: 1", exception.getMessage());
        verify(companyMembershipRepository, times(1)).existsById(anyLong());
        verifyNoMoreInteractions(companyMembershipRepository);
    }
}
