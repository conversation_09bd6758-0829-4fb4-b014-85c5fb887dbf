package com.arealytics.areadocs.service;

import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import com.arealytics.areadocs.domain.DocumentFolder;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.dto.requestDTO.DocumentFolderRequestDTO;
import com.arealytics.areadocs.dto.requestDTO.FolderFilterDTO;
import com.arealytics.areadocs.dto.responseDTO.DocumentFolderDTO;
import com.arealytics.areadocs.exception.DocumentFolderNotFoundException;
import com.arealytics.areadocs.mapper.DocumentFolderMapper;
import com.arealytics.areadocs.repository.DocumentFolderRepository;
import com.arealytics.areadocs.repository.UserRepository;

import jakarta.persistence.EntityNotFoundException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DocumentFolderServiceTest {

    @Mock private DocumentFolderRepository documentFolderRepository;

    @Mock private UserRepository userRepository;

    @Mock private DocumentFolderMapper documentFolderMapper;

    @InjectMocks private DocumentFolderService documentFolderService;

    private DocumentFolderDTO documentFolderDTO;
    private DocumentFolder documentFolder;
    private User user;
    private FolderFilterDTO filterDTO;

    @BeforeEach
    void setUp() {
        documentFolderDTO = new DocumentFolderDTO();
        documentFolderDTO.setId(1L);
        documentFolderDTO.setName("HR Folder");
        documentFolderDTO.setUserId(1); // Changed to Long
        documentFolderDTO.setParentFolderId(null);

        documentFolder = new DocumentFolder();
        documentFolder.setId(1L);
        documentFolder.setName("HR Folder");
        documentFolder.setCreatedAt(Instant.now());

        user = new User();
        user.setId(1L);

        filterDTO = new FolderFilterDTO();
    }

    @Test
    void testCreateDocumentFolder_Success() {
        DocumentFolderRequestDTO requestDTO = new DocumentFolderRequestDTO();
        requestDTO.setUserId(1); // Use Long
        requestDTO.setName("HR Folder");
        requestDTO.setParentFolderId(null);

        when(userRepository.findById(1L)).thenReturn(Optional.of(user));
        when(documentFolderMapper.toEntity(any(DocumentFolderRequestDTO.class)))
                .thenReturn(documentFolder);
        when(documentFolderRepository.save(any(DocumentFolder.class))).thenReturn(documentFolder);
        when(documentFolderMapper.toDto(any(DocumentFolder.class))).thenReturn(documentFolderDTO);

        DocumentFolderDTO result =
                documentFolderService.createDocumentFolder(requestDTO); // Use requestDTO

        assertNotNull(result);
        assertEquals(documentFolderDTO, result);
        verify(userRepository, times(1)).findById(1L);
        verify(documentFolderRepository, times(1)).save(any(DocumentFolder.class));
        verify(documentFolderMapper, times(1)).toEntity(requestDTO); // Verify with requestDTO
        verify(documentFolderMapper, times(1)).toDto(any(DocumentFolder.class));
    }

    @Test
    void testCreateDocumentFolder_WithParent_Success() {
        DocumentFolderRequestDTO requestDTO = new DocumentFolderRequestDTO();
        requestDTO.setUserId(1); // Use Long
        requestDTO.setName("HR Folder");
        requestDTO.setParentFolderId(2L);

        DocumentFolder parentFolder = new DocumentFolder();
        parentFolder.setId(2L);

        when(userRepository.findById(1L)).thenReturn(Optional.of(user));
        when(documentFolderMapper.toEntity(any(DocumentFolderRequestDTO.class)))
                .thenReturn(documentFolder);
        when(documentFolderRepository.findById(2L)).thenReturn(Optional.of(parentFolder));
        when(documentFolderRepository.save(any(DocumentFolder.class))).thenReturn(documentFolder);
        when(documentFolderMapper.toDto(any(DocumentFolder.class))).thenReturn(documentFolderDTO);

        DocumentFolderDTO result =
                documentFolderService.createDocumentFolder(requestDTO); // Use requestDTO

        assertNotNull(result);
        assertEquals(documentFolderDTO, result);
        verify(userRepository, times(1)).findById(1L);
        verify(documentFolderRepository, times(1)).findById(2L);
        verify(documentFolderRepository, times(1)).save(any(DocumentFolder.class));
        verify(documentFolderMapper, times(1)).toEntity(requestDTO); // Verify with requestDTO
        verify(documentFolderMapper, times(1)).toDto(any(DocumentFolder.class));
    }

    @Test
    void testCreateDocumentFolder_UserNotFound() {
        DocumentFolderRequestDTO requestDTO = new DocumentFolderRequestDTO();
        requestDTO.setUserId(1);
        requestDTO.setName("HR Folder");
        requestDTO.setParentFolderId(null);

        when(userRepository.findById(1L)).thenReturn(Optional.empty());
        when(documentFolderMapper.toEntity(any(DocumentFolderRequestDTO.class)))
                .thenReturn(documentFolder); // Use DocumentFolderRequestDTO

        EntityNotFoundException exception =
                assertThrows(
                        EntityNotFoundException.class,
                        () -> documentFolderService.createDocumentFolder(requestDTO));

        assertEquals("User not found with id: 1", exception.getMessage());
        verify(userRepository, times(1)).findById(1L);
        verify(documentFolderMapper, times(1)).toEntity(requestDTO); // Verify with requestDTO
        verifyNoInteractions(documentFolderRepository);
    }

    @Test
    void testCreateDocumentFolder_ParentNotFound() {
        DocumentFolderRequestDTO requestDTO = new DocumentFolderRequestDTO();
        requestDTO.setUserId(1);
        requestDTO.setName("HR Folder");
        requestDTO.setParentFolderId(2L);

        when(userRepository.findById(1L)).thenReturn(Optional.of(user));
        when(documentFolderMapper.toEntity(any(DocumentFolderRequestDTO.class)))
                .thenReturn(documentFolder); // Use DocumentFolderRequestDTO
        when(documentFolderRepository.findById(2L)).thenReturn(Optional.empty());

        DocumentFolderNotFoundException exception =
                assertThrows(
                        DocumentFolderNotFoundException.class,
                        () -> documentFolderService.createDocumentFolder(requestDTO));

        assertEquals("Parent folder not found", exception.getMessage());
        verify(userRepository, times(1)).findById(1L);
        verify(documentFolderRepository, times(1)).findById(2L);
        verify(documentFolderMapper, times(1)).toEntity(requestDTO); // Verify with requestDTO
        verifyNoMoreInteractions(documentFolderRepository);
    }

    @Test
    void testGetAllDocumentFolders_Success() {
        List<DocumentFolder> folders = Collections.singletonList(documentFolder);
        when(documentFolderRepository.findAll()).thenReturn(folders);
        when(documentFolderMapper.toDto(any(DocumentFolder.class))).thenReturn(documentFolderDTO);

        List<DocumentFolderDTO> result = documentFolderService.getAllDocumentFolders();

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(documentFolderDTO, result.get(0));
        verify(documentFolderRepository, times(1)).findAll();
        verify(documentFolderMapper, times(1)).toDto(any(DocumentFolder.class));
    }

    @Test
    void testGetAllDocumentFolders_Empty() {
        when(documentFolderRepository.findAll()).thenReturn(Collections.emptyList());

        List<DocumentFolderDTO> result = documentFolderService.getAllDocumentFolders();

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(documentFolderRepository, times(1)).findAll();
        verifyNoInteractions(documentFolderMapper);
    }

    @Test
    void testGetDocumentFolderById_Success() {
        when(documentFolderRepository.findById(1L)).thenReturn(Optional.of(documentFolder));
        when(documentFolderMapper.toDto(any(DocumentFolder.class))).thenReturn(documentFolderDTO);

        DocumentFolderDTO result = documentFolderService.getDocumentFolderById(1L);

        assertNotNull(result);
        assertEquals(documentFolderDTO, result);
        verify(documentFolderRepository, times(1)).findById(1L);
        verify(documentFolderMapper, times(1)).toDto(any(DocumentFolder.class));
    }

    @Test
    void testGetDocumentFolderById_NotFound() {
        when(documentFolderRepository.findById(1L)).thenReturn(Optional.empty());

        DocumentFolderNotFoundException exception =
                assertThrows(
                        DocumentFolderNotFoundException.class,
                        () -> documentFolderService.getDocumentFolderById(1L));

        assertEquals("Folder not found with id: 1", exception.getMessage());
        verify(documentFolderRepository, times(1)).findById(1L);
        verifyNoInteractions(documentFolderMapper);
    }

    @Test
    void testUpdateDocumentFolder_Success() {
        DocumentFolder existingFolder = new DocumentFolder();
        existingFolder.setId(1L);
        existingFolder.setName("Old Folder");

        when(documentFolderRepository.findById(1L)).thenReturn(Optional.of(existingFolder));
        when(documentFolderRepository.save(any(DocumentFolder.class))).thenReturn(documentFolder);
        when(documentFolderMapper.toDto(any(DocumentFolder.class))).thenReturn(documentFolderDTO);

        DocumentFolderDTO result =
                documentFolderService.updateDocumentFolder(1L, documentFolderDTO);

        assertNotNull(result);
        assertEquals(documentFolderDTO, result);
        verify(documentFolderRepository, times(1)).findById(1L);
        verify(documentFolderRepository, times(1)).save(any(DocumentFolder.class));
        verify(documentFolderMapper, times(1)).toDto(any(DocumentFolder.class));
    }

    @Test
    void testUpdateDocumentFolder_WithParent_Success() {
        DocumentFolder existingFolder = new DocumentFolder();
        existingFolder.setId(1L);
        existingFolder.setName("Old Folder");

        DocumentFolder parentFolder = new DocumentFolder();
        parentFolder.setId(2L);

        documentFolderDTO.setParentFolderId(2L);

        when(documentFolderRepository.findById(1L)).thenReturn(Optional.of(existingFolder));
        when(documentFolderRepository.findById(2L)).thenReturn(Optional.of(parentFolder));
        when(documentFolderRepository.save(any(DocumentFolder.class))).thenReturn(documentFolder);
        when(documentFolderMapper.toDto(any(DocumentFolder.class))).thenReturn(documentFolderDTO);

        DocumentFolderDTO result =
                documentFolderService.updateDocumentFolder(1L, documentFolderDTO);

        assertNotNull(result);
        assertEquals(documentFolderDTO, result);
        verify(documentFolderRepository, times(1)).findById(1L);
        verify(documentFolderRepository, times(1)).findById(2L);
        verify(documentFolderRepository, times(1)).save(any(DocumentFolder.class));
        verify(documentFolderMapper, times(1)).toDto(any(DocumentFolder.class));
    }

    @Test
    void testUpdateDocumentFolder_NotFound() {
        when(documentFolderRepository.findById(1L)).thenReturn(Optional.empty());

        DocumentFolderNotFoundException exception =
                assertThrows(
                        DocumentFolderNotFoundException.class,
                        () -> documentFolderService.updateDocumentFolder(1L, documentFolderDTO));

        assertEquals("Folder not found", exception.getMessage());
        verify(documentFolderRepository, times(1)).findById(1L);
        verifyNoMoreInteractions(documentFolderRepository);
        verifyNoInteractions(documentFolderMapper);
    }

    @Test
    void testUpdateDocumentFolder_ParentNotFound() {
        DocumentFolder existingFolder = new DocumentFolder();
        existingFolder.setId(1L);

        documentFolderDTO.setParentFolderId(2L);

        when(documentFolderRepository.findById(1L)).thenReturn(Optional.of(existingFolder));
        when(documentFolderRepository.findById(2L)).thenReturn(Optional.empty());

        DocumentFolderNotFoundException exception =
                assertThrows(
                        DocumentFolderNotFoundException.class,
                        () -> documentFolderService.updateDocumentFolder(1L, documentFolderDTO));

        assertEquals("Parent folder not found", exception.getMessage());
        verify(documentFolderRepository, times(1)).findById(1L);
        verify(documentFolderRepository, times(1)).findById(2L);
        verifyNoMoreInteractions(documentFolderRepository);
        verifyNoInteractions(documentFolderMapper);
    }

    @Test
    void testDeleteDocumentFolder_Success() {
        DocumentFolder existingFolder = new DocumentFolder();
        existingFolder.setId(1L);

        when(documentFolderRepository.findById(1L)).thenReturn(Optional.of(existingFolder));
        when(documentFolderRepository.save(any(DocumentFolder.class))).thenReturn(existingFolder);

        documentFolderService.deleteDocumentFolder(1L);

        verify(documentFolderRepository, times(1)).findById(1L);
        verify(documentFolderRepository, times(1)).save(any(DocumentFolder.class));
        verifyNoInteractions(documentFolderMapper);
    }

    @Test
    void testDeleteDocumentFolder_NotFound() {
        when(documentFolderRepository.findById(1L)).thenReturn(Optional.empty());

        DocumentFolderNotFoundException exception =
                assertThrows(
                        DocumentFolderNotFoundException.class,
                        () -> documentFolderService.deleteDocumentFolder(1L));

        assertEquals("Folder not found", exception.getMessage());
        verify(documentFolderRepository, times(1)).findById(1L);
        verifyNoMoreInteractions(documentFolderRepository);
        verifyNoInteractions(documentFolderMapper);
    }

    @Test
    void testGetFoldersWithFilters_Success() {
        Page<DocumentFolder> folders = new PageImpl<>(Collections.singletonList(documentFolder));
        when(documentFolderRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(folders);
        when(documentFolderMapper.toDto(any(DocumentFolder.class))).thenReturn(documentFolderDTO);

        filterDTO.setName("Test");
        filterDTO.setUserId(1); // Changed to Long
        filterDTO.setParentFolderId(2L);

        Page<DocumentFolderDTO> result =
                documentFolderService.getFoldersWithFilters(filterDTO, PageRequest.of(0, 10));

        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(documentFolderDTO, result.getContent().get(0));
        verify(documentFolderRepository, times(1))
                .findAll(any(Specification.class), any(Pageable.class));
        verify(documentFolderMapper, times(1)).toDto(any(DocumentFolder.class));
    }

    @Test
    void testGetFoldersWithFilters_Empty() {
        Page<DocumentFolder> emptyPage = new PageImpl<>(Collections.emptyList());
        when(documentFolderRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(emptyPage);

        Page<DocumentFolderDTO> result =
                documentFolderService.getFoldersWithFilters(filterDTO, PageRequest.of(0, 10));

        assertNotNull(result);
        assertTrue(result.getContent().isEmpty());
        verify(documentFolderRepository, times(1))
                .findAll(any(Specification.class), any(Pageable.class));
        verifyNoInteractions(documentFolderMapper);
    }

    @Test
    void testGetFoldersWithFilters_NoFilters() {
        Page<DocumentFolder> folders = new PageImpl<>(Collections.singletonList(documentFolder));
        when(documentFolderRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(folders);
        when(documentFolderMapper.toDto(any(DocumentFolder.class))).thenReturn(documentFolderDTO);

        Page<DocumentFolderDTO> result =
                documentFolderService.getFoldersWithFilters(
                        new FolderFilterDTO(), PageRequest.of(0, 10));

        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(documentFolderDTO, result.getContent().get(0));
        verify(documentFolderRepository, times(1))
                .findAll(any(Specification.class), any(Pageable.class));
        verify(documentFolderMapper, times(1)).toDto(any(DocumentFolder.class));
    }
}
