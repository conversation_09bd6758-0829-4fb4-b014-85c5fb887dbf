package com.arealytics.areadocs.service;

import com.arealytics.areadocs.domain.DocumentCategory;
import com.arealytics.areadocs.dto.requestDTO.DocumentCategoryFilterDTO;
import com.arealytics.areadocs.dto.responseDTO.DocumentCategoryDTO;
import com.arealytics.areadocs.exception.DocumentCategoryNotFoundException;
import com.arealytics.areadocs.mapper.DocumentCategoryMapper;
import com.arealytics.areadocs.repository.DocumentCategoryRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;

import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DocumentCategoryServiceTest {

    @Mock
    private DocumentCategoryRepository documentCategoryRepository;

    @Mock
    private DocumentCategoryMapper documentCategoryMapper;

    @InjectMocks
    private DocumentCategoryService documentCategoryService;

    private DocumentCategoryDTO documentCategoryDTO;
    private DocumentCategory documentCategory;
    private DocumentCategoryFilterDTO filterDTO;

    @BeforeEach
    void setUp() {
        documentCategoryDTO = new DocumentCategoryDTO();
        documentCategoryDTO.setId(1L);
        documentCategoryDTO.setName("HR Docs");
        documentCategoryDTO.setDescription("HR Manual");

        documentCategory = new DocumentCategory();
        documentCategory.setId(1L);
        documentCategory.setName("HR Docs");
        documentCategory.setDescription("HR Manual");
        documentCategory.setCreatedAt(Instant.now());
        documentCategory.setModifiedAt(Instant.now());

        filterDTO = new DocumentCategoryFilterDTO();
    }

    @Test
    void testGetAllDocumentCategories_Success() {
        List<DocumentCategory> categories = Collections.singletonList(documentCategory);
        when(documentCategoryRepository.findAll()).thenReturn(categories);
        when(documentCategoryMapper.toDTO(any(DocumentCategory.class))).thenReturn(documentCategoryDTO);

        List<DocumentCategoryDTO> result = documentCategoryService.getAllDocumentCategories();

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(documentCategoryDTO, result.get(0));
        verify(documentCategoryRepository, times(1)).findAll();
        verify(documentCategoryMapper, times(1)).toDTO(any(DocumentCategory.class));
    }

    @Test
    void testGetAllDocumentCategories_Empty() {
        when(documentCategoryRepository.findAll()).thenReturn(Collections.emptyList());

        List<DocumentCategoryDTO> result = documentCategoryService.getAllDocumentCategories();

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(documentCategoryRepository, times(1)).findAll();
        verifyNoInteractions(documentCategoryMapper);
    }

    @Test
    void testGetDocumentCategoryById_Success() {
        when(documentCategoryRepository.findById(1L)).thenReturn(Optional.of(documentCategory));
        when(documentCategoryMapper.toDTO(any(DocumentCategory.class))).thenReturn(documentCategoryDTO);

        DocumentCategoryDTO result = documentCategoryService.getDocumentCategoryById(1L);

        assertNotNull(result);
        assertEquals(documentCategoryDTO, result);
        verify(documentCategoryRepository, times(1)).findById(1L);
        verify(documentCategoryMapper, times(1)).toDTO(any(DocumentCategory.class));
    }

    @Test
    void testGetDocumentCategoryById_NotFound() {
        when(documentCategoryRepository.findById(1L)).thenReturn(Optional.empty());

        DocumentCategoryNotFoundException exception = assertThrows(DocumentCategoryNotFoundException.class,
                () -> documentCategoryService.getDocumentCategoryById(1L));

        assertEquals("Category not found", exception.getMessage());
        verify(documentCategoryRepository, times(1)).findById(1L);
        verifyNoInteractions(documentCategoryMapper);
    }

    @Test
    void testUpdateDocumentCategory_Success() {
        DocumentCategory existingCategory = new DocumentCategory();
        existingCategory.setId(1L);
        existingCategory.setName("Old Category");
        existingCategory.setDescription("Old Description");

        when(documentCategoryRepository.findById(1L)).thenReturn(Optional.of(existingCategory));
        when(documentCategoryRepository.save(any(DocumentCategory.class))).thenReturn(documentCategory);
        when(documentCategoryMapper.toDTO(any(DocumentCategory.class))).thenReturn(documentCategoryDTO);

        DocumentCategoryDTO result = documentCategoryService.updateDocumentCategory(1L, documentCategoryDTO);

        assertNotNull(result);
        assertEquals(documentCategoryDTO, result);
        verify(documentCategoryRepository, times(1)).findById(1L);
        verify(documentCategoryRepository, times(1)).save(any(DocumentCategory.class));
        verify(documentCategoryMapper, times(1)).toDTO(any(DocumentCategory.class));
    }

    @Test
    void testUpdateDocumentCategory_WithParent_Success() {
        DocumentCategory parentCategory = new DocumentCategory();
        parentCategory.setId(2L);
        parentCategory.setName("Parent Category");

        DocumentCategory existingCategory = new DocumentCategory();
        existingCategory.setId(1L);
        existingCategory.setName("Old Category");

        DocumentCategoryDTO inputDTO = new DocumentCategoryDTO();
        inputDTO.setId(1L);
        inputDTO.setName("HR Docs");
        inputDTO.setDescription("HR Manual");
        DocumentCategoryDTO parentDTO = new DocumentCategoryDTO();
        parentDTO.setId(2L);
        inputDTO.setParentCategory(parentDTO);

        when(documentCategoryRepository.findById(1L)).thenReturn(Optional.of(existingCategory));
        when(documentCategoryRepository.findById(2L)).thenReturn(Optional.of(parentCategory));
        when(documentCategoryRepository.save(any(DocumentCategory.class))).thenReturn(documentCategory);
        when(documentCategoryMapper.toDTO(any(DocumentCategory.class))).thenReturn(documentCategoryDTO);

        DocumentCategoryDTO result = documentCategoryService.updateDocumentCategory(1L, inputDTO);

        assertNotNull(result);
        assertEquals(documentCategoryDTO, result);
        verify(documentCategoryRepository, times(1)).findById(1L);
        verify(documentCategoryRepository, times(1)).findById(2L);
        verify(documentCategoryRepository, times(1)).save(any(DocumentCategory.class));
        verify(documentCategoryMapper, times(1)).toDTO(any(DocumentCategory.class));
    }

    @Test
    void testUpdateDocumentCategory_NotFound() {
        when(documentCategoryRepository.findById(1L)).thenReturn(Optional.empty());

        DocumentCategoryNotFoundException exception = assertThrows(DocumentCategoryNotFoundException.class,
                () -> documentCategoryService.updateDocumentCategory(1L, documentCategoryDTO));

        assertEquals("Category not found", exception.getMessage());
        verify(documentCategoryRepository, times(1)).findById(1L);
        verifyNoMoreInteractions(documentCategoryRepository);
        verifyNoInteractions(documentCategoryMapper);
    }

    @Test
    void testUpdateDocumentCategory_ParentNotFound() {
        DocumentCategory existingCategory = new DocumentCategory();
        existingCategory.setId(1L);

        DocumentCategoryDTO inputDTO = new DocumentCategoryDTO();
        inputDTO.setId(1L);
        inputDTO.setName("HR Docs");
        DocumentCategoryDTO parentDTO = new DocumentCategoryDTO();
        parentDTO.setId(2L);
        inputDTO.setParentCategory(parentDTO);

        when(documentCategoryRepository.findById(1L)).thenReturn(Optional.of(existingCategory));
        when(documentCategoryRepository.findById(2L)).thenReturn(Optional.empty());

        DocumentCategoryNotFoundException exception = assertThrows(DocumentCategoryNotFoundException.class,
                () -> documentCategoryService.updateDocumentCategory(1L, inputDTO));

        assertEquals("Parent category not found", exception.getMessage());
        verify(documentCategoryRepository, times(1)).findById(1L);
        verify(documentCategoryRepository, times(1)).findById(2L);
        verifyNoMoreInteractions(documentCategoryRepository);
        verifyNoInteractions(documentCategoryMapper);
    }

    @Test
    void testCreateDocumentCategory_Success() {
        when(documentCategoryMapper.toEntity(any(DocumentCategoryDTO.class))).thenReturn(documentCategory);
        when(documentCategoryRepository.save(any(DocumentCategory.class))).thenReturn(documentCategory);
        when(documentCategoryMapper.toDTO(any(DocumentCategory.class))).thenReturn(documentCategoryDTO);

        DocumentCategoryDTO result = documentCategoryService.createDocumentCategory(documentCategoryDTO);

        assertNotNull(result);
        assertEquals(documentCategoryDTO, result);
        verify(documentCategoryRepository, times(1)).save(any(DocumentCategory.class));
        verify(documentCategoryMapper, times(1)).toEntity(any(DocumentCategoryDTO.class));
        verify(documentCategoryMapper, times(1)).toDTO(any(DocumentCategory.class));
    }

    @Test
    void testCreateDocumentCategory_WithParent_Success() {
        DocumentCategory parentCategory = new DocumentCategory();
        parentCategory.setId(2L);

        DocumentCategoryDTO inputDTO = new DocumentCategoryDTO();
        inputDTO.setId(1L);
        inputDTO.setName("HR Docs");
        DocumentCategoryDTO parentDTO = new DocumentCategoryDTO();
        parentDTO.setId(2L);
        inputDTO.setParentCategory(parentDTO);

        when(documentCategoryMapper.toEntity(any(DocumentCategoryDTO.class))).thenReturn(documentCategory);
        when(documentCategoryRepository.findById(2L)).thenReturn(Optional.of(parentCategory));
        when(documentCategoryRepository.save(any(DocumentCategory.class))).thenReturn(documentCategory);
        when(documentCategoryMapper.toDTO(any(DocumentCategory.class))).thenReturn(documentCategoryDTO);

        DocumentCategoryDTO result = documentCategoryService.createDocumentCategory(inputDTO);

        assertNotNull(result);
        assertEquals(documentCategoryDTO, result);
        verify(documentCategoryRepository, times(1)).findById(2L);
        verify(documentCategoryRepository, times(1)).save(any(DocumentCategory.class));
        verify(documentCategoryMapper, times(1)).toEntity(any(DocumentCategoryDTO.class));
        verify(documentCategoryMapper, times(1)).toDTO(any(DocumentCategory.class));
    }

    @Test
    void testCreateDocumentCategory_ParentNotFound() {
        DocumentCategoryDTO inputDTO = new DocumentCategoryDTO();
        inputDTO.setId(1L);
        inputDTO.setName("HR Docs");
        DocumentCategoryDTO parentDTO = new DocumentCategoryDTO();
        parentDTO.setId(2L);
        inputDTO.setParentCategory(parentDTO);

        when(documentCategoryMapper.toEntity(any(DocumentCategoryDTO.class))).thenReturn(documentCategory);
        when(documentCategoryRepository.findById(2L)).thenReturn(Optional.empty());

        DocumentCategoryNotFoundException exception = assertThrows(DocumentCategoryNotFoundException.class,
                () -> documentCategoryService.createDocumentCategory(inputDTO));

        assertEquals("Parent category not found", exception.getMessage());
        verify(documentCategoryRepository, times(1)).findById(2L);
        verifyNoMoreInteractions(documentCategoryRepository);
        verify(documentCategoryMapper, times(1)).toEntity(any(DocumentCategoryDTO.class));
    }

    @Test
    void testDeleteDocumentCategory_Success() {
        when(documentCategoryRepository.existsById(1L)).thenReturn(true);

        documentCategoryService.deleteDocumentCategory(1L);

        verify(documentCategoryRepository, times(1)).existsById(1L);
        verify(documentCategoryRepository, times(1)).deleteById(1L);
        verifyNoInteractions(documentCategoryMapper);
    }

    @Test
    void testDeleteDocumentCategory_NotFound() {
        when(documentCategoryRepository.existsById(1L)).thenReturn(false);

        DocumentCategoryNotFoundException exception = assertThrows(DocumentCategoryNotFoundException.class,
                () -> documentCategoryService.deleteDocumentCategory(1L));

        assertEquals("Category not found", exception.getMessage());
        verify(documentCategoryRepository, times(1)).existsById(1L);
        verifyNoMoreInteractions(documentCategoryRepository);
        verifyNoInteractions(documentCategoryMapper);
    }

    @Test
    void testGetDocumentCategoriesWithFilters_Success() {
        Page<DocumentCategory> categories = new PageImpl<>(Collections.singletonList(documentCategory));
        when(documentCategoryRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(categories);
        when(documentCategoryMapper.toDTO(any(DocumentCategory.class))).thenReturn(documentCategoryDTO);

        filterDTO.setName("Test");
        filterDTO.setDescription("Desc");
        filterDTO.setParentCategoryId(2L);

        Page<DocumentCategoryDTO> result = documentCategoryService.getDocumentCategoriesWithFilters(filterDTO, PageRequest.of(0, 10));

        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(documentCategoryDTO, result.getContent().get(0));
        verify(documentCategoryRepository, times(1)).findAll(any(Specification.class), any(Pageable.class));
        verify(documentCategoryMapper, times(1)).toDTO(any(DocumentCategory.class));
    }

    @Test
    void testGetDocumentCategoriesWithFilters_Empty() {
        Page<DocumentCategory> emptyPage = new PageImpl<>(Collections.emptyList());
        when(documentCategoryRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(emptyPage);

        Page<DocumentCategoryDTO> result = documentCategoryService.getDocumentCategoriesWithFilters(filterDTO, PageRequest.of(0, 10));

        assertNotNull(result);
        assertTrue(result.getContent().isEmpty());
        verify(documentCategoryRepository, times(1)).findAll(any(Specification.class), any(Pageable.class));
        verifyNoInteractions(documentCategoryMapper);
    }

    @Test
    void testGetDocumentCategoriesWithFilters_NoFilters() {
        Page<DocumentCategory> categories = new PageImpl<>(Collections.singletonList(documentCategory));
        when(documentCategoryRepository.findAll(any(Specification.class), any(Pageable.class))).thenReturn(categories);
        when(documentCategoryMapper.toDTO(any(DocumentCategory.class))).thenReturn(documentCategoryDTO);

        Page<DocumentCategoryDTO> result = documentCategoryService.getDocumentCategoriesWithFilters(new DocumentCategoryFilterDTO(), PageRequest.of(0, 10));

        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(documentCategoryDTO, result.getContent().get(0));
        verify(documentCategoryRepository, times(1)).findAll(any(Specification.class), any(Pageable.class));
        verify(documentCategoryMapper, times(1)).toDTO(any(DocumentCategory.class));
    }
}