package com.arealytics.areadocs.service;

import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import com.arealytics.areadocs.domain.Permission;
import com.arealytics.areadocs.domain.Role;
import com.arealytics.areadocs.domain.RolePermissionMapping;
import com.arealytics.areadocs.dto.requestDTO.RolePermissionRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.RolePermissionDTO;
import com.arealytics.areadocs.exception.PermissionException;
import com.arealytics.areadocs.exception.RoleException;
import com.arealytics.areadocs.mapper.RolePermissionMapper;
import com.arealytics.areadocs.repository.PermissionRepository;
import com.arealytics.areadocs.repository.RolePermissionMappingRepository;
import com.arealytics.areadocs.repository.RoleRepository;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RolePermissionServiceTest {

    @Mock private RolePermissionMappingRepository rolePermissionMappingRepository;

    @Mock private RoleRepository roleRepository;

    @Mock private PermissionRepository permissionRepository;

    @Mock private RolePermissionMapper rolePermissionMapper;

    @InjectMocks private RolePermissionService rolePermissionService;

    private Role role;
    private Permission permission;
    private RolePermissionMapping rolePermissionMapping;
    private RolePermissionDTO rolePermissionDTO;
    private RolePermissionRequestDTO rolePermissionRequestDTO;

    @BeforeEach
    void setUp() {
        role = new Role();
        role.setId(1L);
        role.setName("Admin");

        permission = new Permission();
        permission.setId(1L);
        permission.setPermissionCode("CREATE_USER");
        permission.setPermissionName("Create User");
        permission.setPermissionDescription("Ability to create new users");

        rolePermissionMapping = new RolePermissionMapping();
        rolePermissionMapping.setId(1L);
        rolePermissionMapping.setRole(role);
        rolePermissionMapping.setPermission(permission);
        rolePermissionMapping.setStatus("ACTIVE");

        rolePermissionDTO = new RolePermissionDTO();
        rolePermissionDTO.setId(1L);
        rolePermissionDTO.setRoleId(1L);
        rolePermissionDTO.setRoleName("Admin");
        rolePermissionDTO.setPermissionId(1L);
        rolePermissionDTO.setPermissionCode("CREATE_USER");
        rolePermissionDTO.setPermissionName("Create User");

        rolePermissionRequestDTO = new RolePermissionRequestDTO();
        rolePermissionRequestDTO.setRoleId(1L);
        rolePermissionRequestDTO.setPermissionId(1L);
    }

    @Test
    void assignPermissionToRole_Success() {
        when(roleRepository.findById(1L)).thenReturn(Optional.of(role));
        when(permissionRepository.findById(1L)).thenReturn(Optional.of(permission));
        when(rolePermissionMappingRepository.existsByRoleIdAndPermissionId(1L, 1L))
                .thenReturn(false);
        when(rolePermissionMappingRepository.save(any(RolePermissionMapping.class)))
                .thenReturn(rolePermissionMapping);
        when(rolePermissionMapper.toDto(rolePermissionMapping)).thenReturn(rolePermissionDTO);

        RolePermissionDTO result =
                rolePermissionService.assignPermissionToRole(rolePermissionRequestDTO);

        assertNotNull(result);
        assertEquals(rolePermissionDTO.getRoleId(), result.getRoleId());
        assertEquals(rolePermissionDTO.getPermissionId(), result.getPermissionId());
        verify(rolePermissionMappingRepository).save(any(RolePermissionMapping.class));
    }

    @Test
    void assignPermissionToRole_RoleNotFound() {
        when(roleRepository.findById(1L)).thenReturn(Optional.empty());

        assertThrows(
                RoleException.RoleNotFoundException.class,
                () -> rolePermissionService.assignPermissionToRole(rolePermissionRequestDTO));
        verify(rolePermissionMappingRepository, never()).save(any(RolePermissionMapping.class));
    }

    @Test
    void assignPermissionToRole_PermissionNotFound() {
        when(roleRepository.findById(1L)).thenReturn(Optional.of(role));
        when(permissionRepository.findById(1L)).thenReturn(Optional.empty());

        assertThrows(
                PermissionException.PermissionNotFoundException.class,
                () -> rolePermissionService.assignPermissionToRole(rolePermissionRequestDTO));
        verify(rolePermissionMappingRepository, never()).save(any(RolePermissionMapping.class));
    }

    @Test
    void assignPermissionToRole_AlreadyAssigned() {
        when(roleRepository.findById(1L)).thenReturn(Optional.of(role));
        when(permissionRepository.findById(1L)).thenReturn(Optional.of(permission));
        when(rolePermissionMappingRepository.existsByRoleIdAndPermissionId(1L, 1L))
                .thenReturn(true);

        assertThrows(
                IllegalStateException.class,
                () -> rolePermissionService.assignPermissionToRole(rolePermissionRequestDTO));
        verify(rolePermissionMappingRepository, never()).save(any(RolePermissionMapping.class));
    }

    @Test
    void getRolePermissions_Success() {
        when(roleRepository.existsById(1L)).thenReturn(true);
        when(rolePermissionMappingRepository.findByRoleId(1L))
                .thenReturn(List.of(rolePermissionMapping));
        when(rolePermissionMapper.toDto(rolePermissionMapping)).thenReturn(rolePermissionDTO);

        List<RolePermissionDTO> result = rolePermissionService.getRolePermissions(1L);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(rolePermissionDTO.getRoleId(), result.get(0).getRoleId());
        assertEquals(rolePermissionDTO.getPermissionId(), result.get(0).getPermissionId());
    }

    @Test
    void getRolePermissions_RoleNotFound() {
        when(roleRepository.existsById(1L)).thenReturn(false);

        assertThrows(
                RoleException.RoleNotFoundException.class,
                () -> rolePermissionService.getRolePermissions(1L));
    }

    @Test
    void getPermissionRoles_Success() {
        when(permissionRepository.existsById(1L)).thenReturn(true);
        when(rolePermissionMappingRepository.findByPermissionId(1L))
                .thenReturn(List.of(rolePermissionMapping));
        when(rolePermissionMapper.toDto(rolePermissionMapping)).thenReturn(rolePermissionDTO);

        List<RolePermissionDTO> result = rolePermissionService.getPermissionRoles(1L);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(rolePermissionDTO.getRoleId(), result.get(0).getRoleId());
        assertEquals(rolePermissionDTO.getPermissionId(), result.get(0).getPermissionId());
    }

    @Test
    void getPermissionRoles_PermissionNotFound() {
        when(permissionRepository.existsById(1L)).thenReturn(false);

        assertThrows(
                PermissionException.PermissionNotFoundException.class,
                () -> rolePermissionService.getPermissionRoles(1L));
    }

    @Test
    void getRolePermissionById_Success() {
        when(rolePermissionMappingRepository.findById(1L))
                .thenReturn(Optional.of(rolePermissionMapping));
        when(rolePermissionMapper.toDto(rolePermissionMapping)).thenReturn(rolePermissionDTO);

        RolePermissionDTO result = rolePermissionService.getRolePermissionById(1L);

        assertNotNull(result);
        assertEquals(rolePermissionDTO.getRoleId(), result.getRoleId());
        assertEquals(rolePermissionDTO.getPermissionId(), result.getPermissionId());
    }

    @Test
    void getRolePermissionById_NotFound() {
        when(rolePermissionMappingRepository.findById(1L)).thenReturn(Optional.empty());

        assertThrows(
                IllegalArgumentException.class,
                () -> rolePermissionService.getRolePermissionById(1L));
    }

    @Test
    void updateRolePermission_Success() {
        when(rolePermissionMappingRepository.findById(1L))
                .thenReturn(Optional.of(rolePermissionMapping));
        when(rolePermissionMappingRepository.save(rolePermissionMapping))
                .thenReturn(rolePermissionMapping);
        when(rolePermissionMapper.toDto(rolePermissionMapping)).thenReturn(rolePermissionDTO);

        RolePermissionDTO result =
                rolePermissionService.updateRolePermission(1L, rolePermissionRequestDTO);

        assertNotNull(result);
        assertEquals(rolePermissionDTO.getRoleId(), result.getRoleId());
        assertEquals(rolePermissionDTO.getPermissionId(), result.getPermissionId());
        verify(rolePermissionMappingRepository).save(rolePermissionMapping);
    }

    @Test
    void updateRolePermission_NotFound() {
        when(rolePermissionMappingRepository.findById(1L)).thenReturn(Optional.empty());

        assertThrows(
                IllegalArgumentException.class,
                () -> rolePermissionService.updateRolePermission(1L, rolePermissionRequestDTO));
        verify(rolePermissionMappingRepository, never()).save(any(RolePermissionMapping.class));
    }

    @Test
    void removeRolePermission_Success() {
        when(rolePermissionMappingRepository.existsById(1L)).thenReturn(true);
        doNothing().when(rolePermissionMappingRepository).deleteById(1L);

        assertDoesNotThrow(() -> rolePermissionService.removeRolePermission(1L));
        verify(rolePermissionMappingRepository).deleteById(1L);
    }

    @Test
    void removeRolePermission_NotFound() {
        when(rolePermissionMappingRepository.existsById(1L)).thenReturn(false);

        assertThrows(
                IllegalArgumentException.class,
                () -> rolePermissionService.removeRolePermission(1L));
        verify(rolePermissionMappingRepository, never()).deleteById(anyLong());
    }

    @Test
    void removePermissionFromRole_Success() {
        when(rolePermissionMappingRepository.findByRoleIdAndPermissionId(1L, 1L))
                .thenReturn(Optional.of(rolePermissionMapping));
        doNothing().when(rolePermissionMappingRepository).delete(rolePermissionMapping);

        assertDoesNotThrow(() -> rolePermissionService.removePermissionFromRole(1L, 1L));
        verify(rolePermissionMappingRepository).delete(rolePermissionMapping);
    }

    @Test
    void removePermissionFromRole_NotFound() {
        when(rolePermissionMappingRepository.findByRoleIdAndPermissionId(1L, 1L))
                .thenReturn(Optional.empty());

        assertThrows(
                IllegalArgumentException.class,
                () -> rolePermissionService.removePermissionFromRole(1L, 1L));
        verify(rolePermissionMappingRepository, never()).delete(any(RolePermissionMapping.class));
    }

    @Test
    void removeAllPermissionsFromRole_Success() {
        when(roleRepository.existsById(1L)).thenReturn(true);
        doNothing().when(rolePermissionMappingRepository).deleteByRoleId(1L);

        assertDoesNotThrow(() -> rolePermissionService.removeAllPermissionsFromRole(1L));
        verify(rolePermissionMappingRepository).deleteByRoleId(1L);
    }

    @Test
    void removeAllPermissionsFromRole_RoleNotFound() {
        when(roleRepository.existsById(1L)).thenReturn(false);

        assertThrows(
                RoleException.RoleNotFoundException.class,
                () -> rolePermissionService.removeAllPermissionsFromRole(1L));
        verify(rolePermissionMappingRepository, never()).deleteByRoleId(anyLong());
    }

    @Test
    void assignPermissionsToRole_Success() {
        when(roleRepository.findById(1L)).thenReturn(Optional.of(role));
        when(permissionRepository.findById(1L)).thenReturn(Optional.of(permission));
        when(rolePermissionMappingRepository.existsByRoleIdAndPermissionId(1L, 1L))
                .thenReturn(false);
        when(rolePermissionMappingRepository.save(any(RolePermissionMapping.class)))
                .thenReturn(rolePermissionMapping);
        when(rolePermissionMapper.toDto(rolePermissionMapping)).thenReturn(rolePermissionDTO);

        List<RolePermissionDTO> result =
                rolePermissionService.assignPermissionsToRole(1L, List.of(1L));

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(rolePermissionDTO.getRoleId(), result.get(0).getRoleId());
        assertEquals(rolePermissionDTO.getPermissionId(), result.get(0).getPermissionId());
        verify(rolePermissionMappingRepository).save(any(RolePermissionMapping.class));
    }

    @Test
    void assignPermissionsToRole_RoleNotFound() {
        when(roleRepository.findById(1L)).thenReturn(Optional.empty());

        assertThrows(
                RoleException.RoleNotFoundException.class,
                () -> rolePermissionService.assignPermissionsToRole(1L, List.of(1L)));
        verify(rolePermissionMappingRepository, never()).save(any(RolePermissionMapping.class));
    }

    @Test
    void getAllRolePermissions_Success() {
        Page<RolePermissionMapping> rolePermissionPage =
                new PageImpl<>(List.of(rolePermissionMapping));
        when(rolePermissionMappingRepository.findAll(any(Pageable.class)))
                .thenReturn(rolePermissionPage);
        when(rolePermissionMapper.toDto(rolePermissionMapping)).thenReturn(rolePermissionDTO);

        Page<RolePermissionDTO> result =
                rolePermissionService.getAllRolePermissions(Pageable.unpaged());

        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(rolePermissionDTO.getRoleId(), result.getContent().get(0).getRoleId());
        assertEquals(
                rolePermissionDTO.getPermissionId(), result.getContent().get(0).getPermissionId());
    }
}
