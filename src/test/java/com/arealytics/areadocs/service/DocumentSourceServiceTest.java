package com.arealytics.areadocs.service;

import java.time.Instant;
import java.util.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import com.arealytics.areadocs.domain.DocumentSource;
import com.arealytics.areadocs.dto.requestDTO.DocumentSourceFilterDTO;
import com.arealytics.areadocs.dto.requestDTO.DocumentSourceRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.DocumentSourceDTO;
import com.arealytics.areadocs.exception.DocumentSourceNotFoundException;
import com.arealytics.areadocs.mapper.DocumentSourceMapper;
import com.arealytics.areadocs.repository.DocumentSourceRepository;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DocumentSourceServiceTest {

    @Mock private DocumentSourceRepository documentSourceRepository;

    @Mock private DocumentSourceMapper documentSourceMapper;

    @InjectMocks private DocumentSourceService documentSourceService;

    private DocumentSource documentSource;
    private DocumentSourceDTO documentSourceDTO;
    private DocumentSourceFilterDTO filterDTO;

    @BeforeEach
    void setUp() {
        documentSource = new DocumentSource();
        documentSource.setId(1L);
        documentSource.setName("Human Resources");
        documentSource.setBaseUrl("http://hr.com");
        documentSource.setCreatedAt(Instant.now());

        documentSourceDTO = new DocumentSourceDTO();
        documentSourceDTO.setId(1L);
        documentSourceDTO.setName("Human Resources");
        documentSourceDTO.setBaseUrl("http://hr.com");

        filterDTO = new DocumentSourceFilterDTO();
    }

    @Test
    void testCreateDocumentSource_Success() {
        // Create a DocumentSourceRequestDTO for the test
        DocumentSourceRequestDTO requestDTO = new DocumentSourceRequestDTO();
        requestDTO.setName("Human Resources");
        requestDTO.setBaseUrl("http://hr.com");

        when(documentSourceMapper.toEntity(any(DocumentSourceRequestDTO.class)))
                .thenReturn(documentSource);
        when(documentSourceRepository.save(any(DocumentSource.class))).thenReturn(documentSource);
        when(documentSourceMapper.toDto(any(DocumentSource.class))).thenReturn(documentSourceDTO);

        DocumentSourceDTO result =
                documentSourceService.createDocumentSource(requestDTO); // Pass the requestDTO

        assertNotNull(result);
        assertEquals(documentSourceDTO, result);
        verify(documentSourceRepository).save(any(DocumentSource.class));
        verify(documentSourceMapper).toEntity(requestDTO); // Verify with the correct argument
        verify(documentSourceMapper).toDto(documentSource);
    }

    @Test
    void testGetAllDocumentSources_Success() {
        when(documentSourceRepository.findAll())
                .thenReturn(Collections.singletonList(documentSource));
        when(documentSourceMapper.toDto(any(DocumentSource.class))).thenReturn(documentSourceDTO);

        List<DocumentSourceDTO> result = documentSourceService.getAllDocumentSources();

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(documentSourceDTO, result.get(0));
        verify(documentSourceRepository).findAll();
        verify(documentSourceMapper).toDto(documentSource);
    }

    @Test
    void testGetAllDocumentSources_Empty() {
        when(documentSourceRepository.findAll()).thenReturn(Collections.emptyList());

        List<DocumentSourceDTO> result = documentSourceService.getAllDocumentSources();

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(documentSourceRepository).findAll();
        verifyNoInteractions(documentSourceMapper);
    }

    @Test
    void testGetDocumentSourceById_Success() {
        when(documentSourceRepository.findById(1L)).thenReturn(Optional.of(documentSource));
        when(documentSourceMapper.toDto(documentSource)).thenReturn(documentSourceDTO);

        DocumentSourceDTO result = documentSourceService.getDocumentSourceById(1L);

        assertNotNull(result);
        assertEquals(documentSourceDTO, result);
        verify(documentSourceRepository).findById(1L);
        verify(documentSourceMapper).toDto(documentSource);
    }

    @Test
    void testGetDocumentSourceById_NotFound() {
        when(documentSourceRepository.findById(1L)).thenReturn(Optional.empty());

        DocumentSourceNotFoundException exception =
                assertThrows(
                        DocumentSourceNotFoundException.class,
                        () -> documentSourceService.getDocumentSourceById(1L));

        assertEquals("DocumentSource with ID 1 not found", exception.getMessage());
        verify(documentSourceRepository).findById(1L);
        verifyNoInteractions(documentSourceMapper);
    }

    @Test
    void testUpdateDocumentSource_Success() {
        when(documentSourceRepository.findById(1L)).thenReturn(Optional.of(documentSource));
        when(documentSourceRepository.save(any(DocumentSource.class))).thenReturn(documentSource);
        when(documentSourceMapper.toDto(documentSource)).thenReturn(documentSourceDTO);

        DocumentSourceDTO result =
                documentSourceService.updateDocumentSource(1L, documentSourceDTO);

        assertNotNull(result);
        assertEquals(documentSourceDTO, result);
        verify(documentSourceRepository).findById(1L);
        verify(documentSourceRepository).save(documentSource);
        verify(documentSourceMapper).toDto(documentSource);
    }

    @Test
    void testUpdateDocumentSource_NotFound() {
        when(documentSourceRepository.findById(1L)).thenReturn(Optional.empty());

        DocumentSourceNotFoundException exception =
                assertThrows(
                        DocumentSourceNotFoundException.class,
                        () -> documentSourceService.updateDocumentSource(1L, documentSourceDTO));

        assertEquals("DocumentSource with ID 1 not found", exception.getMessage());
        verify(documentSourceRepository).findById(1L);
        verifyNoMoreInteractions(documentSourceRepository);
        verifyNoInteractions(documentSourceMapper);
    }

    @Test
    void testDeleteDocumentSource_Success() {
        when(documentSourceRepository.findById(1L)).thenReturn(Optional.of(documentSource));
        when(documentSourceRepository.save(documentSource)).thenReturn(documentSource);

        assertDoesNotThrow(() -> documentSourceService.deleteDocumentSource(1L));

        verify(documentSourceRepository).findById(1L);
        verify(documentSourceRepository).save(documentSource);
    }

    @Test
    void testDeleteDocumentSource_NotFound() {
        when(documentSourceRepository.findById(1L)).thenReturn(Optional.empty());

        DocumentSourceNotFoundException exception =
                assertThrows(
                        DocumentSourceNotFoundException.class,
                        () -> documentSourceService.deleteDocumentSource(1L));

        assertEquals("DocumentSource with ID 1 not found", exception.getMessage());
        verify(documentSourceRepository).findById(1L);
    }

    @Test
    void testGetDocumentSourcesWithFilters_Success() {
        Page<DocumentSource> page = new PageImpl<>(List.of(documentSource));
        when(((JpaSpecificationExecutor<DocumentSource>) documentSourceRepository)
                        .findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(page);
        when(documentSourceMapper.toDto(documentSource)).thenReturn(documentSourceDTO);

        filterDTO.setName("Test");
        filterDTO.setBaseUrl("example.com");

        Page<DocumentSourceDTO> result =
                documentSourceService.getDocumentSourcesWithFilters(
                        filterDTO, PageRequest.of(0, 10));

        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(documentSourceDTO, result.getContent().get(0));
        verify(documentSourceRepository)
                .findAll(
                        ArgumentMatchers.<Specification<DocumentSource>>any(),
                        ArgumentMatchers.any(Pageable.class));
        verify(documentSourceMapper).toDto(documentSource);
    }

    @Test
    void testGetDocumentSourcesWithFilters_Empty() {
        Page<DocumentSource> emptyPage = new PageImpl<>(Collections.emptyList());
        when(documentSourceRepository.findAll(any(Specification.class), any(Pageable.class)))
                .thenReturn(emptyPage);

        Page<DocumentSourceDTO> result =
                documentSourceService.getDocumentSourcesWithFilters(
                        filterDTO, PageRequest.of(0, 10));

        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(documentSourceRepository)
                .findAll(
                        ArgumentMatchers.<Specification<DocumentSource>>any(),
                        ArgumentMatchers.any(Pageable.class));
        verifyNoInteractions(documentSourceMapper);
    }
}
