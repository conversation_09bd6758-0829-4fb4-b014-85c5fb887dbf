// package com.arealytics.areadocs.integration;

// import static org.junit.jupiter.api.Assertions.assertEquals;
// import static org.junit.jupiter.api.Assertions.assertNotNull;
// import static org.junit.jupiter.api.Assertions.assertTrue;
// import static org.springframework.test.web.client.match.MockRestRequestMatchers.header;
// import static org.springframework.test.web.client.match.MockRestRequestMatchers.method;
// import static org.springframework.test.web.client.match.MockRestRequestMatchers.requestTo;
// import static org.springframework.test.web.client.response.MockRestResponseCreators.withStatus;

// import java.net.URI;
// import java.util.ArrayList;
// import java.util.List;

// import com.arealytics.areadocs.dto.responseDTO.serv.ParcelResponse;
// import com.arealytics.areadocs.service.ServApiService;
// import org.junit.jupiter.api.BeforeEach;
// import org.junit.jupiter.api.Test;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.boot.test.autoconfigure.web.client.RestClientTest;
// import org.springframework.http.HttpMethod;
// import org.springframework.http.HttpStatus;
// import org.springframework.http.MediaType;
// import org.springframework.test.context.TestPropertySource;
// import org.springframework.test.web.client.MockRestServiceServer;
// import org.springframework.web.client.RestTemplate;
// import org.springframework.web.util.UriComponentsBuilder;

// import com.fasterxml.jackson.databind.ObjectMapper;

// @RestClientTest(ServApiService.class)
// @TestPropertySource(properties = {
//         "serv.api.baseUrl=https://test-api.example.com",
//         "serv.api.clientId=test-client-id",
//         "serv.api.clientSecret=test-client-secret",
//         "serv.api.authToken=test-auth-token"
// })
// public class ServApiParcelIntegrationTest {

//     @Autowired
//     private ServApiService servApiService;

//     @Autowired
//     private RestTemplate restTemplate;

//     @Autowired
//     private ObjectMapper objectMapper;

//     private MockRestServiceServer mockServer;
//     private ParcelResponse sampleParcelResponse;

//     @BeforeEach
//     void setUp() throws Exception {
//         mockServer = MockRestServiceServer.createServer(restTemplate);
//         sampleParcelResponse = createSampleParcelResponse();
//     }

//     @Test
//     void getParcel_IntegrationTest() throws Exception {
//         // Arrange
//         String spi = "3A2\\PS515587";
//         String baseUrl = "https://test-api.example.com";

//         URI expectedUri = UriComponentsBuilder
//                 .fromHttpUrl(baseUrl + "/discovery/v1/parcels")
//                 .queryParam("spi", spi)
//                 .build()
//                 .toUri();

//         mockServer.expect(requestTo(expectedUri))
//                 .andExpect(method(HttpMethod.GET))
//                 .andExpect(header("client_id", "test-client-id"))
//                 .andExpect(header("client_secret", "test-client-secret"))
//                 .andExpect(header("Authorization", "test-auth-token"))
//               //  .andExpect(header("x-correlation-id", value -> assertNotNull(value)))
//                 .andRespond(withStatus(HttpStatus.OK)
//                         .contentType(MediaType.APPLICATION_JSON)
//                         .body(objectMapper.writeValueAsString(sampleParcelResponse)));

//         // Act
//         var response = servApiService.getParcel(spi, ParcelResponse.class);

//         // Assert
//         mockServer.verify();
//         assertNotNull(response);
//         assertEquals(HttpStatus.OK, response.getStatusCode());

//         ParcelResponse parcel = response.getBody();
//         assertNotNull(parcel);
//         assertEquals(spi, parcel.getSpi());
//         assertEquals("LOT", parcel.getParcelType());
//         assertEquals("RESTRICTED", parcel.getLotType());
//         assertEquals("ACTIVE", parcel.getParcelStatus());

//         assertNotNull(parcel.getParcelDetails());
//         assertEquals("3A2", parcel.getParcelDetails().getLot());
//         assertEquals("PS515587S", parcel.getParcelDetails().getPlanNumber());

//         assertNotNull(parcel.getTitles());
//         assertEquals(1, parcel.getTitles().size());
//         assertEquals("FREEHOLD", parcel.getTitles().get(0).getTitleType());

//         assertNotNull(parcel.getProperties());
//         assertEquals(1, parcel.getProperties().size());
//         assertEquals("208045633", parcel.getProperties().get(0).getPropertyPfi());
//         assertTrue(parcel.getProperties().get(0).isMultiAssess());
//     }

//     private ParcelResponse createSampleParcelResponse() {
//         ParcelResponse response = new ParcelResponse();
//         response.setSpi("3A2\\PS515587");
//         response.setParcelType("LOT");
//         response.setLotType("RESTRICTED");
//         response.setParcelStatus("ACTIVE");

//         ParcelResponse.ParcelDetails details = new ParcelResponse.ParcelDetails();
//         details.setLot("3A2");
//         details.setPlanNumber("PS515587S");
//         response.setParcelDetails(details);

//         List<ParcelResponse.ParcelTitle> titles = new ArrayList<>();
//         ParcelResponse.ParcelTitle title = new ParcelResponse.ParcelTitle();
//         title.setTitleId("qp0gjuieZ5VzbNKtT/eZvqPryobRkEYxMA==");
//         title.setTitleStatus("ACTIVE");
//         title.setTitleType("FREEHOLD");
//         titles.add(title);
//         response.setTitles(titles);

//         List<ParcelResponse.ParcelProperty> properties = new ArrayList<>();
//         ParcelResponse.ParcelProperty property = new ParcelResponse.ParcelProperty();
//         property.setPropertyPfi("208045633");
//         property.setMultiAssess(true);
//         property.setPropertyStatus("ACTIVE");
//         properties.add(property);
//         response.setProperties(properties);

//         return response;
//     }
// }
