package com.arealytics.areadocs.controller;

import java.util.Collections;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import com.arealytics.areadocs.dto.requestDTO.PermissionFilterDTO;
import com.arealytics.areadocs.dto.requestDTO.PermissionRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.ApiResponse;
import com.arealytics.areadocs.dto.responseDTO.PermissionDTO;
import com.arealytics.areadocs.service.PermissionService;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PermissionControllerTest {

    @Mock private PermissionService permissionService;

    @InjectMocks private PermissionController permissionController;

    private PermissionRequestDTO permissionRequestDTO;
    private PermissionDTO permissionDTO;

    @BeforeEach
    void setUp() {
        permissionRequestDTO = new PermissionRequestDTO();
        permissionRequestDTO.setPermissionCode("TEST_CODE");
        permissionRequestDTO.setPermissionName("Test Permission");

        permissionDTO = new PermissionDTO();
        permissionDTO.setId(1L);
        permissionDTO.setPermissionCode("TEST_CODE");
        permissionDTO.setPermissionName("Test Permission");
    }

    @Test
    void testCreatePermission_Success() {
        when(permissionService.createPermission(any(PermissionRequestDTO.class)))
                .thenReturn(permissionDTO);

        ResponseEntity<ApiResponse<PermissionDTO>> response =
                permissionController.createPermission(permissionRequestDTO);

        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals("Permission created successfully", response.getBody().getMessage());
        assertEquals(permissionDTO, response.getBody().getData());
        verify(permissionService, times(1)).createPermission(any(PermissionRequestDTO.class));
    }

    @Test
    void testGetPermissions_ByPermissionId_Success() {
        when(permissionService.findPermissionById(anyLong()))
                .thenReturn(Optional.of(permissionDTO));

        ResponseEntity<ApiResponse<Page<PermissionDTO>>> response =
                permissionController.getPermissions(1L, null, null, 0, 10, "createdAt,desc");

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals(1, response.getBody().getData().getContent().size());
        assertEquals(permissionDTO, response.getBody().getData().getContent().get(0));
        verify(permissionService, times(1)).findPermissionById(anyLong());
    }

    @Test
    void testGetPermissions_ByPermissionId_NotFound() {
        when(permissionService.findPermissionById(anyLong())).thenReturn(Optional.empty());

        ResponseEntity<ApiResponse<Page<PermissionDTO>>> response =
                permissionController.getPermissions(1L, null, null, 0, 10, "createdAt,desc");

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertTrue(response.getBody().getData().isEmpty());
        verify(permissionService, times(1)).findPermissionById(anyLong());
    }

    @Test
    void testGetPermissions_WithFilters_Success() {
        Page<PermissionDTO> permissionPage =
                new PageImpl<>(Collections.singletonList(permissionDTO));
        when(permissionService.getPermissionsWithFilters(any(PermissionFilterDTO.class), any()))
                .thenReturn(permissionPage);

        ResponseEntity<ApiResponse<Page<PermissionDTO>>> response =
                permissionController.getPermissions(
                        null, null, "Test Permission", 0, 10, "createdAt,desc");

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals(permissionPage, response.getBody().getData());
        verify(permissionService, times(1))
                .getPermissionsWithFilters(any(PermissionFilterDTO.class), any());
    }

    @Test
    void testGetPermissionById_Success() {
        when(permissionService.getPermissionById(anyLong())).thenReturn(permissionDTO);

        ResponseEntity<ApiResponse<PermissionDTO>> response =
                permissionController.getPermissionById(1L);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals(permissionDTO, response.getBody().getData());
        verify(permissionService, times(1)).getPermissionById(anyLong());
    }

    @Test
    void testUpdatePermission_Success() {
        when(permissionService.updatePermission(anyLong(), any(PermissionRequestDTO.class)))
                .thenReturn(permissionDTO);

        ResponseEntity<ApiResponse<PermissionDTO>> response =
                permissionController.updatePermission(1L, permissionRequestDTO);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals("Permission updated successfully", response.getBody().getMessage());
        assertEquals(permissionDTO, response.getBody().getData());
        verify(permissionService, times(1))
                .updatePermission(anyLong(), any(PermissionRequestDTO.class));
    }

    @Test
    void testDeletePermission_Success() {
        doNothing().when(permissionService).deletePermission(anyLong());

        ResponseEntity<Void> response = permissionController.deletePermission(1L);

        assertEquals(HttpStatus.NO_CONTENT, response.getStatusCode());
        verify(permissionService, times(1)).deletePermission(anyLong());
    }
}
