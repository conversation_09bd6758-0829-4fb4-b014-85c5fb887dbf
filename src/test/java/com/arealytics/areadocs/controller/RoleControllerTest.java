package com.arealytics.areadocs.controller;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import com.arealytics.areadocs.dto.requestDTO.*;
import com.arealytics.areadocs.dto.responseDTO.*;
import com.arealytics.areadocs.service.RolePermissionService;
import com.arealytics.areadocs.service.RoleService;
import com.arealytics.areadocs.service.UserRoleService;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RoleControllerTest {

    @Mock private RoleService roleService;
    @Mock private RolePermissionService rolePermissionService;
    @Mock private UserRoleService userRoleService;

    @InjectMocks private RoleController roleController;

    private RoleDTO roleDTO;
    private RoleRequestDTO roleRequestDTO;
    private RolePermissionDTO rolePermissionDTO;
    private RolePermissionAssignmentRequestDTO permissionRequestDTO;
    private UserRoleDTO userRoleDTO;
    private RoleUserAssignmentRequestDTO userAssignmentRequestDTO;

    @BeforeEach
    void setUp() {
        roleDTO = new RoleDTO();
        roleDTO.setId(1L);
        roleDTO.setName("Admin");

        roleRequestDTO = new RoleRequestDTO();
        roleRequestDTO.setName("Admin");

        rolePermissionDTO = new RolePermissionDTO();
        rolePermissionDTO.setId(1L);

        permissionRequestDTO = new RolePermissionAssignmentRequestDTO();
        permissionRequestDTO.setPermissionIds(List.of(1L, 2L));

        userRoleDTO = new UserRoleDTO();
        userRoleDTO.setId(1L);
        userRoleDTO.setRoleId(1L);
        userRoleDTO.setUserId(1L);

        userAssignmentRequestDTO = new RoleUserAssignmentRequestDTO();
        userAssignmentRequestDTO.setUserIds(List.of(1L, 2L));
    }

    @Test
    void testCreateRole_Success() {
        when(roleService.createRole(any(RoleRequestDTO.class))).thenReturn(roleDTO);

        ResponseEntity<ApiResponse<RoleDTO>> response = roleController.createRole(roleRequestDTO);

        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals("Role created successfully", response.getBody().getMessage());
        assertEquals(roleDTO, response.getBody().getData());
        verify(roleService, times(1)).createRole(any(RoleRequestDTO.class));
    }

    @Test
    void testGetRoles_ByRoleId_Success() {
        when(roleService.findRoleById(anyLong())).thenReturn(Optional.of(roleDTO));

        ResponseEntity<ApiResponse<?>> response =
                roleController.getRoles(1L, null, 0, 10, "createdAt,desc");

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        Page<RoleDTO> data = (Page<RoleDTO>) response.getBody().getData();
        assertEquals(1, data.getContent().size());
        assertEquals(roleDTO, data.getContent().get(0));
        verify(roleService, times(1)).findRoleById(anyLong());
    }

    @Test
    void testGetRoles_WithFilters_Success() {
        Page<RoleDTO> rolePage = new PageImpl<>(Collections.singletonList(roleDTO));
        ArgumentCaptor<RoleFilterDTO> roleFilterCaptor =
                ArgumentCaptor.forClass(RoleFilterDTO.class);
        when(roleService.getRolesWithFilters(roleFilterCaptor.capture(), any()))
                .thenReturn(rolePage);

        ResponseEntity<ApiResponse<?>> response =
                roleController.getRoles(null, null, 0, 10, "createdAt,desc");

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        Page<RoleDTO> data = (Page<RoleDTO>) response.getBody().getData();
        assertEquals(1, data.getContent().size());
        assertEquals(roleDTO, data.getContent().get(0));
        verify(roleService, times(1)).getRolesWithFilters(any(RoleFilterDTO.class), any());
        RoleFilterDTO capturedFilter = roleFilterCaptor.getValue();
        assertNotNull(capturedFilter);
        assertNull(capturedFilter.getName());
    }

    @Test
    void testUpdateRole_Success() {
        when(roleService.updateRole(anyLong(), any(RoleRequestDTO.class))).thenReturn(roleDTO);

        ResponseEntity<ApiResponse<RoleDTO>> response =
                roleController.updateRole(1L, roleRequestDTO);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals("Role updated successfully", response.getBody().getMessage());
        assertEquals(roleDTO, response.getBody().getData());
        verify(roleService, times(1)).updateRole(anyLong(), any(RoleRequestDTO.class));
    }

    @Test
    void testDeleteRole_Success() {
        doNothing().when(roleService).deleteRole(anyLong());

        ResponseEntity<ApiResponse<Void>> response = roleController.deleteRole(1L);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals("Role deleted successfully", response.getBody().getMessage());
        verify(roleService, times(1)).deleteRole(anyLong());
    }

    @Test
    void testAssignPermissionsToRole_Success() {
        List<RolePermissionDTO> rolePermissions =
                List.of(new RolePermissionDTO(), new RolePermissionDTO());
        when(rolePermissionService.assignPermissionsToRole(anyLong(), anyList()))
                .thenReturn(rolePermissions);

        ResponseEntity<ApiResponse<List<RolePermissionDTO>>> response =
                roleController.assignPermissionsToRole(1L, permissionRequestDTO);

        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals("Permissions assigned to role successfully", response.getBody().getMessage());
        assertEquals(rolePermissions, response.getBody().getData());
        verify(rolePermissionService, times(1)).assignPermissionsToRole(anyLong(), anyList());
    }

    @Test
    void testGetRolePermissions_Success() {
        List<RolePermissionDTO> rolePermissions =
                List.of(new RolePermissionDTO(), new RolePermissionDTO());
        when(rolePermissionService.getRolePermissionsByParams(anyLong(), isNull()))
                .thenReturn(rolePermissions);

        ResponseEntity<ApiResponse<List<RolePermissionDTO>>> response =
                roleController.getRolePermissions(1L);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals(rolePermissions, response.getBody().getData());
        verify(rolePermissionService, times(1)).getRolePermissionsByParams(anyLong(), isNull());
    }

    @Test
    void testAssignRoleToUsers_Success() {
        List<UserRoleDTO> userRoles = List.of(new UserRoleDTO(), new UserRoleDTO());
        when(userRoleService.assignRoleToUser(any(UserRoleRequestDTO.class)))
                .thenReturn(userRoles.get(0), userRoles.get(1));

        ResponseEntity<ApiResponse<List<UserRoleDTO>>> response =
                roleController.assignRoleToUsers(1L, userAssignmentRequestDTO);

        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals("Role assigned to users successfully", response.getBody().getMessage());
        assertEquals(userRoles, response.getBody().getData());
        verify(userRoleService, times(2)).assignRoleToUser(any(UserRoleRequestDTO.class));
    }

    @Test
    void testRemoveRoleFromUser_Success() {
        doNothing().when(userRoleService).removeRoleFromUser(anyLong(), anyLong());

        ResponseEntity<ApiResponse<Void>> response = roleController.removeRoleFromUser(1L, 2L);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals("Role removed from user successfully", response.getBody().getMessage());
        verify(userRoleService, times(1)).removeRoleFromUser(anyLong(), anyLong());
    }
}
