package com.arealytics.areadocs.controller;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import com.arealytics.areadocs.dto.requestDTO.serv.PropertyRequest;
import com.arealytics.areadocs.dto.responseDTO.ApiResponse;
import com.arealytics.areadocs.dto.responseDTO.serv.ParcelResponse;
import com.arealytics.areadocs.dto.responseDTO.serv.PropertyResponse;
import com.arealytics.areadocs.service.ServApiService;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
class ServApiControllerTest {

    private MockMvc mockMvc;

    @InjectMocks private ServApiController servApiController;

    @Mock private ServApiService servApiService;

    @Mock private Validator validator;

    private static final String PROPERTY_PFI = "12345678";
    private static final String PROPERTY_NUMBER = "42";
    private static final String MUNICIPALITY_NAME = "TestTown";
    private static final String EZI_ADDRESS = "Residential";
    private static final String PARCEL_IDENTIFIER = "123456789012345678901234";

    private PropertyResponse propertyResponse;
    private ParcelResponse parcelResponse;

    @BeforeEach
    void setUp() {
        mockMvc =
                MockMvcBuilders.standaloneSetup(servApiController)
                        .build();

        propertyResponse = new PropertyResponse();
        PropertyResponse.PropertyDTO dto = new PropertyResponse.PropertyDTO();
        dto.setPropertyPfi(Long.valueOf("123456789"));
        propertyResponse.setPropertySummaries(List.of(dto));

        parcelResponse = new ParcelResponse();
        ParcelResponse.ParcelProperty parcelProperty = new ParcelResponse.ParcelProperty();
        parcelProperty.setPropertyPfi("12345");
        parcelResponse.setProperties(List.of(parcelProperty));

        // By default, no validation errors
        Mockito.lenient()
                .when(validator.validate(any(PropertyRequest.class)))
                .thenReturn(Collections.emptySet());

        ReflectionTestUtils.setField(servApiController, "validator", validator);
    }

    // --- PROPERTY SEARCH TESTS ---

    @Test
    void testGetProperties_AllParamsProvided_ReturnsOk() {
        String query = "propertyPfi=" + PROPERTY_PFI;

        when(servApiService.getProperties(eq(query), eq(PropertyResponse.class)))
                .thenReturn(ResponseEntity.ok(propertyResponse));

        ResponseEntity<ApiResponse<PropertyResponse>> response =
                servApiController.getProperties(PROPERTY_PFI, null, null, null);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("Properties retrieved successfully", response.getBody().getMessage());
        assertFalse(response.getBody().getData().getPropertySummaries().isEmpty());

        verify(servApiService).getProperties(eq(query), eq(PropertyResponse.class));
    }

    @Test
    void testGetProperties_NoParamsProvided_ReturnsBadRequest() {
        ResponseEntity<ApiResponse<PropertyResponse>> response =
                servApiController.getProperties(null, null, null, null);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(
                "At least one search parameter must be provided.", response.getBody().getMessage());

        verifyNoInteractions(servApiService);
    }

    @Test
    void testGetProperties_WithValidationErrors_ReturnsBadRequest() {
        Set<ConstraintViolation<PropertyRequest>> violations = new HashSet<>();
        ConstraintViolation<PropertyRequest> mockViolation = mock(ConstraintViolation.class);
        when(mockViolation.getMessage()).thenReturn("Invalid property number");
        violations.add(mockViolation);

        when(validator.validate(any(PropertyRequest.class))).thenReturn(violations);

        ResponseEntity<ApiResponse<PropertyResponse>> response =
                servApiController.getProperties(null, PROPERTY_NUMBER, null, null);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("Invalid property number", response.getBody().getMessage());

        verifyNoInteractions(servApiService);
    }

    @Test
    void testGetProperties_NoDataFound_ReturnsBadRequest() {
        PropertyResponse emptyResponse = new PropertyResponse();
        emptyResponse.setPropertySummaries(Collections.emptyList());

        when(servApiService.getProperties(anyString(), eq(PropertyResponse.class)))
                .thenReturn(ResponseEntity.ok(emptyResponse));

        ResponseEntity<ApiResponse<PropertyResponse>> response =
                servApiController.getProperties(PROPERTY_PFI, null, null, null);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        verify(servApiService).getProperties(anyString(), eq(PropertyResponse.class));
    }

    @Test
    void testGetProperties_EziAddressOnly_ReturnsOk() {
        String expectedQuery = "eziAddress=" + EZI_ADDRESS;

        when(servApiService.getProperties(eq(expectedQuery), eq(PropertyResponse.class)))
                .thenReturn(ResponseEntity.ok(propertyResponse));

        ResponseEntity<ApiResponse<PropertyResponse>> response =
                servApiController.getProperties(null, null, null, EZI_ADDRESS);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertFalse(response.getBody().getData().getPropertySummaries().isEmpty());

        verify(servApiService).getProperties(eq(expectedQuery), eq(PropertyResponse.class));
    }

    @Test
    void testGetProperties_MunicipalityNameAndPropertyNumberProvided_ReturnsOk() {
        String expectedQuery =
                "propertyNumber=" + PROPERTY_NUMBER + "&municipalityName=" + MUNICIPALITY_NAME;

        when(servApiService.getProperties(eq(expectedQuery), eq(PropertyResponse.class)))
                .thenReturn(ResponseEntity.ok(propertyResponse));

        ResponseEntity<ApiResponse<PropertyResponse>> response =
                servApiController.getProperties(null, PROPERTY_NUMBER, MUNICIPALITY_NAME, null);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertFalse(response.getBody().getData().getPropertySummaries().isEmpty());

        verify(servApiService).getProperties(eq(expectedQuery), eq(PropertyResponse.class));
    }

    @Test
    void testGetProperties_TooManyParams_ThrowsIllegalArgumentException() {
        IllegalArgumentException exception =
                assertThrows(
                        IllegalArgumentException.class,
                        () ->
                                servApiController.getProperties(
                                        PROPERTY_PFI,
                                        PROPERTY_NUMBER,
                                        MUNICIPALITY_NAME,
                                        EZI_ADDRESS));

        assertEquals("propertyPfi cannot be used with other parameters", exception.getMessage());
        verifyNoInteractions(servApiService);
    }

    @Test
    void testGetProperties_PropertyPfiWithOthers_ThrowsIllegalArgumentException() {
        IllegalArgumentException exception =
                assertThrows(
                        IllegalArgumentException.class,
                        () ->
                                servApiController.getProperties(
                                        PROPERTY_PFI, PROPERTY_NUMBER, null, null));

        assertEquals("propertyPfi cannot be used with other parameters", exception.getMessage());
        verifyNoInteractions(servApiService);
    }

    @Test
    void testGetProperties_MunicipalityNameWithoutPropertyNumber_ThrowsIllegalArgumentException() {
        IllegalArgumentException exception =
                assertThrows(
                        IllegalArgumentException.class,
                        () -> servApiController.getProperties(null, null, "MELBOURNE", null));

        assertEquals(
                "propertyNumber must be provided with municipalityName", exception.getMessage());
        verifyNoInteractions(servApiService);
    }

    @Test
    void testGetProperties_EziAddressWithOtherParams_ThrowsIllegalArgumentException() {
        IllegalArgumentException exception =
                assertThrows(
                        IllegalArgumentException.class,
                        () ->
                                servApiController.getProperties(
                                        null, PROPERTY_NUMBER, MUNICIPALITY_NAME, EZI_ADDRESS));

        assertEquals("eziAddress cannot be used with other parameters", exception.getMessage());
        verifyNoInteractions(servApiService);
    }

    @Test
    void testGetProperties_NullServiceResponse_ThrowsIllegalArgumentException() {
        when(servApiService.getProperties(anyString(), eq(PropertyResponse.class)))
                .thenReturn(null);

        IllegalArgumentException exception =
                assertThrows(
                        IllegalArgumentException.class,
                        () -> servApiController.getProperties(PROPERTY_PFI, null, null, null));

        assertEquals("Property response cannot be null", exception.getMessage());
        verify(servApiService)
                .getProperties(eq("propertyPfi=" + PROPERTY_PFI), eq(PropertyResponse.class));
    }

    @Test
    void testGetProperties_NullPropertyData_ReturnsBadRequest() {
        when(servApiService.getProperties(anyString(), eq(PropertyResponse.class)))
                .thenReturn(ResponseEntity.ok(null));

        ResponseEntity<ApiResponse<PropertyResponse>> response =
                servApiController.getProperties(PROPERTY_PFI, null, null, null);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        verify(servApiService)
                .getProperties(eq("propertyPfi=" + PROPERTY_PFI), eq(PropertyResponse.class));
    }

    @Test
    void testGetProperties_InvalidPropertyPfiFormat_ReturnsBadRequest() {
        Set<ConstraintViolation<PropertyRequest>> violations = new HashSet<>();
        ConstraintViolation<PropertyRequest> violation = mock(ConstraintViolation.class);
        when(violation.getMessage()).thenReturn("propertyPfi must be 1 to 20 digits");
        violations.add(violation);

        when(validator.validate(any(PropertyRequest.class))).thenReturn(violations);

        ResponseEntity<ApiResponse<PropertyResponse>> response =
                servApiController.getProperties("invalidPfi", null, null, null);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals("propertyPfi must be 1 to 20 digits", response.getBody().getMessage());
        verifyNoInteractions(servApiService);
    }

    @Test
    void testGetProperties_BoundaryLengthParameters_ReturnsOk() {
        String longPropertyNumber = "12345678901234567890"; // 20 digits
        String expectedQuery =
                "propertyNumber=" + longPropertyNumber + "&municipalityName=" + MUNICIPALITY_NAME;

        when(servApiService.getProperties(eq(expectedQuery), eq(PropertyResponse.class)))
                .thenReturn(ResponseEntity.ok(propertyResponse));

        ResponseEntity<ApiResponse<PropertyResponse>> response =
                servApiController.getProperties(null, longPropertyNumber, MUNICIPALITY_NAME, null);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertFalse(response.getBody().getData().getPropertySummaries().isEmpty());
        verify(servApiService).getProperties(eq(expectedQuery), eq(PropertyResponse.class));
    }

    // --- PARCEL SEARCH TESTS ---

    @Test
    void testGetParcel_ValidParcelIdentifier_ReturnsOk() throws Exception {
        when(servApiService.getParcel(eq(PARCEL_IDENTIFIER), eq(ParcelResponse.class)))
                .thenReturn(ResponseEntity.ok(parcelResponse));

        mockMvc.perform(get("/properties-by-parcel").param("parcelIdentifier", PARCEL_IDENTIFIER))
                .andExpect(status().isOk());

        verify(servApiService).getParcel(eq(PARCEL_IDENTIFIER), eq(ParcelResponse.class));
    }

    @Test
    void testGetParcel_NoPropertiesFound_ReturnsBadRequest() throws Exception {
        ParcelResponse emptyParcelResponse = new ParcelResponse();
        emptyParcelResponse.setProperties(Collections.emptyList());

        when(servApiService.getParcel(eq(PARCEL_IDENTIFIER), eq(ParcelResponse.class)))
                .thenReturn(ResponseEntity.ok(emptyParcelResponse));

        mockMvc.perform(get("/properties-by-parcel").param("parcelIdentifier", PARCEL_IDENTIFIER))
                .andExpect(status().isBadRequest());

        verify(servApiService).getParcel(eq(PARCEL_IDENTIFIER), eq(ParcelResponse.class));
    }

    @Test
    void testGetParcel_NullParcelResponse_ReturnsBadRequest() throws Exception {
        when(servApiService.getParcel(eq(PARCEL_IDENTIFIER), eq(ParcelResponse.class)))
                .thenReturn(ResponseEntity.ok(null));

        mockMvc.perform(get("/properties-by-parcel").param("parcelIdentifier", PARCEL_IDENTIFIER))
                .andExpect(status().isBadRequest());

        verify(servApiService).getParcel(eq(PARCEL_IDENTIFIER), eq(ParcelResponse.class));
    }

    @Test
    void testGetParcel_BlankParcelIdentifier_ReturnsBadRequest() throws Exception {
        mockMvc.perform(get("/properties-by-parcel").param("parcelIdentifier", ""))
                .andExpect(status().isBadRequest());

        verifyNoInteractions(servApiService);
    }

    @Test
    void testGetParcel_ParcelIdentifierTooShort_ReturnsBadRequest() throws Exception {

        mockMvc.perform(get("/properties-by-parcel").param("parcelIdentifier", ""))
                .andExpect(status().isBadRequest());

        verifyNoInteractions(servApiService);
    }

    @Test
    void testGetParcel_ParcelIdentifierTooLong_ReturnsBadRequest() throws Exception {
        String tooLongParcelIdentifier = "1234567890123456789012345";

        mockMvc.perform(
                        get("/properties-by-parcel")
                                .param("parcelIdentifier", tooLongParcelIdentifier))
                .andExpect(status().isBadRequest());

        verifyNoInteractions(servApiService);
    }
}
