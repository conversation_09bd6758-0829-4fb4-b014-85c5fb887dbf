package com.arealytics.areadocs.controller;

import java.util.Collections;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import com.arealytics.areadocs.dto.requestDTO.CompanyFilterDTO;
import com.arealytics.areadocs.dto.requestDTO.CompanyMembershipRequestDTO;
import com.arealytics.areadocs.dto.requestDTO.CompanyRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.ApiResponse;
import com.arealytics.areadocs.dto.responseDTO.CompanyDTO;
import com.arealytics.areadocs.dto.responseDTO.CompanyMembershipDTO;
import com.arealytics.areadocs.mapper.CompanyRequestMapper;
import com.arealytics.areadocs.service.CompanyMembershipService;
import com.arealytics.areadocs.service.CompanyService;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CompanyControllerTest {

    @Mock private CompanyService companyService;
    @Mock private CompanyMembershipService companyMembershipService;
    @Mock private CompanyRequestMapper companyRequestMapper;

    @InjectMocks private CompanyController companyController;

    private CompanyDTO companyDTO;
    private CompanyRequestDTO companyRequestDTO;
    private CompanyMembershipDTO companyMembershipDTO;
    private CompanyMembershipRequestDTO companyMembershipRequestDTO;

    @BeforeEach
    void setUp() {
        companyDTO = new CompanyDTO();
        companyDTO.setId(1L);
        companyDTO.setName("Zessta");
        companyDTO.setABN("ZES123");
        companyDTO.setACN("ZES456");
        companyDTO.setIndustry("Tech");
        companyDTO.setEmployeeCount(100);
        companyDTO.setIsActive(true);
        companyDTO.setBillingEmail("<EMAIL>");
        companyDTO.setAccountsContactName("John Doe");
        companyDTO.setAccountsContactNumber("**********");

        companyRequestDTO = new CompanyRequestDTO();
        companyRequestDTO.setName("Zessta");
        companyRequestDTO.setABN("ZES123");
        companyRequestDTO.setACN("ZES456");
        companyRequestDTO.setIndustry("Tech");
        companyRequestDTO.setEmployeeCount(100);
        companyRequestDTO.setBillingEmail("<EMAIL>");

        companyMembershipDTO = new CompanyMembershipDTO();
        companyMembershipDTO.setId(1L);
        companyMembershipDTO.setCompanyId(1L);
        companyMembershipDTO.setRoleId(1L);

        companyMembershipRequestDTO = new CompanyMembershipRequestDTO();
        companyMembershipRequestDTO.setCompanyId(1L);
        companyMembershipRequestDTO.setUserId(1L);
        companyMembershipRequestDTO.setRoleId(1L);
    }

    @Test
    void testGetCompanies_WithFilters_Success() {
        Page<CompanyDTO> companyPage = new PageImpl<>(Collections.singletonList(companyDTO));
        when(companyService.getCompaniesWithFilters(any(CompanyFilterDTO.class), any()))
                .thenReturn(companyPage);

        ResponseEntity<ApiResponse<Page<CompanyDTO>>> response =
                companyController.getCompanies(
                        null,
                        "Zessta",
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        0,
                        10,
                        "createdAt,desc");

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals(companyPage, response.getBody().getData());
        verify(companyService, times(1))
                .getCompaniesWithFilters(any(CompanyFilterDTO.class), any());
    }

    @Test
    void testDeleteCompany_Success() {
        doNothing().when(companyService).deleteCompany(anyLong());

        ResponseEntity<ApiResponse<Void>> response = companyController.deleteCompany(1L);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals("Company deleted successfully", response.getBody().getMessage());
        verify(companyService, times(1)).deleteCompany(anyLong());
    }

    /*
    @Test
    void testAddUserToCompany_Success() {
        when(companyService.getCompanyById(anyLong())).thenReturn(companyDTO);
        when(companyMembershipService.createCompanyMembership(
                        any(CompanyMembershipRequestDTO.class)))
                .thenReturn(companyMembershipDTO);

        ResponseEntity<ApiResponse<CompanyMembershipDTO>> response =
                companyController.addUserToCompany(1L, 1L, 1L);

        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals("User successfully added to company", response.getBody().getMessage());
        assertEquals(companyMembershipDTO, response.getBody().getData());
        verify(companyMembershipService, times(1))
                .createCompanyMembership(any(CompanyMembershipRequestDTO.class));
    }

    @Test
    void testGetCompanyMemberships_Success() {
        when(companyService.getCompanyById(anyLong())).thenReturn(companyDTO);
        when(companyMembershipService.getCompanyMembershipsByCompanyId(anyLong()))
                .thenReturn(Collections.singletonList(companyMembershipDTO));

        ResponseEntity<ApiResponse<Page<CompanyMembershipDTO>>> response =
                companyController.getCompanyMemberships(1L, null, 0, 10, "joinedAt,desc");

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals(1, response.getBody().getData().getContent().size());
        assertEquals(companyMembershipDTO, response.getBody().getData().getContent().get(0));
        verify(companyMembershipService, times(1)).getCompanyMembershipsByCompanyId(anyLong());
    }

    @Test
    void testGetCompanyMembershipById_Success() {
        when(companyMembershipService.getCompanyMembershipById(anyLong()))
                .thenReturn(companyMembershipDTO);

        ResponseEntity<ApiResponse<CompanyMembershipDTO>> response =
                companyController.getCompanyMembershipById(1L);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals(companyMembershipDTO, response.getBody().getData());
        verify(companyMembershipService, times(1)).getCompanyMembershipById(anyLong());
    }

    @Test
    void testGetCompanyMembershipById_NotFound() {
        when(companyMembershipService.getCompanyMembershipById(anyLong()))
                .thenThrow(new EntityNotFoundException("Membership not found"));

        ResponseEntity<ApiResponse<CompanyMembershipDTO>> response =
                companyController.getCompanyMembershipById(1L);

        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertFalse(response.getBody().isSuccess());
        assertEquals("Membership not found", response.getBody().getMessage());
    }

    @Test
    void testUpdateCompanyMembership_Success() {
        when(companyMembershipService.updateCompanyMembership(
                        anyLong(), any(CompanyMembershipRequestDTO.class)))
                .thenReturn(companyMembershipDTO);

        ResponseEntity<ApiResponse<CompanyMembershipDTO>> response =
                companyController.updateCompanyMembership(1L, companyMembershipRequestDTO);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals("Company membership updated successfully", response.getBody().getMessage());
        assertEquals(companyMembershipDTO, response.getBody().getData());
        verify(companyMembershipService, times(1))
                .updateCompanyMembership(anyLong(), any(CompanyMembershipRequestDTO.class));
    }

    @Test
    void testDeleteCompanyMembership_Success() {
        doNothing().when(companyMembershipService).deleteCompanyMembership(anyLong());

        ResponseEntity<ApiResponse<Void>> response = companyController.deleteCompanyMembership(1L);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals("Company membership deleted successfully", response.getBody().getMessage());
        verify(companyMembershipService, times(1)).deleteCompanyMembership(anyLong());
    }

    @Test
    void testDeleteCompanyMembership_NotFound() {
        doThrow(new EntityNotFoundException("Membership not found"))
                .when(companyMembershipService)
                .deleteCompanyMembership(anyLong());

        ResponseEntity<ApiResponse<Void>> response = companyController.deleteCompanyMembership(1L);

        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertFalse(response.getBody().isSuccess());
        assertEquals("Membership not found", response.getBody().getMessage());
    }
    */
}
