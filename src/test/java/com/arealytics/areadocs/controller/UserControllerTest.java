package com.arealytics.areadocs.controller;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import com.arealytics.areadocs.dto.requestDTO.UserRequestDTO;
import com.arealytics.areadocs.dto.requestDTO.UserSchemaFilterDTO;
import com.arealytics.areadocs.dto.responseDTO.ApiResponse;
import com.arealytics.areadocs.dto.responseDTO.UserResponseDTO;
import com.arealytics.areadocs.enumeration.SortOrder;
import com.arealytics.areadocs.enumeration.UserType;
import com.arealytics.areadocs.service.UserService;

import jakarta.persistence.EntityNotFoundException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserControllerTest {

    @Mock private UserService userService;

    @InjectMocks private UserController userController;

    private UserResponseDTO userDTO;
    private UserRequestDTO userRequestDTO;
    private List<UserResponseDTO> userDTOList;
    private Page<UserResponseDTO> userDTOPage;
    private String profilePictureUrl;

    @BeforeEach
    void setUp() {
        // Initialize profilePictureUrl
        profilePictureUrl =
                "https://areadocs-test.s3.amazonaws.com/user-profile-images/<EMAIL>";

        // Create a sample UserResponseDTO for testing
        userDTO = new UserResponseDTO();
        userDTO.setId(1L);
        userDTO.setEmail("<EMAIL>");
        userDTO.setFirstName("John");
        userDTO.setLastName("Doe");
        userDTO.setContactNumber("1234567890");
        userDTO.setUserType(UserType.INDIVIDUAL.name());
        userDTO.setCreatedAt(LocalDateTime.now());
        userDTO.setModifiedAt(LocalDateTime.now());
        userDTO.setProfilePictureUrl(profilePictureUrl);

        userRequestDTO = new UserRequestDTO();
        userRequestDTO.setEmail("<EMAIL>");
        userRequestDTO.setFirstName("John");
        userRequestDTO.setLastName("Doe");
        userRequestDTO.setContactNumber("1234567890");
        userRequestDTO.setUserType(UserType.INDIVIDUAL);
        userRequestDTO.setPassword("newPassword");
        userRequestDTO.setProfilePictureUrl(profilePictureUrl);

        // Create a list of UserDTOs for testing
        UserResponseDTO userDTO2 = new UserResponseDTO();
        userDTO2.setId(2L);
        userDTO2.setEmail("<EMAIL>");
        userDTO2.setFirstName("Jane");
        userDTO2.setLastName("Smith");
        userDTO2.setUserType(UserType.COMPANY.name());

        userDTOList = Arrays.asList(userDTO, userDTO2);

        // Create a Page of UserDTOs for testing
        Pageable pageable = PageRequest.of(0, 10);
        userDTOPage = new PageImpl<>(userDTOList, pageable, userDTOList.size());
    }

    @Test
    void createUser_ShouldReturnCreatedUser() {
        // Arrange
        UserRequestDTO requestDTO = new UserRequestDTO();
        requestDTO.setEmail("<EMAIL>");
        requestDTO.setPassword("password");
        requestDTO.setFirstName("John");
        requestDTO.setLastName("Doe");
        requestDTO.setUserType(UserType.INDIVIDUAL);
        requestDTO.setProfilePictureUrl(profilePictureUrl);

        when(userService.createUser(any(UserRequestDTO.class), anyString())).thenReturn(userDTO);
        // Act
        ResponseEntity<?> response = userController.createUser(requestDTO);

        // Assert
        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().toString().contains("User created successfully"));
        verify(userService, times(1)).createUser(any(UserRequestDTO.class), eq(profilePictureUrl));
    }

    @Test
    void createUser_WithCompanyIdZeroAndUserTypeIndividual_ShouldReturnCreatedUser() {
        // Arrange
        UserRequestDTO requestDTO = new UserRequestDTO();
        requestDTO.setEmail("<EMAIL>");
        requestDTO.setPassword("password");
        requestDTO.setFirstName("John");
        requestDTO.setLastName("Doe");
        requestDTO.setUserType(UserType.INDIVIDUAL);
        requestDTO.setCompanyId(0L);
        requestDTO.setProfilePictureUrl(profilePictureUrl);

        when(userService.createUser(any(UserRequestDTO.class), anyString())).thenReturn(userDTO);
        // Act
        ResponseEntity<?> response = userController.createUser(requestDTO);

        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().toString().contains("User created successfully"));
        verify(userService, times(1)).createUser(any(UserRequestDTO.class), eq(profilePictureUrl));
    }

    @Test
    void createUser_WithCompanyIdAndNoUserType_ShouldSetUserTypeToCompany() {
        // Arrange
        UserRequestDTO requestDTO = new UserRequestDTO();
        requestDTO.setEmail("<EMAIL>");
        requestDTO.setPassword("password");
        requestDTO.setFirstName("John");
        requestDTO.setLastName("Doe");
        requestDTO.setCompanyId(1L); // Set companyId
        requestDTO.setProfilePictureUrl(profilePictureUrl);
        // userType is null

        when(userService.createUser(any(UserRequestDTO.class), anyString())).thenReturn(userDTO);
        // Act
        ResponseEntity<?> response = userController.createUser(requestDTO);

        // Assert
        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().toString().contains("User created successfully"));
        verify(userService, times(1))
                .createUser(
                        argThat(dto -> dto.getUserType() == UserType.COMPANY),
                        eq(profilePictureUrl));
    }

    @Test
    void createUser_WithNoCompanyIdAndNoUserType_ShouldSetUserTypeToIndividual() {
        // Arrange
        UserRequestDTO requestDTO = new UserRequestDTO();
        requestDTO.setEmail("<EMAIL>");
        requestDTO.setPassword("password");
        requestDTO.setFirstName("John");
        requestDTO.setLastName("Doe");
        requestDTO.setProfilePictureUrl(profilePictureUrl);
        // companyId and userType are null

        when(userService.createUser(any(UserRequestDTO.class), anyString())).thenReturn(userDTO);
        // Act
        ResponseEntity<?> response = userController.createUser(requestDTO);

        // Assert
        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().toString().contains("User created successfully"));
        verify(userService, times(1))
                .createUser(
                        argThat(dto -> dto.getUserType() == UserType.INDIVIDUAL),
                        eq(profilePictureUrl));
    }

    @Test
    void getUsers_WithId_ShouldReturnSingleUser() {
        // Arrange
        when(userService.getUserById(anyLong())).thenReturn(userDTO);

        // Act
        ResponseEntity<?> response =
                userController.getUsers(1L, null, null, null, 0, 10, "id", SortOrder.ASCEND);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().toString().contains("Users retrieved successfully"));
        verify(userService, times(1)).getUserById(1L);
        verify(userService, never()).getUserByEmail(anyString());
        verify(userService, never()).getUsers(any(), any());
        verify(userService, never()).getAllUsers(any(Pageable.class));
    }

    @Test
    void getUsers_WithEmail_ShouldReturnSingleUser() {
        // Arrange
        when(userService.getUserByEmail(anyString())).thenReturn(userDTO);

        // Act
        ResponseEntity<?> response =
                userController.getUsers(
                        null, "<EMAIL>", null, null, 0, 10, "id", SortOrder.ASCEND);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().toString().contains("Users retrieved successfully"));
        verify(userService, never()).getUserById(anyLong());
        verify(userService, times(1)).getUserByEmail("<EMAIL>");
        verify(userService, never()).getUsers(any(), any());
        verify(userService, never()).getAllUsers(any(Pageable.class));
    }

    @Test
    void getUsers_WithCompanyId_ShouldReturnUsersFromCompany() {
        // Arrange
        when(userService.getUsersByCompanyId(anyLong(), any(Pageable.class)))
                .thenReturn(userDTOPage);

        // Act
        ResponseEntity<?> response =
                userController.getUsers(null, null, 1L, null, 0, 10, "id", SortOrder.ASCEND);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().toString().contains("Users retrieved successfully"));
        verify(userService, never()).getUserById(anyLong());
        verify(userService, never()).getUserByEmail(anyString());
        verify(userService, times(1)).getUsersByCompanyId(eq(1L), any(Pageable.class));
        verify(userService, never()).getUsers(any(UserSchemaFilterDTO.class), any(Pageable.class));
        verify(userService, never()).getAllUsers(any(Pageable.class));
    }

    @Test
    void getUsers_WithUserType_ShouldReturnFilteredUsers() {
        // Arrange
        when(userService.getUsers(any(UserSchemaFilterDTO.class), any(Pageable.class)))
                .thenReturn(userDTOPage);

        // Act
        ResponseEntity<?> response =
                userController.getUsers(
                        null, null, null, UserType.INDIVIDUAL, 0, 10, "id", SortOrder.ASCEND);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().toString().contains("Users retrieved successfully"));
        verify(userService, never()).getUserById(anyLong());
        verify(userService, never()).getUserByEmail(anyString());
        verify(userService, times(1)).getUsers(any(UserSchemaFilterDTO.class), any(Pageable.class));
        verify(userService, never()).getAllUsers(any(Pageable.class));
        verify(userService, never()).getUsersByCompanyId(anyLong(), any(Pageable.class));
    }

    @Test
    void getUsers_WithNoParameters_ShouldReturnAllUsers() {
        // Arrange
        when(userService.getAllUsers(any(Pageable.class))).thenReturn(userDTOPage);

        // Act
        ResponseEntity<?> response =
                userController.getUsers(null, null, null, null, 0, 10, "id", SortOrder.ASCEND);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().toString().contains("Users retrieved successfully"));
        verify(userService, never()).getUserById(anyLong());
        verify(userService, never()).getUserByEmail(anyString());
        verify(userService, never()).getUsers(any(), any());
        verify(userService, times(1)).getAllUsers(any(Pageable.class));
    }

    @Test
    void updateUser_ShouldReturnUpdatedUser() {
        // Arrange
        when(userService.updateUser(anyLong(), any(UserRequestDTO.class), anyString()))
                .thenReturn(userDTO);

        ResponseEntity<ApiResponse<UserResponseDTO>> response =
                userController.updateUser(1L, userRequestDTO);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().getMessage().contains("User updated successfully"));
        verify(userService, times(1))
                .updateUser(eq(1L), any(UserRequestDTO.class), eq(profilePictureUrl));
    }

    @Test
    void deleteUser_ShouldReturnSuccessMessage() {
        // Arrange
        doNothing().when(userService).deleteUser(anyLong());

        // Act
        ResponseEntity<?> response = userController.deleteUser(1L);

        // Assert
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().toString().contains("User deleted successfully"));
        verify(userService, times(1)).deleteUser(1L);
    }

    @Test
    void getUserById_WhenUserNotFound_ShouldThrowException() {
        // Arrange
        when(userService.getUserById(anyLong()))
                .thenThrow(new EntityNotFoundException("User not found with id: 999"));

        // Act & Assert
        Exception exception =
                assertThrows(
                        EntityNotFoundException.class,
                        () -> {
                            userController.getUsers(
                                    999L, null, null, null, 0, 10, "id", SortOrder.ASCEND);
                        });

        assertEquals("User not found with id: 999", exception.getMessage());
        verify(userService, times(1)).getUserById(999L);
    }

    @Test
    void getUserByEmail_WhenUserNotFound_ShouldThrowException() {
        // Arrange
        when(userService.getUserByEmail(anyString()))
                .thenThrow(
                        new EntityNotFoundException(
                                "User not found with email: <EMAIL>"));

        // Act & Assert
        Exception exception =
                assertThrows(
                        EntityNotFoundException.class,
                        () -> {
                            userController.getUsers(
                                    null,
                                    "<EMAIL>",
                                    null,
                                    null,
                                    0,
                                    10,
                                    "id",
                                    SortOrder.ASCEND);
                        });

        assertEquals("User not found with email: <EMAIL>", exception.getMessage());
        verify(userService, times(1)).getUserByEmail("<EMAIL>");
    }

    @Test
    void createUser_WithValidationError_ShouldReturnBadRequest() {
        // Arrange
        UserRequestDTO requestDTO = new UserRequestDTO();
        requestDTO.setEmail("<EMAIL>");
        requestDTO.setPassword("password");
        requestDTO.setFirstName("John");
        requestDTO.setLastName("Doe");
        requestDTO.setProfilePictureUrl(profilePictureUrl);

        when(userService.createUser(any(UserRequestDTO.class), anyString()))
                .thenThrow(new IllegalArgumentException("Email already exists: <EMAIL>"));

        // Act
        ResponseEntity<?> response = userController.createUser(requestDTO);

        // Assert
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(
                response.getBody()
                        .toString()
                        .contains("Validation error: Email already exists: <EMAIL>"));
        verify(userService, times(1)).createUser(any(UserRequestDTO.class), eq(profilePictureUrl));
    }

    @Test
    void createUser_WithUnexpectedError_ShouldReturnInternalServerError() {
        // Arrange
        UserRequestDTO requestDTO = new UserRequestDTO();
        requestDTO.setEmail("<EMAIL>");
        requestDTO.setPassword("password");
        requestDTO.setFirstName("John");
        requestDTO.setLastName("Doe");
        requestDTO.setProfilePictureUrl(profilePictureUrl);

        when(userService.createUser(any(UserRequestDTO.class), anyString()))
                .thenThrow(new RuntimeException("Database connection failed"));

        // Act
        ResponseEntity<?> response = userController.createUser(requestDTO);

        // Assert
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertNotNull(response.getBody());
        assertTrue(
                response.getBody()
                        .toString()
                        .contains("Error creating user: Database connection failed"));
        verify(userService, times(1)).createUser(any(UserRequestDTO.class), eq(profilePictureUrl));
    }
}
