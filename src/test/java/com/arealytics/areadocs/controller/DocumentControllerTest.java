package com.arealytics.areadocs.controller;

import java.math.BigDecimal;
import java.util.Collections;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import com.arealytics.areadocs.dto.requestDTO.DocumentFilterDTO;
import com.arealytics.areadocs.dto.requestDTO.DocumentFolderRequestDTO;
import com.arealytics.areadocs.dto.requestDTO.DocumentRequestDTO;
import com.arealytics.areadocs.dto.requestDTO.DocumentSourceRequestDTO;
import com.arealytics.areadocs.dto.requestDTO.FolderFilterDTO;
import com.arealytics.areadocs.dto.responseDTO.ApiResponse;
import com.arealytics.areadocs.dto.responseDTO.DocumentCategoryDTO;
import com.arealytics.areadocs.dto.responseDTO.DocumentDTO;
import com.arealytics.areadocs.dto.responseDTO.DocumentFolderDTO;
import com.arealytics.areadocs.dto.responseDTO.DocumentSourceDTO;
import com.arealytics.areadocs.service.DocumentCategoryService;
import com.arealytics.areadocs.service.DocumentFolderService;
import com.arealytics.areadocs.service.DocumentService;
import com.arealytics.areadocs.service.DocumentSourceService;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DocumentControllerTest {

    @Mock private DocumentService documentService;

    @Mock private DocumentFolderService documentFolderService;

    @Mock private DocumentCategoryService documentCategoryService;

    @Mock private DocumentSourceService documentSourceService;

    @InjectMocks private DocumentController documentController;

    private DocumentDTO documentDTO;
    private DocumentFolderDTO documentFolderDTO;
    private DocumentSourceDTO documentSourceDTO;
    private DocumentCategoryDTO documentCategoryDTO;
    private DocumentRequestDTO documentRequestDTO;
    private DocumentFolderRequestDTO documentFolderRequestDTO;
    private DocumentSourceRequestDTO documentSourceRequestDTO;

    @BeforeEach
    void setUp() {
        documentDTO = new DocumentDTO();
        documentDTO.setId(1L);
        documentDTO.setTitle("HR_Policy_Manual");
        documentDTO.setBasePrice(BigDecimal.valueOf(100));
        documentDTO.setFinalPrice(BigDecimal.valueOf(120));

        documentFolderDTO = new DocumentFolderDTO();
        documentFolderDTO.setId(1L);
        documentFolderDTO.setName("HR_Documents");

        documentSourceDTO = new DocumentSourceDTO();
        documentSourceDTO.setId(1L);
        documentSourceDTO.setName("HRSoftware");

        documentCategoryDTO = new DocumentCategoryDTO();
        documentCategoryDTO.setId(1L);
        documentCategoryDTO.setName("Human Resources");

        documentRequestDTO = new DocumentRequestDTO();
        documentRequestDTO.setTitle("New Document");
        documentRequestDTO.setFolderId(1L);
        documentRequestDTO.setSourceId(1L);
        documentRequestDTO.setCategoryId(1L);

        documentFolderRequestDTO = new DocumentFolderRequestDTO();
        documentFolderRequestDTO.setName("New Folder");

        documentSourceRequestDTO = new DocumentSourceRequestDTO();
        documentSourceRequestDTO.setName("New Source");
    }

    // Document Tests
    @Test
    void testGetDocuments_ByDocumentId_Success() {
        Page<DocumentDTO> expectedPage = new PageImpl<>(Collections.singletonList(documentDTO));
        when(documentService.getDocumentById(anyLong())).thenReturn(documentDTO);

        ResponseEntity<ApiResponse<Page<DocumentDTO>>> response =
                documentController.getDocuments(
                        1L,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        0,
                        10,
                        "createdAt,desc");

        assertEquals(HttpStatus.OK, response.getStatusCode(), "Expected HTTP 200 OK");
        assertTrue(response.getBody().isSuccess(), "Response should indicate success");
        assertEquals(expectedPage, response.getBody().getData(), "Expected page with one document");
        verify(documentService, times(1)).getDocumentById(1L);
    }

    @Test
    void testGetDocuments_WithFilters_Success() {
        Page<DocumentDTO> documentPage = new PageImpl<>(Collections.singletonList(documentDTO));
        when(documentService.getDocumentsWithFilters(any(DocumentFilterDTO.class), any()))
                .thenReturn(documentPage);

        ResponseEntity<ApiResponse<Page<DocumentDTO>>> response =
                documentController.getDocuments(
                        null,
                        "HR_Policy_Manual",
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        0,
                        10,
                        "createdAt,desc");

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals(documentPage, response.getBody().getData());
        verify(documentService, times(1))
                .getDocumentsWithFilters(any(DocumentFilterDTO.class), any());
    }

    @Test
    void testGetDocuments_Exception() {
        when(documentService.getDocumentsWithFilters(any(DocumentFilterDTO.class), any()))
                .thenThrow(new RuntimeException("Error"));

        ResponseEntity<ApiResponse<Page<DocumentDTO>>> response =
                documentController.getDocuments(
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        null,
                        0,
                        10,
                        "createdAt,desc");

        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());
        assertFalse(response.getBody().isSuccess());
        assertTrue(
                response.getBody()
                        .getMessage()
                        .contains("An error occurred while retrieving documents"));
    }

    @Test
    void testCreateDocument_Success() {
        when(documentService.createDocument(any(DocumentRequestDTO.class))).thenReturn(documentDTO);

        ResponseEntity<ApiResponse<DocumentDTO>> response =
                documentController.createDocument(documentRequestDTO);

        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals(documentDTO, response.getBody().getData());
        verify(documentService, times(1)).createDocument(any(DocumentRequestDTO.class));
    }

    @Test
    void testUpdateDocument_Success() {
        when(documentService.updateDocument(anyLong(), any(DocumentRequestDTO.class)))
                .thenReturn(documentDTO);

        ResponseEntity<ApiResponse<DocumentDTO>> response =
                documentController.updateDocument(1L, documentRequestDTO);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals(documentDTO, response.getBody().getData());
        verify(documentService, times(1)).updateDocument(eq(1L), any(DocumentRequestDTO.class));
    }

    @Test
    void testDeleteDocument_Success() {
        doNothing().when(documentService).deleteDocument(anyLong());

        ResponseEntity<String> response = documentController.deleteDocument(1L);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals("Document deleted successfully!", response.getBody());
        verify(documentService, times(1)).deleteDocument(1L);
    }

    // Document Folder Tests
    @Test
    void testCreateDocumentFolder_Success() {
        when(documentFolderService.createDocumentFolder(any(DocumentFolderRequestDTO.class)))
                .thenReturn(documentFolderDTO);

        ResponseEntity<DocumentFolderDTO> response =
                documentController.createDocumentFolder(documentFolderRequestDTO);

        assertEquals(HttpStatus.CREATED, response.getStatusCode());
        assertEquals(documentFolderDTO, response.getBody());
        verify(documentFolderService, times(1))
                .createDocumentFolder(any(DocumentFolderRequestDTO.class));
    }

    @Test
    void testGetDocumentFolders_ByFolderId_Success() {
        when(documentFolderService.getDocumentFolderById(anyLong())).thenReturn(documentFolderDTO);

        ResponseEntity<ApiResponse<Page<DocumentFolderDTO>>> response =
                documentController.getDocumentFolders(
                        1L, null, null, null, 0, 10, "createdAt,desc");

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals(1, response.getBody().getData().getContent().size());
        assertEquals(documentFolderDTO, response.getBody().getData().getContent().get(0));
        verify(documentFolderService, times(1)).getDocumentFolderById(1L);
    }

    @Test
    void testGetDocumentFolders_WithFilters_Success() {
        Page<DocumentFolderDTO> folderPage =
                new PageImpl<>(Collections.singletonList(documentFolderDTO));
        when(documentFolderService.getFoldersWithFilters(any(FolderFilterDTO.class), any()))
                .thenReturn(folderPage);

        ResponseEntity<ApiResponse<Page<DocumentFolderDTO>>> response =
                documentController.getDocumentFolders(
                        null, "HR_Documents", null, null, 0, 10, "createdAt,desc");

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals(folderPage, response.getBody().getData());
        verify(documentFolderService, times(1))
                .getFoldersWithFilters(any(FolderFilterDTO.class), any());
    }

    @Test
    void testUpdateDocumentFolder_Success() {
        when(documentFolderService.updateDocumentFolder(
                        anyLong(), any(DocumentFolderRequestDTO.class)))
                .thenReturn(documentFolderDTO);

        ResponseEntity<DocumentFolderDTO> response =
                documentController.updateDocumentFolder(1L, documentFolderRequestDTO);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(documentFolderDTO, response.getBody());
        verify(documentFolderService, times(1))
                .updateDocumentFolder(eq(1L), any(DocumentFolderRequestDTO.class));
    }

    @Test
    void testDeleteDocumentFolder_Success() {
        doNothing().when(documentFolderService).deleteDocumentFolder(anyLong());

        ResponseEntity<Void> response = documentController.deleteDocumentFolder(1L);

        assertEquals(HttpStatus.NO_CONTENT, response.getStatusCode());
        verify(documentFolderService, times(1)).deleteDocumentFolder(1L);
    }

    /*
    // Document Source Tests
    @Test
    void testCreateDocumentSource_Success() {
        when(documentSourceService.createDocumentSource(any(DocumentSourceRequestDTO.class)))
                .thenReturn(documentSourceDTO);

        ResponseEntity<DocumentSourceDTO> response =
                documentController.createDocumentSource(documentSourceRequestDTO);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(documentSourceDTO, response.getBody());
        verify(documentSourceService, times(1))
                .createDocumentSource(any(DocumentSourceRequestDTO.class));
    }

    @Test
    void testGetDocumentSources_ById_Success() {
        when(documentSourceService.getDocumentSourceById(anyLong())).thenReturn(documentSourceDTO);

        ResponseEntity<ApiResponse<Page<DocumentSourceDTO>>> response =
                documentController.getDocumentSources(
                        1L, null, null, null, 0, 10, "createdAt,desc");

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals(1, response.getBody().getData().getContent().size());
        assertEquals(documentSourceDTO, response.getBody().getData().getContent().get(0));
        verify(documentSourceService, times(1)).getDocumentSourceById(1L);
    }

    @Test
    void testGetDocumentSources_WithFilters_Success() {
        Page<DocumentSourceDTO> sourcePage =
                new PageImpl<>(Collections.singletonList(documentSourceDTO));
        when(documentSourceService.getDocumentSourcesWithFilters(
                        any(DocumentSourceFilterDTO.class), any()))
                .thenReturn(sourcePage);

        ResponseEntity<ApiResponse<Page<DocumentSourceDTO>>> response =
                documentController.getDocumentSources(
                        null, "HRSoftware", null, null, 0, 10, "createdAt,desc");

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals(sourcePage, response.getBody().getData());
        verify(documentSourceService, times(1))
                .getDocumentSourcesWithFilters(any(DocumentSourceFilterDTO.class), any());
    }

    @Test
    void testUpdateDocumentSource_Success() {
        when(documentSourceService.updateDocumentSource(
                        anyLong(), any(DocumentSourceRequestDTO.class)))
                .thenReturn(documentSourceDTO);

        ResponseEntity<DocumentSourceDTO> response =
                documentController.updateDocumentSource(1L, documentSourceRequestDTO);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(documentSourceDTO, response.getBody());
        verify(documentSourceService, times(1))
                .updateDocumentSource(eq(1L), any(DocumentSourceRequestDTO.class));
    }

    @Test
    void testDeleteDocumentSource_Success() {
        doNothing().when(documentSourceService).deleteDocumentSource(anyLong());

        ResponseEntity<Void> response = documentController.deleteDocumentSource(1L);

        assertEquals(HttpStatus.NO_CONTENT, response.getStatusCode());
        verify(documentSourceService, times(1)).deleteDocumentSource(1L);
    }

    // Document Category Tests
    @Test
    void testCreateDocumentCategory_Success() {
        when(documentCategoryService.createDocumentCategory(any(DocumentCategoryDTO.class)))
                .thenReturn(documentCategoryDTO);

        ResponseEntity<DocumentCategoryDTO> response =
                documentController.createDocumentCategory(documentCategoryDTO);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(documentCategoryDTO, response.getBody());
        verify(documentCategoryService, times(1))
                .createDocumentCategory(any(DocumentCategoryDTO.class));
    }

    @Test
    void testGetDocumentCategories_ById_Success() {
        when(documentCategoryService.getDocumentCategoryById(anyLong()))
                .thenReturn(documentCategoryDTO);

        ResponseEntity<ApiResponse<Page<DocumentCategoryDTO>>> response =
                documentController.getDocumentCategories(
                        1L, null, null, null, 0, 10, "createdAt,desc");

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals(1, response.getBody().getData().getContent().size());
        assertEquals(documentCategoryDTO, response.getBody().getData().getContent().get(0));
        verify(documentCategoryService, times(1)).getDocumentCategoryById(1L);
    }

    @Test
    void testGetDocumentCategories_WithFilters_Success() {
        Page<DocumentCategoryDTO> categoryPage =
                new PageImpl<>(Collections.singletonList(documentCategoryDTO));
        when(documentCategoryService.getDocumentCategoriesWithFilters(
                        any(DocumentCategoryFilterDTO.class), any()))
                .thenReturn(categoryPage);

        ResponseEntity<ApiResponse<Page<DocumentCategoryDTO>>> response =
                documentController.getDocumentCategories(
                        null, "Human Resources", null, null, 0, 10, "createdAt,desc");

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertTrue(response.getBody().isSuccess());
        assertEquals(categoryPage, response.getBody().getData());
        verify(documentCategoryService, times(1))
                .getDocumentCategoriesWithFilters(any(DocumentCategoryFilterDTO.class), any());
    }

    @Test
    void testUpdateDocumentCategory_Success() {
        when(documentCategoryService.updateDocumentCategory(
                        anyLong(), any(DocumentCategoryDTO.class)))
                .thenReturn(documentCategoryDTO);

        ResponseEntity<DocumentCategoryDTO> response =
                documentController.updateDocumentCategory(1L, documentCategoryDTO);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals(documentCategoryDTO, response.getBody());
        verify(documentCategoryService, times(1))
                .updateDocumentCategory(eq(1L), any(DocumentCategoryDTO.class));
    }

    @Test
    void testDeleteDocumentCategory_Success() {
        doNothing().when(documentCategoryService).deleteDocumentCategory(anyLong());

        ResponseEntity<Void> response = documentController.deleteDocumentCategory(1L);

        assertEquals(HttpStatus.NO_CONTENT, response.getStatusCode());
        verify(documentCategoryService, times(1)).deleteDocumentCategory(1L);
    }
    */
}
