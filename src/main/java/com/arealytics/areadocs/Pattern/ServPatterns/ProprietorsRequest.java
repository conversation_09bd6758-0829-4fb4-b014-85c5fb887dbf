package com.arealytics.areadocs.Pattern.ServPatterns;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProprietorsRequest {

    @Pattern(
            regexp =
                    "^(?![.,-]+$)[a-zA-Z0-9*+=?()@`&,.!#$;\\[\\]\"/]+[a-zA-Z0-9*+=?()@`&,.!#$"
                            + " ;/'\\[\\]\"-]*[a-zA-Z0-9*+=?()@`&,.!#$;\\[\\]\"/]$",
            message =
                    "Last name must start and end with valid characters (not hyphen or apostrophe)"
                            + " and contain only allowed characters")
    @Size(min = 2, max = 130, message = "Last name must be between 2 and 130 characters") private String lastName;

    @Pattern(
            regexp = "^[a-zA-Z][a-zA-Z\\s'-]*[a-zA-Z]$",
            message =
                    "Given name must start and end with a letter and contain only letters, spaces,"
                            + " hyphens, or apostrophes")
    @Size(min = 2, max = 180, message = "Given name must be between 2 and 180 characters") private String givenName;

    @Pattern(
            regexp =
                    "^(?![.,-]+$)[a-zA-Z0-9*+=?()@`&,.!#$;\\[\\]\"/]+[a-zA-Z0-9*+=?()@`&,.!#$"
                            + " ;/'\\[\\]\"-]*[a-zA-Z0-9*+=?()@`&,.!#$;\\[\\]\"/]$",
            message =
                    "Company name must start and end with valid characters (not hyphen or"
                            + " apostrophe) and contain only allowed characters")
    @Size(min = 2, max = 130, message = "Company name must be between 2 and 130 characters") private String companyName;

    @Min(value = 1, message = "Page number must be at least 1") private Integer pageNumber = 1; // Default value
}
