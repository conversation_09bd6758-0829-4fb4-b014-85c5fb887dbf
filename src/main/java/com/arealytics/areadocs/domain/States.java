package com.arealytics.areadocs.domain;

import org.hibernate.envers.Audited;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Table(name = "States")
@Audited(withModifiedFlag = true)
public class States extends BaseEntity {

    @Column(name = "StateName", nullable = false)
    private String stateName;

    @Column(name = "StateAbbr", nullable = false)
    private String stateAbbr;
}
