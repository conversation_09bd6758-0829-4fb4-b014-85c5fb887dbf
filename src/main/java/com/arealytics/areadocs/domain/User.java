package com.arealytics.areadocs.domain;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.hibernate.envers.Audited;

import com.arealytics.areadocs.enumeration.DeactivationReason;
import com.arealytics.areadocs.enumeration.UserType;
import com.fasterxml.jackson.annotation.JsonManagedReference;

import jakarta.persistence.*;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@ToString(
        callSuper = true,
        exclude = {
            "userRoles",
            "companyMemberships",
            "account",
            "transactions",
            "paymentTransactions",
            "orders"
        })
@Table(name = "app_user")
@Audited(withModifiedFlag = true)
public class User extends BaseEntity {

    @Column(name = "Email", nullable = false, unique = true, length = 255)
    private String email;

    @Column(name = "First_name", length = 100)
    private String firstName;

    @Column(name = "Middle_name", length = 100)
    private String middleName;

    @Column(name = "Last_name", length = 100)
    private String lastName;

    @Column(name = "Contact_number", length = 20)
    private String contactNumber;

    @Column(name = "User_type", nullable = false, length = 20)
    @Enumerated(EnumType.STRING)
    private UserType userType;

    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<UserRole> userRoles = new ArrayList<>();

    @Column(name = "Last_login")
    private LocalDateTime lastLogin;

    // UserProfile fields
    @Column(name = "Bio", columnDefinition = "TEXT")
    private String bio;

    @Column(name = "Profile_picture_url")
    private String profilePictureUrl;

    @OneToOne(cascade = CascadeType.ALL, optional = false)
    @JoinColumn(name = "primary_address_id", referencedColumnName = "id", nullable = false)
    private Address primaryAddress;

    @ManyToOne(cascade = CascadeType.ALL, optional = false)
    @JoinColumn(name = "billing_address_id", referencedColumnName = "id", nullable = false)
    private Address billingAddress;

    @OneToOne(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
    private CompanyMembership companyMemberships;

    @OneToOne(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
    private Account account;

    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<Transaction> transactions = new ArrayList<>();

    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<PaymentTransaction> paymentTransactions = new ArrayList<>();

    @Column(name = "Keycloak_id", length = 255)
    private String keycloakId;

    @OneToMany(
            mappedBy = "user",
            cascade = {CascadeType.PERSIST, CascadeType.MERGE},
            fetch = FetchType.LAZY)
    @JsonManagedReference("user-orders")
    private List<Order> orders = new ArrayList<>();

    @Column(name = "deactivation_reason", length = 50)
    @Enumerated(EnumType.STRING)
    private DeactivationReason deactivationReason;

    public void addOrder(Order order) {
        if (order == null) {
            return;
        }

        if (!orders.contains(order)) {
            orders.add(order);
            // Only set the user if it's different to avoid recursion
            if (order.getUser() != this) {
                order.setUser(this);
            }
        }
    }
    // BaseEntity already provides id, createdAt, modifiedAt, etc.
}
