package com.arealytics.areadocs.domain;

import org.hibernate.envers.Audited;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Table(name = "RolePermissionMappings")
@Audited(withModifiedFlag = true)
public class RolePermissionMapping extends BaseEntity {

    @ManyToOne
    @JoinColumn(name = "role_id", nullable = false)
    private Role role;

    @ManyToOne
    @JoinColumn(name = "permission_id", nullable = false)
    private Permission permission;

    @Column(name = "status", length = 20, nullable = false)
    private String status = "ACTIVE";

    // BaseEntity already provides id, createdAt, modifiedAt, etc.
}
