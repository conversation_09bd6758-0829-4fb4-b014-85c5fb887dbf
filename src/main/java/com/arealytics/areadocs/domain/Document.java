package com.arealytics.areadocs.domain;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import org.hibernate.envers.Audited;

import com.arealytics.areadocs.config.JsonConverter;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Table(name = "Document")
@Audited(withModifiedFlag = true)
public class Document extends BaseEntity {

    @ManyToOne
    @JoinColumn(name = "user_id")
    private User user;

    @ManyToOne
    @JoinColumn(name = "company_id")
    private Company company;

    @ManyToOne
    @JoinColumn(name = "folder_id")
    private Folder folder;

    @Column(name = "title", nullable = false)
    private String title;

    @Column(name = "description")
    private String description;

    @ManyToOne
    @JoinColumn(name = "source_id")
    private DocumentSource source;

    @ManyToOne
    @JoinColumn(name = "category_id")
    private DocumentCategory category;

    @Column(name = "file_path")
    private String filePath;

    @Column(name = "metadata", columnDefinition = "text", nullable = true)
    @Convert(converter = JsonConverter.class)
    private Map<String, Object> metadata = new HashMap<>();

    @Column(name = "last_accessed")
    private LocalDateTime lastAccessed;

    @Column(name = "accessed_count")
    private Long accessedCount = 0L;

    @Column(name = "expiry_date")
    private LocalDateTime expiryDate;
}
