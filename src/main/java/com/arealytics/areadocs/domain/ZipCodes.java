package com.arealytics.areadocs.domain;

import org.hibernate.envers.Audited;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Table(name = "ZipCodes")
@Audited(withModifiedFlag = true)
public class ZipCodes extends BaseEntity {

    @Column(name = "ZipCode", nullable = false)
    private String zipCode;

    @ManyToOne
    @JoinColumn(name = "state_id", nullable = false)
    private States state;
}
