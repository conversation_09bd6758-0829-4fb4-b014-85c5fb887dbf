package com.arealytics.areadocs.domain;

import java.math.BigDecimal;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
@Table(
        name = "company_special_price",
        uniqueConstraints =
                @UniqueConstraint(
                        name = "unique_company_price",
                        columnNames = {"document_price_id", "company_id"}))
public class CompanySpecialPrice extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_price_id", nullable = false)
    private DocumentPrice documentPrice;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id", nullable = false)
    private Company company;

    @Column(name = "special_price", nullable = false, precision = 10, scale = 2)
    private BigDecimal specialPrice;

    @Column(name = "special_price_gst", nullable = false, precision = 10, scale = 2)
    private BigDecimal specialPriceGst;

    
}
