package com.arealytics.areadocs.domain;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import org.hibernate.envers.Audited;

import com.arealytics.areadocs.enumeration.PaymentStatus;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
@Table(name = "payment_transaction")
@Audited(withModifiedFlag = true)
public class PaymentTransaction extends BaseEntity {

    @Column(name = "order_id", nullable = false, length = 50, unique = true)
    private String orderId;

    @Column(name = "transaction_id", nullable = false, length = 100, unique = true)
    private String transactionId;

    @Column(name = "amount", nullable = false, precision = 15, scale = 2)
    private BigDecimal amount;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private PaymentStatus status;

    @Column(name = "transaction_timestamp", nullable = false)
    private LocalDateTime transactionTimestamp = LocalDateTime.now();

    @Column(name = "payment_method", length = 50)
    private String paymentMethod;

    @Column(name = "payment_gateway", length = 50)
    private String paymentGateway;

    @Column(name = "gateway_reference", length = 100)
    private String gatewayReference;

    @Column(name = "gateway_status", length = 50)
    private String gatewayStatus;

    @Lob
    @Column(name = "gateway_response")
    private String gatewayResponse;

    @Column(name = "card_type", length = 50)
    private String cardType;

    @Column(name = "card_last_four", length = 4)
    private String cardLastFour;

    @Column(name = "card_expiry_month", length = 2)
    private String cardExpiryMonth;

    @Column(name = "card_expiry_year", length = 4)
    private String cardExpiryYear;

    @Column(name = "billing_name", length = 100)
    private String billingName;

    @Column(name = "billing_email", length = 100)
    private String billingEmail;

    @Column(name = "billing_address", length = 255)
    private String billingAddress;

    @Column(name = "billing_city", length = 100)
    private String billingCity;

    @Column(name = "billing_state", length = 100)
    private String billingState;

    @Column(name = "billing_postal_code", length = 20)
    private String billingPostalCode;

    @Column(name = "billing_country", length = 100)
    private String billingCountry;

    @Column(name = "error_code", length = 50)
    private String errorCode;

    @Column(name = "error_message", length = 255)
    private String errorMessage;

    @Column(name = "receipt_url", length = 255)
    private String receiptUrl;

    @Column(name = "confirmation_code", length = 100)
    private String confirmationCode;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id")
    private Company company;
}
