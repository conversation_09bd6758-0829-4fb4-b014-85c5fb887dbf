package com.arealytics.areadocs.domain;

import org.hibernate.envers.Audited;

import com.arealytics.areadocs.enumeration.AddressType;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Table(name = "Address")
@Audited(withModifiedFlag = true)
public class Address extends BaseEntity {

    @Column(name = "address_line_1", nullable = false)
    private String addressLine1;

    @Column(name = "address_line_2")
    private String addressLine2;

    @Column(name = "suburb")
    private String suburb;

    @ManyToOne
    @JoinColumn(name = "state_id", nullable = false)
    private States state;

    @ManyToOne
    @JoinColumn(name = "zip_code_id", nullable = false)
    private ZipCodes zipCode;

    @Column(name = "address_type", nullable = false, length = 20)
    @Enumerated(EnumType.STRING)
    private AddressType addressType;
}
