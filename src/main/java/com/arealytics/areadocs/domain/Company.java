package com.arealytics.areadocs.domain;

import java.util.ArrayList;
import java.util.List;

import org.hibernate.envers.Audited;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Table(name = "Company")
@Audited(withModifiedFlag = true)
public class Company extends BaseEntity {

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "abn")
    private String ABN;

    @Column(name = "acn")
    private String ACN;

    @Column(name = "industry")
    private String industry;

    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    @Column(name = "website")
    private String website;

    @Column(name = "employee_count")
    private Integer employeeCount;

    @OneToMany(mappedBy = "company", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<CompanyMembership> memberships = new ArrayList<>();

    @OneToOne(mappedBy = "company", cascade = CascadeType.ALL, orphanRemoval = true)
    private Account account;

    @OneToOne(cascade = CascadeType.ALL, optional = false)
    @JoinColumn(name = "primary_address_id", referencedColumnName = "id", nullable = false)
    private Address primaryAddress;

    @OneToOne(cascade = CascadeType.ALL, optional = false)
    @JoinColumn(name = "billing_address_id", referencedColumnName = "id", nullable = false)
    private Address billingAddress;

    @Column(name = "accounts_Contact_number", length = 20)
    private String accountsContactNumber;

    @Column(name = "accounts_contact_name", nullable = false)
    private String accountsContactName;

    @Column(name = "billing_email", nullable = false, unique = true, length = 255)
    private String billingEmail;
}
