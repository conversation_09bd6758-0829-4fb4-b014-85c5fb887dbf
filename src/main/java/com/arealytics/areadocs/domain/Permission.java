package com.arealytics.areadocs.domain;

import org.hibernate.envers.Audited;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Entity
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Table(name = "Permissions")
@Audited(withModifiedFlag = true)
public class Permission extends BaseEntity {

    @Column(name = "permission_code", length = 50, unique = true, nullable = false)
    private String permissionCode;

    @Column(name = "permission_name", length = 100, nullable = false)
    private String permissionName;

    @Column(name = "permission_description", length = 255)
    private String permissionDescription;

    // BaseEntity already provides id, createdAt, modifiedAt, etc.
}
