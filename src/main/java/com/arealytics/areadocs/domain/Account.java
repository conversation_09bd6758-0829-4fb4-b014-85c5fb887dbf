package com.arealytics.areadocs.domain;

import java.math.BigDecimal;

import org.hibernate.envers.Audited;

import com.arealytics.areadocs.enumeration.AccountType;

import jakarta.persistence.*;
import lombok.*;

/**
 * Entity representing an account in the system. Each user or company can have one account. The
 * platform also has one account.
 */
@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
@Table(name = "account")
@Audited(withModifiedFlag = true)
public class Account extends BaseEntity {

    @Column(name = "account_type", nullable = false, length = 20)
    @Enumerated(EnumType.STRING)
    private AccountType accountType;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", unique = true)
    private User user;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id", unique = true)
    private Company company;

    @Column(name = "balance", nullable = false, precision = 15, scale = 2)
    private BigDecimal balance = BigDecimal.ZERO;

    @Column(name = "currency", nullable = false, length = 10)
    private String currency = "AUD";
}
