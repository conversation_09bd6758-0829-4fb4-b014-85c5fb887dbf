package com.arealytics.areadocs.domain;

import java.math.BigDecimal;

import org.hibernate.envers.Audited;

import com.arealytics.areadocs.enumeration.LandUseApplicationReason;
import com.arealytics.areadocs.enumeration.OrderItemStatus;
import com.fasterxml.jackson.annotation.JsonBackReference;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@ToString(
        callSuper = true,
        exclude = {"order", "document", "user"})
@Builder
@AllArgsConstructor
@Table(name = "order_items")
@Audited(withModifiedFlag = true)
public class OrderItem extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id")
    @JsonBackReference("order-items")
    private Order order;

    @ManyToOne
    @JoinColumn(name = "document_id")
    private Document document;

    @Column(name = "price", precision = 10, scale = 2, nullable = false)
    private BigDecimal price;

    @Column(name = "tax", precision = 10, scale = 2)
    private BigDecimal tax;

    @Column(name = "final_price", precision = 10, scale = 2)
    private BigDecimal finalPrice;

    @Column(name = "status", nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    private OrderItemStatus status = OrderItemStatus.CART;

    @Column(name = "folder_id")
    private Long folderId;

    @Column(name = "product_code", length = 100)
    private String productCode;

    @Column(name = "product_name", length = 100)
    private String productName;

    @Column(name = "property_pfi", length = 50)
    private String propertyPfi;

    @Column(name = "volume_folio", length = 50)
    private String volumeFolio;

    @Column(name = "is_warning_acknowledged")
    private Boolean isWarningAcknowledged;

    @Column(name = "land_use_application_reason")
    @Enumerated(EnumType.STRING)
    private LandUseApplicationReason landUseApplicationReason;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    @JsonBackReference
    private User user;

    @Column(name = "external_order_item_id", length = 100)
    private String externalOrderItemId;

    /**
     * Helper method to associate this item with an order This maintains both sides of the
     * bidirectional relationship
     *
     * @param order The order to associate with
     */
    public void setOrder(Order order) {
        // If same order, do nothing
        if (this.order == order) {
            return;
        }

        // Remove from old order if exists
        if (this.order != null) {
            // Remove from old order's collection without triggering recursion
            if (this.order.getOrderItems().contains(this)) {
                this.order.getOrderItems().remove(this);
            }
        }

        // Set the new order
        this.order = order;

        // Add to new order if not null and not already added
        if (order != null && !order.getOrderItems().contains(this)) {
            // Add to new order's collection without triggering recursion
            order.getOrderItems().add(this);
        }
    }

    /**
     * Helper method to associate this item with a user
     *
     * @param user The user to associate with
     */
    public void setUser(User user) {
        this.user = user;
        // Do not modify user collections here to avoid recursion
    }
}
