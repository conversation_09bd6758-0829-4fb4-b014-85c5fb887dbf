package com.arealytics.areadocs.domain;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import org.hibernate.envers.Audited;

import com.arealytics.areadocs.enumeration.OrderStatus;
import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonManagedReference;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
@ToString(
        callSuper = true,
        exclude = {"user", "orderItems", "company"})
@Builder
@Table(name = "orders")
@Audited(withModifiedFlag = true)
public class Order extends BaseEntity {

    @ManyToOne
    @JoinColumn(name = "user_id", nullable = false)
    @JsonBackReference("user-orders")
    private User user;

    @ManyToOne
    @JoinColumn(name = "company_id")
    private Company company;

    @Column(name = "total_amount", precision = 15, scale = 2)
    private BigDecimal totalAmount;

    @Column(name = "status", nullable = false, length = 50)
    @Enumerated(EnumType.STRING)
    private OrderStatus status = OrderStatus.PENDING;

    @Column(name = "payment_method", length = 50)
    private String paymentMethod;

    @OneToMany(mappedBy = "order", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonManagedReference("order-items")
    private List<OrderItem> orderItems = new ArrayList<>();

    @Column(name = "external_order_id", length = 100)
    private String externalOrderId;

    @Column(name = "order_date")
    private LocalDateTime orderDate;

    /**
     * Add an order item to this order
     *
     * @param orderItem The order item to add
     * @return The added order item
     */
    public OrderItem addOrderItem(OrderItem orderItem) {
        if (orderItem == null) {
            return null;
        }

        if (!orderItems.contains(orderItem)) {
            orderItems.add(orderItem);
            // Only set the order if it's different to avoid recursion
            if (orderItem.getOrder() != this) {
                orderItem.setOrder(this);
            }
        }
        return orderItem;
    }

    /**
     * Remove an order item from this order
     *
     * @param orderItem The order item to remove
     */
    public void removeOrderItem(OrderItem orderItem) {
        if (orderItem != null && orderItems.remove(orderItem)) {
            // Only set the order to null if it's this order to avoid recursion
            if (orderItem.getOrder() == this) {
                orderItem.setOrder(null);
            }
        }
    }

    /**
     * Set the user for this order
     *
     * @param user The user to set
     */
    public void setUser(User user) {
        this.user = user;
        // Do not modify user.getOrders() here to avoid recursion
    }
}
