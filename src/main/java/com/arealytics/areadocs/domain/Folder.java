package com.arealytics.areadocs.domain;

import java.util.List;

import org.hibernate.envers.Audited;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import com.arealytics.areadocs.enumeration.FolderType;

import jakarta.persistence.*;
import lombok.Data;

@Data
@Entity
@Table(name = "folder")
@EntityListeners(AuditingEntityListener.class)
@Audited(withModifiedFlag = true)
public class Folder extends BaseEntity {

    @ManyToOne
    @JoinColumn(name = "user_id")
    private User user;

    @ManyToOne
    @JoinColumn(name = "company_id")
    private Company company;

    @Column(name = "name", length = 100, nullable = false)
    private String name;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_folder_id")
    private Folder parentFolder;

    @OneToMany(mappedBy = "parentFolder", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<Folder> childFolders;

    @Column(name = "folder_type", length = 50)
    @Enumerated(EnumType.STRING)
    private FolderType folderType;
}
