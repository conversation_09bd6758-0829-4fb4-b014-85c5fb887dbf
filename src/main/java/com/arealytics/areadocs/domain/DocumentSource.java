package com.arealytics.areadocs.domain;

import org.hibernate.envers.Audited;

import com.arealytics.areadocs.enumeration.DocumentSourceStatus;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@NoArgsConstructor
@Table(name = "DocumentSource")
@Audited(withModifiedFlag = true)
public class DocumentSource extends BaseEntity {

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "base_url")
    private String baseUrl;

    @Column(name = "status", length = 20)
    @Enumerated(EnumType.STRING)
    private DocumentSourceStatus status = DocumentSourceStatus.ACTIVE;
}
