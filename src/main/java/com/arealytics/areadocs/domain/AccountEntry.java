package com.arealytics.areadocs.domain;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import org.hibernate.envers.Audited;

import com.arealytics.areadocs.enumeration.EntryStatus;
import com.arealytics.areadocs.enumeration.EntryType;

import jakarta.persistence.*;
import lombok.*;

/**
 * Entity representing an account entry in the system. This is the header record for a double-entry
 * accounting transaction.
 */
@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
@Table(name = "account_entry")
@Audited(withModifiedFlag = true)
public class AccountEntry extends BaseEntity {

    @Column(name = "entry_date", nullable = false)
    private LocalDate entryDate;

    @Column(name = "reference_number", nullable = false, unique = true, length = 50)
    private String referenceNumber;

    @Column(name = "description", length = 255)
    private String description;

    @Column(name = "entry_type", nullable = false, length = 20)
    @Enumerated(EnumType.STRING)
    private EntryType entryType;

    @Column(name = "status", nullable = false, length = 20)
    @Enumerated(EnumType.STRING)
    private EntryStatus status;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "created_by")
    private User createdBy;

    @OneToMany(mappedBy = "accountEntry", cascade = CascadeType.ALL, orphanRemoval = true)
    @Builder.Default
    private List<AccountEntryDetail> details = new ArrayList<>();
}
