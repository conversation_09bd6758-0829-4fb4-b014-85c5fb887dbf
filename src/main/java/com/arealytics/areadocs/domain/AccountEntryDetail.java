package com.arealytics.areadocs.domain;

import java.math.BigDecimal;

import org.hibernate.envers.Audited;

import com.arealytics.areadocs.enumeration.AccountTransactionType;

import jakarta.persistence.*;
import lombok.*;

/**
 * Entity representing a detail line in an account entry. Each detail represents a debit or credit
 * to a specific account.
 */
@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = false)
@Table(name = "account_entry_detail")
@Audited(withModifiedFlag = true)
public class AccountEntryDetail extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "account_entry_id", nullable = false)
    private AccountEntry accountEntry;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "account_id", nullable = false)
    private Account account;

    @Column(name = "account_transaction_type", nullable = false, length = 20)
    @Enumerated(EnumType.STRING)
    private AccountTransactionType accountTransactionType;

    @Column(name = "amount", nullable = false, precision = 15, scale = 2)
    private BigDecimal amount;
}
