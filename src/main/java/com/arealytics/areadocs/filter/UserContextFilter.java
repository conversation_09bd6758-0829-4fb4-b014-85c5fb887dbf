package com.arealytics.areadocs.filter;

import java.io.IOException;

import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.security.SecurityUtils;
import com.arealytics.areadocs.service.UserService;
import com.arealytics.areadocs.util.AuthUtils;
import com.arealytics.areadocs.util.UserContext;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Filter to set the current user ID for each request. This is used for audit logging when Spring
 * Security is not available.
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class UserContextFilter extends OncePerRequestFilter {

    private final SecurityUtils securityUtils;
    private final UserService userService;
    private final AuthUtils authUtils;

    @Override
    protected void doFilterInternal(
            HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        try {
            // Get user ID
            User user = authUtils.getCurrentUser();
            Long userId = user != null ? user.getId() : null;

            // Set the user ID in the context
            UserContext.setCurrentUserId(userId);

            // Continue with the request
            filterChain.doFilter(request, response);
        } finally {
            // Clear the context to prevent memory leaks
            UserContext.clear();
        }
    }
}
