package com.arealytics.areadocs.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.arealytics.areadocs.domain.Folder;
import com.arealytics.areadocs.dto.responseDTO.FolderResponseDTO;

@Mapper(
        componentModel = "spring",
        uses = {DateTimeMapper.class})
public interface FolderMapper {

    @Mapping(target = "userId", source = "user.id")
    @Mapping(target = "companyId", source = "company.id")
    @Mapping(target = "folderType", source = "folderType")
    @Mapping(target = "parentFolderId", source = "parentFolder.id")
    @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(target = "deleteAt", source = "deleteAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(
            target = "modifiedAt",
            source = "modifiedAt",
            qualifiedByName = "instantToLocalDateTime")
    FolderResponseDTO toDto(Folder folder);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "modifiedAt", ignore = true)
    @Mapping(target = "deleteAt", ignore = true)
    @Mapping(target = "isActive", ignore = true)
    @Mapping(target = "version", ignore = true)
    Folder toEntity(FolderResponseDTO folderResponseDTO);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "modifiedAt", ignore = true)
    @Mapping(target = "deleteAt", ignore = true)
    @Mapping(target = "isActive", ignore = true)
    @Mapping(target = "version", ignore = true)
    Folder toEntity(com.arealytics.areadocs.dto.requestDTO.FolderRequestDTO folderRequestDTO);
}
