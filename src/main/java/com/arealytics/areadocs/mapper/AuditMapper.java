package com.arealytics.areadocs.mapper;

import com.arealytics.areadocs.config.audit.CustomRevisionEntity;

/**
 * Mapper interface for audit-related operations. This interface defines methods for mapping user
 * context information to revision entities.
 */
public interface AuditMapper {

    /**
     * Maps the current user context to a revision entity.
     *
     * @param revisionEntity The revision entity to populate
     */
    void mapCurrentUserToRevision(CustomRevisionEntity revisionEntity);
}
