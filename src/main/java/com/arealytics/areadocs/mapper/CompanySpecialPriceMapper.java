package com.arealytics.areadocs.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import com.arealytics.areadocs.domain.CompanySpecialPrice;
import com.arealytics.areadocs.dto.requestDTO.CompanySpecialPriceRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.CompanySpecialPriceResponseDTO;

@Mapper(
        componentModel = "spring",
        uses = {DateTimeMapper.class, DocumentPriceMapper.class})
public interface CompanySpecialPriceMapper {

    @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(
            target = "modifiedAt",
            source = "modifiedAt",
            qualifiedByName = "instantToLocalDateTime")
    @Mapping(target = "deleteAt", source = "deleteAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(target = "companyId", expression = "java(companySpecialPrice.getCompany().getId())")
    @Mapping(target = "documentPrice", source = "documentPrice")
    CompanySpecialPriceResponseDTO toDto(CompanySpecialPrice companySpecialPrice);

    CompanySpecialPrice toEntity(CompanySpecialPriceRequestDTO companySpecialPriceRequestDTO);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "modifiedAt", ignore = true)
    @Mapping(target = "deleteAt", ignore = true)
    @Mapping(target = "isActive", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "company", ignore = true)
    @Mapping(target = "documentPrice", ignore = true)
    void updateCompanySpecialPriceFromDto(
            CompanySpecialPriceRequestDTO companySpecialPriceRequestDTO,
            @MappingTarget CompanySpecialPrice companySpecialPrice);
}
