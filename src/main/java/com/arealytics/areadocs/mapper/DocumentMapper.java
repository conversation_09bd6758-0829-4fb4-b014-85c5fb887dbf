package com.arealytics.areadocs.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.arealytics.areadocs.domain.*;
import com.arealytics.areadocs.dto.requestDTO.DocumentUpdateDTO;
import com.arealytics.areadocs.dto.responseDTO.DocumentDTO;

@Mapper(
        componentModel = "spring",
        uses = {DateTimeMapper.class})
public interface DocumentMapper {

    @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(target = "deleteAt", source = "deleteAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(
            target = "modifiedAt",
            source = "modifiedAt",
            qualifiedByName = "instantToLocalDateTime")
    @Mapping(target = "metadata", source = "metadata")
    @Mapping(target = "folderName", source = "folder.name")
    @Mapping(target = "folderId", source = "folder.id")
    @Mapping(target = "categoryName", source = "category.name")
    @Mapping(target = "categoryId", source = "category.id")
    DocumentDTO toDTO(Document document);

    @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "localDateTimeToInstant")
    @Mapping(target = "deleteAt", source = "deleteAt", qualifiedByName = "localDateTimeToInstant")
    @Mapping(
            target = "modifiedAt",
            source = "modifiedAt",
            qualifiedByName = "localDateTimeToInstant")
    @Mapping(target = "metadata", source = "metadata")
    @Mapping(target = "folder", ignore = true)
    @Mapping(target = "source", ignore = true)
    @Mapping(target = "category", ignore = true)
    Document toEntity(DocumentDTO dto);

    /**
     * Maps a request DTO to an entity, ignoring audit fields which will be handled by JPA auditing
     */
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "deleteAt", ignore = true)
    @Mapping(target = "modifiedAt", ignore = true)
    @Mapping(target = "isActive", ignore = true)
    @Mapping(target = "version", ignore = true)
    Document toEntity(DocumentUpdateDTO requestDTO);
}
