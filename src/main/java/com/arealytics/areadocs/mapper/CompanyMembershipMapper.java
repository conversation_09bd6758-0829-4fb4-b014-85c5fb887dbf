package com.arealytics.areadocs.mapper;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

import com.arealytics.areadocs.domain.Company;
import com.arealytics.areadocs.domain.CompanyMembership;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.dto.requestDTO.CompanyMembershipRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.CompanyMembershipDTO;
import com.arealytics.areadocs.repository.CompanyRepository;
import com.arealytics.areadocs.repository.UserRepository;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class CompanyMembershipMapper {

    @Autowired protected UserRepository userRepository;

    @Autowired protected CompanyRepository companyRepository;

    @Mapping(target = "userId", source = "user.id")
    @Mapping(target = "userEmail", source = "user.email")
    @Mapping(target = "userFirstName", source = "user.firstName")
    @Mapping(target = "userLastName", source = "user.lastName")
    @Mapping(target = "companyId", source = "company.id")
    @Mapping(target = "companyName", source = "company.name")
    @Mapping(target = "roleId", source = "role.id")
    @Mapping(target = "roleName", source = "role.name")
    @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(
            target = "modifiedAt",
            source = "modifiedAt",
            qualifiedByName = "instantToLocalDateTime")
    @Mapping(target = "deleteAt", source = "deleteAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(target = "roleDisplayText", source = "role.displayText")
    @Mapping(target = "companyABN", source = "company.ABN")
    @Mapping(target = "companyACN", source = "company.ACN")
    public abstract CompanyMembershipDTO toDto(CompanyMembership companyMembership);

    // Map from response DTO to entity (should only be used for testing)
    @Mapping(target = "user", source = "userId", qualifiedByName = "idToUser")
    @Mapping(target = "company", source = "companyId", qualifiedByName = "idToCompany")
    @Mapping(target = "role", ignore = true) // Will be set manually
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "modifiedAt", ignore = true)
    @Mapping(target = "deleteAt", ignore = true)
    public abstract CompanyMembership toEntity(CompanyMembershipDTO companyMembershipDTO);

    // Map from request DTO to entity (use this for create/update operations)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "user", source = "userId", qualifiedByName = "idToUser")
    @Mapping(target = "company", source = "companyId", qualifiedByName = "idToCompany")
    @Mapping(target = "role", ignore = true) // Will be set manually in service
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "modifiedAt", ignore = true)
    @Mapping(target = "deleteAt", ignore = true)
    @Mapping(target = "isActive", constant = "true")
    @Mapping(target = "version", ignore = true)
    public abstract CompanyMembership toEntity(CompanyMembershipRequestDTO requestDTO);

    @Named("instantToLocalDateTime")
    protected LocalDateTime instantToLocalDateTime(Instant instant) {
        return instant != null ? LocalDateTime.ofInstant(instant, ZoneId.systemDefault()) : null;
    }

    @Named("localDateTimeToInstant")
    protected Instant localDateTimeToInstant(LocalDateTime localDateTime) {
        return localDateTime != null
                ? localDateTime.atZone(ZoneId.systemDefault()).toInstant()
                : null;
    }

    @Named("idToUser")
    protected User idToUser(Long id) {
        if (id == null) {
            return null;
        }
        User user = new User();
        user.setId(id);
        return user;
    }

    @Named("idToCompany")
    protected Company idToCompany(Long id) {
        if (id == null) {
            return null;
        }
        Company company = new Company();
        company.setId(id);
        return company;
    }

    @AfterMapping
    protected void enrichCompanyMembership(
            CompanyMembershipDTO dto, @MappingTarget CompanyMembership entity) {
        if (dto.getUserId() != null) {
            userRepository.findById(dto.getUserId()).ifPresent(entity::setUser);
        }

        if (dto.getCompanyId() != null) {
            companyRepository.findById(dto.getCompanyId()).ifPresent(entity::setCompany);
        }
    }
}
