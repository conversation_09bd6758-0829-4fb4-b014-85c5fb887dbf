package com.arealytics.areadocs.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.arealytics.areadocs.domain.Company;
import com.arealytics.areadocs.dto.responseDTO.CompanyDTO;

@Mapper(
        componentModel = "spring",
        uses = {AddressMapper.class, DateTimeMapper.class})
public interface CompanyMapper {

    @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(
            target = "modifiedAt",
            source = "modifiedAt",
            qualifiedByName = "instantToLocalDateTime")
    @Mapping(target = "deleteAt", source = "deleteAt", qualifiedByName = "instantToLocalDateTime")
    CompanyDTO toDto(Company company);

    @Mapping(target = "primaryAddress", ignore = true)
    @Mapping(target = "billingAddress", ignore = true)
    @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "localDateTimeToInstant")
    @Mapping(
            target = "modifiedAt",
            source = "modifiedAt",
            qualifiedByName = "localDateTimeToInstant")
    @Mapping(target = "deleteAt", ignore = true)
    @Mapping(target = "memberships", expression = "java(new java.util.ArrayList<>())")
    @Mapping(target = "account", ignore = true) // Explicitly ignore unmapped property
    Company toEntity(CompanyDTO companyDTO);
}
