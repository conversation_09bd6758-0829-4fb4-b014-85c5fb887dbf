package com.arealytics.areadocs.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import com.arealytics.areadocs.dto.responseDTO.serv.CustomParcelResponse;
import com.arealytics.areadocs.dto.responseDTO.serv.ParcelResponse;

@Mapper(componentModel = "spring")
public interface ParcelResponseMapper {

    @Mapping(target = "properties", ignore = true)
    CustomParcelResponse toCustomParcelResponse(ParcelResponse parcelResponse);
}
