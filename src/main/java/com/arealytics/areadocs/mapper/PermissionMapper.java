package com.arealytics.areadocs.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import com.arealytics.areadocs.domain.Permission;
import com.arealytics.areadocs.dto.requestDTO.PermissionRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.PermissionDTO;

@Mapper(
        componentModel = "spring",
        uses = {DateTimeMapper.class})
public interface PermissionMapper {

    @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(
            target = "modifiedAt",
            source = "modifiedAt",
            qualifiedByName = "instantToLocalDateTime")
    @Mapping(target = "deleteAt", source = "deleteAt", qualifiedByName = "instantToLocalDateTime")
    PermissionDTO toDto(Permission permission);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "modifiedAt", ignore = true)
    @Mapping(target = "deleteAt", ignore = true)
    @Mapping(target = "isActive", constant = "true")
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "permissionName", source = "name")
    @Mapping(target = "permissionDescription", source = "description")
    Permission toEntity(PermissionRequestDTO permissionRequestDTO);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "modifiedAt", ignore = true)
    @Mapping(target = "deleteAt", ignore = true)
    @Mapping(target = "isActive", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "permissionName", source = "name")
    @Mapping(target = "permissionDescription", source = "description")
    void updateEntityFromDto(PermissionRequestDTO dto, @MappingTarget Permission entity);
}
