package com.arealytics.areadocs.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.arealytics.areadocs.domain.RolePermissionMapping;
import com.arealytics.areadocs.dto.requestDTO.RolePermissionRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.RolePermissionDTO;

@Mapper(
        componentModel = "spring",
        uses = {DateTimeMapper.class})
public interface RolePermissionMapper {

    @Mapping(target = "roleId", source = "role.id")
    @Mapping(target = "roleName", source = "role.name")
    @Mapping(target = "permissionId", source = "permission.id")
    @Mapping(target = "permissionCode", source = "permission.permissionCode")
    @Mapping(target = "permissionName", source = "permission.permissionName")
    @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(
            target = "modifiedAt",
            source = "modifiedAt",
            qualifiedByName = "instantToLocalDateTime")
    @Mapping(target = "deleteAt", source = "deleteAt", qualifiedByName = "instantToLocalDateTime")
    RolePermissionDTO toDto(RolePermissionMapping rolePermissionMapping);

    @Mapping(source = "roleId", target = "role.id")
    @Mapping(source = "permissionId", target = "permission.id")
    RolePermissionMapping toEntity(RolePermissionRequestDTO requestDTO);
}
