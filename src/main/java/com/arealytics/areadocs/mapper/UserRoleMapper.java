package com.arealytics.areadocs.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.arealytics.areadocs.domain.UserRole;
import com.arealytics.areadocs.dto.responseDTO.UserRoleDTO;

@Mapper(
        componentModel = "spring",
        uses = {DateTimeMapper.class})
public interface UserRoleMapper {

    @Mapping(target = "userId", source = "user.id")
    @Mapping(target = "userEmail", source = "user.email")
    @Mapping(target = "userFirstName", source = "user.firstName")
    @Mapping(target = "userLastName", source = "user.lastName")
    @Mapping(target = "roleId", source = "role.id")
    @Mapping(target = "roleName", source = "role.name")
    @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(
            target = "modifiedAt",
            source = "modifiedAt",
            qualifiedByName = "instantToLocalDateTime")
    @Mapping(target = "deleteAt", source = "deleteAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(target = "roleDisplayText", source = "role.displayText")
    UserRoleDTO toDto(UserRole userRole);
}
