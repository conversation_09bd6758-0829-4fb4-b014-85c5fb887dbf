package com.arealytics.areadocs.mapper;

import java.math.BigDecimal;
import java.util.List;

import org.mapstruct.*;

import com.arealytics.areadocs.domain.OrderItem;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.dto.requestDTO.CartRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.CartResponseDTO;
import com.arealytics.areadocs.dto.responseDTO.serv.ProductsResponse;
import com.arealytics.areadocs.enumeration.OrderItemStatus;

/** MapStruct mapper for converting between OrderItem entities and Cart DTOs */
@Mapper(componentModel = "spring")
public interface OrderItemMapper {

    /**
     * Convert an OrderItem to CartResponseDTO.CartItemDTO
     *
     * @param orderItem OrderItem entity
     * @return CartResponseDTO.CartItemDTO
     */
    @Mapping(target = "id", source = "id")
    @Mapping(target = "productCode", source = "productCode")
    @Mapping(target = "productName", source = "productName")
    @Mapping(target = "folderId", source = "folderId")
    @Mapping(target = "propertyPfi", source = "propertyPfi")
    @Mapping(target = "volumeFolio", source = "volumeFolio")
    @Mapping(target = "isWarningAcknowledged", source = "isWarningAcknowledged")
    @Mapping(target = "price", source = "price")
    @Mapping(target = "tax", source = "tax")
    @Mapping(target = "finalPrice", source = "finalPrice")
    @Mapping(target = "status", source = "status")
    CartResponseDTO.CartItemDTO toCartItemDTO(OrderItem orderItem);

    /** Calculate the total amount from a list of OrderItems */
    @Named("calculateTotalAmount")
    default BigDecimal calculateTotalAmount(List<OrderItem> orderItems) {
        if (orderItems == null || orderItems.isEmpty()) {
            return BigDecimal.ZERO;
        }
        return orderItems.stream()
                .map(OrderItem::getFinalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * Convert a list of OrderItems to a CartResponseDTO
     *
     * @param orderItems List of OrderItem entities
     * @param userId User ID
     * @return CartResponseDTO
     */
    @Mapping(target = "userId", source = "userId")
    @Mapping(
            target = "totalAmount",
            source = "orderItems",
            qualifiedByName = "calculateTotalAmount")
    @Mapping(target = "itemCount", expression = "java(orderItems != null ? orderItems.size() : 0)")
    @Mapping(target = "items", source = "orderItems")
    CartResponseDTO toCartResponseDTO(List<OrderItem> orderItems, Long userId);

    /** Create an OrderItem from ProductData and CartRequestDTO */
    @Named("createOrderItemFromProduct")
    default OrderItem createOrderItemFromProduct(
            @Context ProductsResponse.Product productData,
            @Context User user,
            CartRequestDTO request) {

        if (productData == null || request == null || user == null) {
            return null;
        }

        BigDecimal productPrice = productData.getPrice();
        BigDecimal productTax = productData.getGst();

        return OrderItem.builder()
                .finalPrice(productPrice)
                .tax(productTax)
                .price(productPrice.subtract(productTax))
                .user(user)
                .status(OrderItemStatus.CART)
                .productCode(request.getProductCode())
                .productName(productData.getProductName())
                .folderId(request.getFolderId())
                .propertyPfi(request.getPropertyPfi())
                .volumeFolio(request.getVolumeFolio())
                .isWarningAcknowledged(request.getIsWarningAcknowledged())
                .landUseApplicationReason(request.getLandUseApplicationReason())
                .build();
    }
}
