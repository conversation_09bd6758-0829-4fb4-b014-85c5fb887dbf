package com.arealytics.areadocs.mapper;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.arealytics.areadocs.dto.responseDTO.serv.CustomProductResponse;
import com.arealytics.areadocs.dto.responseDTO.serv.CustomProductResponse.ProductDetails;
import com.arealytics.areadocs.dto.responseDTO.serv.ProductsResponse;

@Mapper(componentModel = "spring")
public interface CustomProductMapper {

    @Mapping(source = "products", target = "products")
    CustomProductResponse toCustomProduct(ProductsResponse response);

    List<ProductDetails> mapProducts(List<ProductsResponse.Product> products);

    @Mapping(source = "productCode", target = "productCode")
    @Mapping(source = "productName", target = "productName")
    @Mapping(source = "productDescription", target = "productDescription")
    @Mapping(source = "productAvailability", target = "productAvailability")
    @Mapping(source = "price", target = "price")
    @Mapping(source = "gst", target = "gst")
    @Mapping(source = "productInformation.documentType", target = "documentType")
    @Mapping(source = "turnaroundTime", target = "turnaroundTime")
    @Mapping(source = "turnaroundTimeUnit", target = "turnaroundTimeUnit")
    @Mapping(source = "customerWarning", target = "customerWarning")
    ProductDetails toCustomProduct(ProductsResponse.Product product);
}
