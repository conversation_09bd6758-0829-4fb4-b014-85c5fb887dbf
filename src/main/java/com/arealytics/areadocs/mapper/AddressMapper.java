package com.arealytics.areadocs.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import com.arealytics.areadocs.domain.Address;
import com.arealytics.areadocs.dto.requestDTO.AddressRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.AddressResponseDTO;

@Mapper(
        componentModel = "spring",
        uses = {DateTimeMapper.class})
public interface AddressMapper {

    @Mapping(target = "state", ignore = true)
    @Mapping(target = "zipCode", ignore = true)
    @Mapping(target = "addressType", ignore = true)
    Address toEntity(AddressRequestDTO addressRequestDTO);

    @Mapping(source = "state.id", target = "stateId")
    @Mapping(source = "state.stateName", target = "stateName")
    @Mapping(source = "state.stateAbbr", target = "stateAbbr")
    @Mapping(source = "zipCode.id", target = "zipCodeId")
    @Mapping(source = "zipCode.zipCode", target = "zipCode")
    AddressResponseDTO toDto(Address address);

    @Mapping(target = "state", ignore = true)
    @Mapping(target = "zipCode", ignore = true)
    @Mapping(target = "addressType", ignore = true)
    void updateAddressFromDTO(AddressRequestDTO dto, @MappingTarget Address entity);
}
