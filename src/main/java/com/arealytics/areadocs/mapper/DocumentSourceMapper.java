package com.arealytics.areadocs.mapper;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import com.arealytics.areadocs.domain.DocumentSource;
import com.arealytics.areadocs.dto.requestDTO.DocumentSourceRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.DocumentSourceDTO;

@Mapper(componentModel = "spring")
public interface DocumentSourceMapper {

    @Named("instantToLocalDateTime")
    static LocalDateTime instantToLocalDateTime(Instant instant) {
        return instant != null ? instant.atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
    }

    @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(target = "deleteAt", source = "deleteAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(
            target = "modifiedAt",
            source = "modifiedAt",
            qualifiedByName = "instantToLocalDateTime")
    DocumentSourceDTO toDto(DocumentSource documentSource);

    @Named("localDateTimeToInstant")
    static Instant localDateTimeToInstant(LocalDateTime localDateTime) {
        return localDateTime != null
                ? localDateTime.atZone(ZoneId.systemDefault()).toInstant()
                : null;
    }

    // Map from response DTO to entity (should only be used for testing)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "deleteAt", ignore = true)
    @Mapping(target = "modifiedAt", ignore = true)
    @Mapping(target = "isActive", constant = "true")
    @Mapping(target = "version", ignore = true)
    DocumentSource toEntity(DocumentSourceDTO documentSourceDTO);

    // Map from request DTO to entity (use this for create/update operations)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "deleteAt", ignore = true)
    @Mapping(target = "modifiedAt", ignore = true)
    @Mapping(target = "isActive", constant = "true")
    @Mapping(target = "version", ignore = true)
    DocumentSource toEntity(DocumentSourceRequestDTO requestDTO);
}
