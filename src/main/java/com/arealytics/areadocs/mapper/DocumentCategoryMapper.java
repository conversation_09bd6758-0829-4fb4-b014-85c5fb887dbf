package com.arealytics.areadocs.mapper;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import com.arealytics.areadocs.domain.DocumentCategory;
import com.arealytics.areadocs.dto.responseDTO.DocumentCategoryDTO;

@Mapper(componentModel = "spring")
public interface DocumentCategoryMapper {

    @Named("instantToLocalDateTime")
    static LocalDateTime instantToLocalDateTime(Instant instant) {
        return instant != null ? instant.atZone(ZoneId.systemDefault()).toLocalDateTime() : null;
    }

    @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(target = "deleteAt", source = "deleteAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(
            target = "modifiedAt",
            source = "modifiedAt",
            qualifiedByName = "instantToLocalDateTime")
    DocumentCategoryDTO toDTO(DocumentCategory entity);

    @Named("localDateTimeToInstant")
    static Instant localDateTimeToInstant(LocalDateTime localDateTime) {
        return localDateTime != null
                ? localDateTime.atZone(ZoneId.systemDefault()).toInstant()
                : null;
    }

    @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "localDateTimeToInstant")
    @Mapping(target = "deleteAt", source = "deleteAt", qualifiedByName = "localDateTimeToInstant")
    @Mapping(
            target = "modifiedAt",
            source = "modifiedAt",
            qualifiedByName = "localDateTimeToInstant")
    @Mapping(target = "id", ignore = true)
    DocumentCategory toEntity(DocumentCategoryDTO dto);
}
