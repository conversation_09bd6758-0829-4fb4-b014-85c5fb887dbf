package com.arealytics.areadocs.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import com.arealytics.areadocs.domain.PaymentTransaction;
import com.arealytics.areadocs.domain.Transaction;
import com.arealytics.areadocs.dto.responseDTO.TransactionDTO;

@Mapper(
        componentModel = "spring",
        uses = {DateTimeMapper.class})
public interface TransactionMapper {

    @Named("mapPaymentMethod")
    static String mapPaymentMethod(PaymentTransaction paymentTransaction) {
        return paymentTransaction != null ? paymentTransaction.getPaymentMethod() : null;
    }

    @Mapping(target = "transactionId", source = "id")
    @Mapping(
            target = "paymentMethod",
            source = "paymentTransaction",
            qualifiedByName = "mapPaymentMethod")
    TransactionDTO toDto(Transaction transaction);

    Transaction toEntity(TransactionDTO transactionDTO);
}
