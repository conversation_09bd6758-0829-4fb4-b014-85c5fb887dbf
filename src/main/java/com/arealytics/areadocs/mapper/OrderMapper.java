package com.arealytics.areadocs.mapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import com.arealytics.areadocs.domain.Order;
import com.arealytics.areadocs.domain.OrderItem;
import com.arealytics.areadocs.domain.Transaction;
import com.arealytics.areadocs.dto.responseDTO.OrderItemDetailsResponse;
import com.arealytics.areadocs.dto.responseDTO.OrderResponseDTO;
import com.arealytics.areadocs.dto.responseDTO.OrderResponseDTO.OrderItemDTO;
import com.arealytics.areadocs.dto.responseDTO.TransactionDTO;

/** Mapper for converting between Order entities and OrderResponseDTOs. */
@Mapper(
        componentModel = "spring",
        uses = {DocumentMapper.class, DateTimeMapper.class})
public interface OrderMapper {

    /** FIXED: Convert an Order entity to an OrderResponseDTO with null checks */
    @Mapping(target = "items", source = "orderItems")
    @Mapping(target = "totalTax", expression = "java(calculateTotalTax(order))")
    @Mapping(target = "totalAmount", expression = "java(calculateTotalAmount(order))")
    @Mapping(target = "externalOrderId", ignore = true)
    @Mapping(target = "transaction", ignore = true)
    OrderResponseDTO toDto(Order order);

    /** Convert a list of Order entities to a list of OrderResponseDTOs. */
    List<OrderResponseDTO> toDto(List<Order> orders);

    /** Convert an OrderItem entity to an OrderItemDTO. */
    @Mapping(target = "productCode", source = "productCode")
    @Mapping(target = "productName", source = "productName")
    @Mapping(target = "documentType", ignore = true)
    @Mapping(target = "titleId", ignore = true)
    @Mapping(target = "status", source = "status", qualifiedByName = "mapStatus")
    @Mapping(target = "document", source = "document")
    OrderItemDTO orderItemToDto(OrderItem orderItem);

    /** Convert a Transaction entity to a TransactionDTO. */
    @Mapping(target = "transactionId", source = "id")
    @Mapping(target = "paymentMethod", constant = "Wallet")
    TransactionDTO transactionToDto(Transaction transaction);

    /** Convert an OrderItem entity to an OrderItemDetailsResponse. */
    @Mapping(target = "orderId", source = "order.id")
    @Mapping(target = "orderReference", source = "order.id")
    @Mapping(target = "productCode", source = "productCode")
    @Mapping(target = "productName", source = "productName")
    @Mapping(target = "documentType", ignore = true)
    @Mapping(target = "titleId", ignore = true)
    @Mapping(target = "status", source = "status", qualifiedByName = "mapStatus")
    OrderItemDetailsResponse toOrderItemDetailsResponse(OrderItem orderItem);

    /** FIXED: Calculate the total tax for an order with proper null checks */
    @Named("calculateTotalTax")
    default BigDecimal calculateTotalTax(Order order) {
        if (order == null || order.getOrderItems() == null || order.getOrderItems().isEmpty()) {
            return BigDecimal.ZERO;
        }
        return order.getOrderItems().stream()
                .filter(Objects::nonNull)
                .map(OrderItem::getTax)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /** FIXED: Calculate the total amount for an order with proper null checks */
    @Named("calculateTotalAmount")
    default BigDecimal calculateTotalAmount(Order order) {
        if (order == null || order.getOrderItems() == null || order.getOrderItems().isEmpty()) {
            return BigDecimal.ZERO;
        }
        return order.getOrderItems().stream()
                .filter(Objects::nonNull)
                .map(OrderItem::getFinalPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /** Map the status enum to a string. */
    @Named("mapStatus")
    default String mapStatus(Object status) {
        return status != null ? status.toString() : null;
    }
}
