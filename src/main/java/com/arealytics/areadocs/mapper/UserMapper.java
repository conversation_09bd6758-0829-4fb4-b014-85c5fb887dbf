package com.arealytics.areadocs.mapper;

import java.time.LocalDateTime;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.dto.requestDTO.UserRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.UserResponseDTO;
import com.arealytics.areadocs.enumeration.UserType;

@Mapper(
        componentModel = "spring",
        uses = {AddressMapper.class, DateTimeMapper.class})
public interface UserMapper {

    @Mapping(target = "userType", source = "userType", qualifiedByName = "userTypeToString")
    @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(
            target = "modifiedAt",
            source = "modifiedAt",
            qualifiedByName = "instantToLocalDateTime")
    @Mapping(target = "deleteAt", source = "deleteAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(target = "profilePictureUrl", source = "profilePictureUrl")
    @Mapping(target = "companyId", expression = "java(getCompanyId(user))")
    @Mapping(target = "roleId", ignore = true)
    @Mapping(target = "roleDisplayText", ignore = true)
    UserResponseDTO toDto(User user);

    @Mapping(target = "userType", source = "userType", qualifiedByName = "stringToUserType")
    @Mapping(target = "companyMemberships", ignore = true)
    @Mapping(target = "isActive", constant = "true")
    @Mapping(target = "primaryAddress", ignore = true)
    @Mapping(target = "billingAddress", ignore = true)
    @Mapping(target = "profilePictureUrl", source = "profilePictureUrl")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "modifiedAt", ignore = true)
    @Mapping(target = "deleteAt", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "account", ignore = true)
    @Mapping(target = "keycloakId", ignore = true)
    @Mapping(target = "lastLogin", ignore = true)
    @Mapping(target = "paymentTransactions", ignore = true)
    @Mapping(target = "transactions", ignore = true)
    @Mapping(target = "userRoles", ignore = true)
    User toEntity(UserRequestDTO userDTO);

    @Named("userTypeToString")
    default String userTypeToString(UserType userType) {
        return userType != null ? userType.name() : null;
    }

    @Named("stringToUserType")
    default UserType stringToUserType(String userType) {
        return userType != null ? UserType.valueOf(userType) : null;
    }

    @Named("localDateTimeToLocalDateTime")
    default LocalDateTime localDateTimeToLocalDateTime(LocalDateTime localDateTime) {
        return localDateTime;
    }

    /** Get the company ID from the user's company memberships if they are a company user */
    default Long getCompanyId(User user) {
        if (user.getUserType() == UserType.COMPANY && user.getCompanyMemberships() != null) {
            return user.getCompanyMemberships().getCompany().getId();
        }
        return null;
    }
}
