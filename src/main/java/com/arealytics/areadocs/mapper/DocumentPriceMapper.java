package com.arealytics.areadocs.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import com.arealytics.areadocs.domain.DocumentPrice;
import com.arealytics.areadocs.dto.requestDTO.DocumentPriceRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.DocumentPriceResponseDTO;

@Mapper(
        componentModel = "spring",
        uses = {DateTimeMapper.class})
public interface DocumentPriceMapper {

    @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(
            target = "modifiedAt",
            source = "modifiedAt",
            qualifiedByName = "instantToLocalDateTime")
    @Mapping(target = "deleteAt", source = "deleteAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(
            target = "documentTypeId",
            expression =
                    "java(documentPrice.getDocumentType() != null ?"
                            + " documentPrice.getDocumentType().getId() : null)")
    DocumentPriceResponseDTO toDto(DocumentPrice documentPrice);

    DocumentPrice toEntity(DocumentPriceRequestDTO documentPriceRequestDTO);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "modifiedAt", ignore = true)
    @Mapping(target = "deleteAt", ignore = true)
    @Mapping(target = "isActive", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "documentType", ignore = true)
    void updateDocumentPriceFromDto(
            DocumentPriceRequestDTO documentPriceRequestDTO,
            @MappingTarget DocumentPrice documentPrice);
}
