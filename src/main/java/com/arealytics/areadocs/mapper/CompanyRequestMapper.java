package com.arealytics.areadocs.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import com.arealytics.areadocs.domain.Company;
import com.arealytics.areadocs.dto.requestDTO.CompanyRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.CompanyDTO;

@Mapper(
        componentModel = "spring",
        uses = {AddressMapper.class, DateTimeMapper.class})
public interface CompanyRequestMapper {

    /**
     * Maps a CompanyRequestDTO to a CompanyDTO
     *
     * @param requestDTO The request DTO to map
     * @return The mapped CompanyDTO
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "modifiedAt", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "deleteAt", ignore = true)
    @Mapping(target = "primaryAddress", ignore = true)
    @Mapping(target = "billingAddress", ignore = true)
    CompanyDTO toDto(CompanyRequestDTO requestDTO);

    @Mapping(target = "primaryAddress", ignore = true)
    @Mapping(target = "billingAddress", ignore = true)
    Company toEntity(CompanyRequestDTO requestDTO);

    @Mapping(target = "primaryAddress", ignore = true)
    @Mapping(target = "billingAddress", ignore = true)
    void updateCompanyFromDto(CompanyRequestDTO requestDTO, @MappingTarget Company company);
}
