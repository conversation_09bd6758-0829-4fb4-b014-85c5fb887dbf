package com.arealytics.areadocs.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import com.arealytics.areadocs.domain.Role;
import com.arealytics.areadocs.dto.requestDTO.RoleRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.RoleDTO;

@Mapper(
        componentModel = "spring",
        uses = {DateTimeMapper.class})
public interface RoleMapper {

    @Mapping(target = "createdAt", source = "createdAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(
            target = "modifiedAt",
            source = "modifiedAt",
            qualifiedByName = "instantToLocalDateTime")
    @Mapping(target = "deleteAt", source = "deleteAt", qualifiedByName = "instantToLocalDateTime")
    @Mapping(target = "displayText", source = "displayText")
    RoleDTO toDto(Role role);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "modifiedAt", ignore = true)
    @Mapping(target = "deleteAt", ignore = true)
    @Mapping(target = "isActive", constant = "true")
    @Mapping(target = "version", ignore = true)
    Role toEntity(RoleRequestDTO roleRequestDTO);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "modifiedAt", ignore = true)
    @Mapping(target = "deleteAt", ignore = true)
    @Mapping(target = "isActive", ignore = true)
    @Mapping(target = "version", ignore = true)
    void updateEntityFromDto(RoleRequestDTO dto, @MappingTarget Role entity);
}
