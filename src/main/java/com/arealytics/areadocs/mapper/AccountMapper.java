package com.arealytics.areadocs.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import com.arealytics.areadocs.domain.Account;
import com.arealytics.areadocs.domain.Company;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.dto.responseDTO.AccountDTO;

@Mapper(
        componentModel = "spring",
        uses = {DateTimeMapper.class})
public interface AccountMapper {

    @Mapping(target = "userId", source = "user", qualifiedByName = "userToUserId")
    @Mapping(target = "companyId", source = "company", qualifiedByName = "companyToCompanyId")
    AccountDTO toDto(Account account);

    @Named("userToUserId")
    default Long userToUserId(User user) {
        return user != null ? user.getId() : null;
    }

    @Named("companyToCompanyId")
    default Long companyToCompanyId(Company company) {
        return company != null ? company.getId() : null;
    }
}
