package com.arealytics.areadocs.enumeration;

public class PasswordActionValues {
    public static final String UPDATE_PASSWORD_ACTION = "UPDATE_PASSWORD";
    public static final String RESET_PASSWORD = "RESET_PASSWORD";
    public static final String GRANT_TYPE = "grant_type";
    public static final String CLIENT_CREDENTIALS = "client_credentials";
    public static final String CLIENT_ID = "client_id";
    public static final String CLIENT_SECRET = "client_secret";
    public static final String ACCESS_TOKEN = "access_token";
    public static final String TOKEN_URL = "%s/realms/%s/protocol/openid-connect/token";
    public static final String EMAIL_URL = "%s/admin/realms/%s/users?email=%s";
    public static final String ACTION_EMAIL_URL =
            "%s/admin/realms/%s/users/%s/execute-actions-email";
    public static final String USER_URL = "%s/admin/realms/%s/users";
}
