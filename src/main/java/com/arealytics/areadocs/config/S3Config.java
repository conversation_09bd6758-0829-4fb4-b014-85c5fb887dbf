package com.arealytics.areadocs.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;

@Configuration
public class S3Config {

    @Value("${aws.access.key.id}")
    private String accessKey;

    @Value("${aws.secret.access.key}")
    private String secretAccessKey;

    @Value("${aws.region}")
    private String region;

    @Bean
    public S3Client s3Client() {

        if (accessKey == null
                || accessKey.isEmpty()
                || secretAccessKey == null
                || secretAccessKey.isEmpty()) {
            System.err.println(
                    "WARNING: AWS Access Key ID or Secret Access Key is missing or empty. S3 client"
                            + " will NOT be initialized.");
            return null;
        }
        AwsBasicCredentials credentials = AwsBasicCredentials.create(accessKey, secretAccessKey);
        return S3Client.builder()
                .region(Region.of(region))
                .credentialsProvider(StaticCredentialsProvider.create(credentials))
                .build();
    }

    @Bean
    public S3Presigner s3Presigner() {
        if (accessKey == null
                || accessKey.isEmpty()
                || secretAccessKey == null
                || secretAccessKey.isEmpty()) {
            System.err.println(
                    "WARNING: AWS Access Key ID or Secret Access Key is missing or empty."
                            + " S3Presigner will NOT be initialized.");
            return null;
        }
        AwsBasicCredentials credentials = AwsBasicCredentials.create(accessKey, secretAccessKey);
        return S3Presigner.builder()
                .region(Region.of(region))
                .credentialsProvider(StaticCredentialsProvider.create(credentials))
                .build();
    }
}
