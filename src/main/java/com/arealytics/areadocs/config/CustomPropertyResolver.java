package com.arealytics.areadocs.config;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.core.env.ConfigurableEnvironment;

public class CustomPropertyResolver implements EnvironmentPostProcessor {
    
    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        // Disable property placeholder resolution that might be causing the error
        System.setProperty("spring.devtools.restart.enabled", "false");
    }
}