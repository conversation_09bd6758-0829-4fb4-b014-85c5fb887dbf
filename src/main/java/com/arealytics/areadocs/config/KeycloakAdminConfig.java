package com.arealytics.areadocs.config;

import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.KeycloakBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class KeycloakAdminConfig {

    @Value("${keycloak.serverUrl}")
    private String serverUrl;

    @Value("${keycloak.clientSecret}")
    private String clientSecret;

    @Value("${keycloak.clientId}")
    private String clientId;

    @Value("${keycloak.realm}")
    private String realm;

    @Value("${keycloak.grantType}")
    private String grantType;

    @Bean
    public Keycloak keycloakAdminClient() {
        return KeycloakBuilder.builder()
                .serverUrl(serverUrl)
                .realm(realm)
                .grantType(grantType)
                .clientId(clientId)
                .clientSecret(clientSecret)
                .build();
    }
}
