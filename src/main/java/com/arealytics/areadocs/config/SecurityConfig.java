package com.arealytics.areadocs.config;

import java.util.Arrays;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

@Configuration
@EnableWebSecurity
public class SecurityConfig {

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http.csrf(csrf -> csrf.disable())
                .cors(cors -> cors.configurationSource(corsConfigurationSource()))
                .authorizeHttpRequests(
                        authorize ->
                                authorize
                                        // Public endpoints
                                        .requestMatchers(
                                                "/",
                                                "/login",
                                                "/register",
                                                "/v3/api-docs/**",
                                                "/swagger-ui/**",
                                                "/swagger-ui.html")
                                        .permitAll()
                                        // Protected endpoints
                                        .anyRequest()
                                        .permitAll())
                .oauth2ResourceServer(oauth2 -> oauth2.jwt(Customizer.withDefaults()));

        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOrigins(Arrays.asList("*"));
        configuration.setAllowedMethods(
                Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"));
        configuration.setAllowedHeaders(Arrays.asList("*"));
        configuration.setMaxAge(3600L);

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);
        return source;
    }
    // needed when authenticating with respective to roles

    //     @Bean
    //     public JwtAuthenticationConverter jwtAuthenticationConverter() {
    //         JwtGrantedAuthoritiesConverter jwtGrantedAuthoritiesConverter =
    //                 new JwtGrantedAuthoritiesConverter();
    //         jwtGrantedAuthoritiesConverter.setAuthoritiesClaimName("roles");
    //         jwtGrantedAuthoritiesConverter.setAuthorityPrefix("ROLE_");

    //         JwtAuthenticationConverter jwtAuthenticationConverter = new
    // JwtAuthenticationConverter();
    //         jwtAuthenticationConverter.setJwtGrantedAuthoritiesConverter(
    //                 jwtGrantedAuthoritiesConverter);
    //         return jwtAuthenticationConverter;
    //     }
}
