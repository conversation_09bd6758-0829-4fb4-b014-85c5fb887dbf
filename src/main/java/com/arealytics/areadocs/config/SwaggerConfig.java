package com.arealytics.areadocs.config;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;

@Configuration
public class SwaggerConfig {
    @Value("${spring.security.oauth2.resourceserver.jwt.issuer-uri}")
    private String issuerUri;

    @Value("${spring.security.oauth2.client.registration.keycloak.redirect-uri}")
    private String redirectUri;

    @Value("${swagger.server.url}")
    private String swaggerUrl;

    @Value("${swagger.server.description}")
    private String swaggerDescription;

    @Bean
    public OpenAPI customOpenAPI(Environment environment) {
        Server activeServer = new Server().url(swaggerUrl).description(swaggerDescription);

        return new OpenAPI()
                .components(
                        new Components()
                                .addSecuritySchemes(
                                        "bearer-jwt",
                                        new SecurityScheme()
                                                .type(SecurityScheme.Type.HTTP)
                                                .scheme("bearer")
                                                .bearerFormat("JWT")
                                                .description(
                                                        "Enter the JWT token obtained after logging"
                                                                + " in. The token can be found in"
                                                                + " localStorage after"
                                                                + " authentication.")))
                .info(
                        new Info()
                                .title("Areadocs Documentation")
                                .version("1.0")
                                .description("API documentation for the Areadocs Application"))
                .servers(List.of(activeServer))
                .addSecurityItem(new SecurityRequirement().addList("bearer-jwt"));
    }
}
