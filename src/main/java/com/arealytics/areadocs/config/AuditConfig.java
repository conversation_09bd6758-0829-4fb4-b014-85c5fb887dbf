package com.arealytics.areadocs.config;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

import com.arealytics.areadocs.security.SecurityUtils;

@Configuration
@EnableJpaAuditing(auditorAwareRef = "auditorProvider")
public class AuditConfig {

    @Autowired private SecurityUtils securityUtils;

    @Bean
    public AuditorAware<String> auditorProvider() {
        return () -> {
            String currentUser = securityUtils.getCurrentUserEmail();
            return Optional.ofNullable(currentUser).or(() -> Optional.of("system"));
        };
    }
}
