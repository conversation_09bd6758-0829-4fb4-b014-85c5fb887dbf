package com.arealytics.areadocs.config.audit;

import org.hibernate.envers.RevisionListener;

import com.arealytics.areadocs.util.UserContext;

/**
 * Custom revision listener for Hibernate Envers. This listener is called when a new revision is
 * created and populates the revision entity with additional information such as the user who made
 * the change.
 */
public class CustomRevisionL<PERSON>ener implements RevisionListener {

    @Override
    public void newRevision(Object revisionEntity) {
        CustomRevisionEntity revision = (CustomRevisionEntity) revisionEntity;

        // Get the current user ID from the UserContext
        Long userId = UserContext.getCurrentUserId();

        // Set the user ID in the revision entity
        revision.setChangedBy(userId);
    }
}
