package com.arealytics.areadocs.controller;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.arealytics.areadocs.domain.Company;
import com.arealytics.areadocs.domain.Folder;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.dto.requestDTO.FolderRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.ApiResponse;
import com.arealytics.areadocs.dto.responseDTO.FolderResponseDTO;
import com.arealytics.areadocs.dto.responseDTO.FolderTreeDTO;
import com.arealytics.areadocs.enumeration.UserType;
import com.arealytics.areadocs.exception.UnauthorizedException;
import com.arealytics.areadocs.mapper.FolderMapper;
import com.arealytics.areadocs.service.FolderService;
import com.arealytics.areadocs.util.AuthUtils;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/folders")
@Validated
@Slf4j
@RequiredArgsConstructor
public class FolderController {

    private final FolderService folderService;
    private final AuthUtils authUtils;
    private final FolderMapper folderMapper;

    /**
     * Create a new folder
     *
     * @param requestDTO
     * @return
     */
    @PostMapping
    @Operation(
            summary = "Create a new folder",
            description = "Creates a new folder with the provided details")
    public ResponseEntity<ApiResponse<FolderResponseDTO>> createFolder(
            @Valid @RequestBody FolderRequestDTO requestDTO) {
        // Validate current user has permission to create folder
        User currentUser = authUtils.getCurrentUser();
        Company currentUserCompany = authUtils.getCurrentUserCompany();

        Folder createdFolder =
                folderService.createFolder(requestDTO, currentUser, currentUserCompany);

        FolderResponseDTO folderResponseDTO = folderMapper.toDto(createdFolder);

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponse.success("Folder created successfully", folderResponseDTO));
    }

    /**
     * Get all folders
     *
     * @param id
     * @param parentId
     * @return
     */
    @GetMapping
    @Operation(summary = "Get folders", description = "Retrieves folders based on provided filters")
    public ResponseEntity<ApiResponse<List<FolderResponseDTO>>> getAllFolders(
            @RequestParam(required = false) Long id,
            @RequestParam(required = false) Long parentId) {

        User currentUser = authUtils.getCurrentUser();
        Company currentUserCompany = authUtils.getCurrentUserCompany();

        if (id != null || parentId != null) {
            Folder folder = folderService.getFolderById(id != null ? id : parentId);
            if (folderService.hasReadAccessToFolder(currentUser, currentUserCompany, folder)) {
                throw new UnauthorizedException("User does not have access to this folder");
            }
        }

        List<Folder> folders;
        if (id != null) {
            folders = List.of(folderService.getFolderById(id));
        } else if (parentId != null) {
            // 1. If parentId is provided, fetch child folders
            folders = folderService.getChildFolders(parentId, currentUser, currentUserCompany);
        } else {
            // 4. Nothing provided, use current user context
            if (currentUser.getUserType() == UserType.COMPANY
                    && currentUser.getCompanyMemberships() != null
                    && currentUser.getCompanyMemberships().getCompany() != null) {

                folders =
                        folderService.getFoldersByCompanyId(
                                currentUser.getCompanyMemberships().getCompany().getId());
            } else {
                folders = folderService.getFoldersByUserId(currentUser.getId());
            }
        }

        return ResponseEntity.ok(
                ApiResponse.success(
                        "Folders retrieved successfully",
                        folders.stream().map(folderMapper::toDto).toList()));
    }

    /**
     * @param folderId
     * @param requestDTO
     * @return
     */
    @PutMapping("/{folderId}")
    @Operation(
            summary = "Update folder",
            description = "Updates a folder with the provided details")
    public ResponseEntity<ApiResponse<FolderResponseDTO>> updateFolder(
            @PathVariable Long folderId, @Valid @RequestBody FolderRequestDTO requestDTO) {

        User currentUser = authUtils.getCurrentUser();
        Company currentUserCompany = authUtils.getCurrentUserCompany();

        // Additional validation to ensure user can update this specific folder
        Folder existingFolder = folderService.getFolderById(folderId);
        if (folderService.hasWriteAccessToFolder(currentUser, currentUserCompany, existingFolder)) {
            throw new UnauthorizedException("User does not have access to this folder");
        }
        Folder updatedFolder =
                folderService.updateFolder(folderId, requestDTO, currentUser, currentUserCompany);

        FolderResponseDTO updatedFolderDTO = folderMapper.toDto(updatedFolder);

        return ResponseEntity.ok(
                ApiResponse.success("Folder updated successfully", updatedFolderDTO));
    }

    /**
     * Move a folder to another folder
     *
     * @param id
     * @param newParentId
     * @return
     */
    @PutMapping("/move/{id}")
    @Operation(summary = "Move folder", description = "Moves a folder to another folder")
    public ResponseEntity<ApiResponse<FolderResponseDTO>> moveFolder(
            @PathVariable Long id, @RequestParam Long newParentId) {

        if (id == null || newParentId == null) {
            throw new IllegalArgumentException("Both folder ID and new parent ID are required");
        }

        User currentUser = authUtils.getCurrentUser();
        Company currentUserCompany = authUtils.getCurrentUserCompany();

        Folder folder = folderService.getFolderById(id);
        Folder newParentFolder = folderService.getFolderById(newParentId);

        if (currentUser.getUserType() == UserType.INDIVIDUAL) {
            if (folder.getUser() == null
                    || !folder.getUser().getId().equals(currentUser.getId())
                    || newParentFolder.getUser() == null
                    || !newParentFolder.getUser().getId().equals(currentUser.getId())) {
                log.warn(
                        "Unauthorized folder move attempt by INDIVIDUAL user {} for folder {} to"
                                + " {}",
                        currentUser.getId(),
                        id,
                        newParentId);
                throw new UnauthorizedException("Individual users can only move their own folders");
            }
        }

        if (folderService.hasWriteAccessToFolder(currentUser, currentUserCompany, folder)
                || folderService.hasWriteAccessToFolder(
                        currentUser, currentUserCompany, newParentFolder)) {
            throw new UnauthorizedException(
                    "User does not have access to move to the specified folder");
        }

        Folder movedFolder = folderService.moveFolder(id, newParentId);
        FolderResponseDTO movedFolderDTO = folderMapper.toDto(movedFolder);

        return ResponseEntity.ok(ApiResponse.success("Folder moved successfully", movedFolderDTO));
    }

    /**
     * Delete a folder
     *
     * @param id
     * @return
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete folder", description = "Deletes a folder by its ID")
    public ResponseEntity<ApiResponse<Void>> deleteFolder(@PathVariable Long id) {
        if (id == null) {
            return ResponseEntity.badRequest().body(ApiResponse.error("Folder ID is required"));
        }

        User currentUser = authUtils.getCurrentUser();
        Company currentUserCompany = authUtils.getCurrentUserCompany();

        // Validate user has permission to delete this folder
        Folder folder = folderService.getFolderById(id);
        if (folderService.hasWriteAccessToFolder(currentUser, currentUserCompany, folder)) {
            throw new UnauthorizedException("User does not have access to this folder");
        }

        folderService.deleteFolder(id);

        return ResponseEntity.ok(ApiResponse.success("Folder deleted successfully", null));
    }

    /**
     * Get folder tree
     *
     * @param rootId
     * @return
     */
    @GetMapping("/tree/{rootId}")
    @Operation(
            summary = "Get folder tree",
            description = "Retrieves the folder tree starting from the specified root folder")
    public ResponseEntity<ApiResponse<FolderTreeDTO>> getFolderTree(@PathVariable Long rootId) {
        User currentUser = authUtils.getCurrentUser();
        Company currentUserCompany = authUtils.getCurrentUserCompany();

        Folder rootFolder = folderService.getFolderById(rootId);

        if (folderService.hasReadAccessToFolder(currentUser, currentUserCompany, rootFolder)) {
            throw new UnauthorizedException("User does not have access to this folder");
        }

        FolderTreeDTO tree = folderService.getFolderTree(rootFolder, currentUser);
        return ResponseEntity.ok(ApiResponse.success("Folder tree retrieved successfully", tree));
    }
}
