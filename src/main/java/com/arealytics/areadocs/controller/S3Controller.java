package com.arealytics.areadocs.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.arealytics.areadocs.service.S3Service;

@RestController
@RequestMapping("/s3")
public class S3Controller {

    @Autowired private S3Service s3Service;

    @GetMapping("/presigned-url")
    public ResponseEntity<PresignedUrlResponse> getPresignedUrl(
            @RequestParam String userEmail, @RequestParam String fileName) {
        try {
            String presignedUrl = s3Service.generatePresignedUrl(userEmail, fileName);
            String fileUrl = s3Service.getFileUrlFromPresignedUrl(presignedUrl);
            PresignedUrlResponse response = new PresignedUrlResponse(presignedUrl, fileUrl);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            PresignedUrlResponse errorResponse =
                    new PresignedUrlResponse(
                            null, null, "Failed to generate pre-signed URL: " + e.getMessage());
            return ResponseEntity.status(500).body(errorResponse);
        }
    }

    public static class PresignedUrlResponse {
        private final String presignedUrl;
        private final String fileUrl;
        private final String error;

        public PresignedUrlResponse(String presignedUrl, String fileUrl) {
            this.presignedUrl = presignedUrl;
            this.fileUrl = fileUrl;
            this.error = null;
        }

        public PresignedUrlResponse(String presignedUrl, String fileUrl, String error) {
            this.presignedUrl = presignedUrl;
            this.fileUrl = fileUrl;
            this.error = error;
        }

        public String getPresignedUrl() {
            return presignedUrl;
        }

        public String getFileUrl() {
            return fileUrl;
        }

        public String getError() {
            return error;
        }
    }
}
