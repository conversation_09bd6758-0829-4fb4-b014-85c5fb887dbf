package com.arealytics.areadocs.controller;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.arealytics.areadocs.domain.Transaction;
import com.arealytics.areadocs.dto.responseDTO.ApiResponse;
import com.arealytics.areadocs.dto.responseDTO.TransactionDTO;
import com.arealytics.areadocs.mapper.TransactionMapper;
import com.arealytics.areadocs.service.TransactionService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/transactions")
@RequiredArgsConstructor
public class TransactionController {

    private final TransactionService transactionService;
    private final TransactionMapper transactionMapper;

    @GetMapping("{type}/{relationId}")
    public ResponseEntity<ApiResponse<List<TransactionDTO>>> getTransactionsByTypeAndId(
            @PathVariable String type,
            @PathVariable Long relationId) {
        log.info("Fetching all transactions for {} with id {}", type, relationId);

        List<Transaction> transactions;
        String successMessage;

        switch (type.toLowerCase()) {
            case "user" -> {
                transactions = transactionService.getTransactionsByUserId(relationId);
                successMessage = "User transactions retrieved successfully";
            }
            case "company" -> {
                transactions = transactionService.getTransactionsByCompanyId(relationId);
                successMessage = "Company transactions retrieved successfully";
            }
            default -> {
                log.warn("Invalid transaction type requested: {}", type);
                return ResponseEntity.badRequest().body(
                        ApiResponse.error("Invalid type. Supported types are: user, company"));
            }
        }

        List<TransactionDTO> response =
                transactions.stream().map(transactionMapper::toDto).collect(Collectors.toList());

        return ResponseEntity.ok(
                ApiResponse.success(successMessage, response));
    }



    @GetMapping("/id/{transactionId}")
    public ResponseEntity<ApiResponse<TransactionDTO>> getTransactionById(
            @PathVariable Long transactionId) {
        log.info("Fetching transaction by id {}", transactionId);

        Transaction transaction = transactionService.getTransactionById(transactionId);
        TransactionDTO response = transactionMapper.toDto(transaction);

        return ResponseEntity.ok(
                ApiResponse.success("Transaction retrieved successfully", response));
    }
}
