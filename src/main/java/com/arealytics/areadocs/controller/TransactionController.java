package com.arealytics.areadocs.controller;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.arealytics.areadocs.domain.Transaction;
import com.arealytics.areadocs.dto.responseDTO.ApiResponse;
import com.arealytics.areadocs.dto.responseDTO.TransactionDTO;
import com.arealytics.areadocs.mapper.TransactionMapper;
import com.arealytics.areadocs.service.TransactionService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/transactions")
@RequiredArgsConstructor
public class TransactionController {

    private final TransactionService transactionService;
    private final TransactionMapper transactionMapper;

//    @GetMapping("user/{userId}")
//    public ResponseEntity<ApiResponse<List<TransactionDTO>>> getAllTransactionsForUser(
//            @PathVariable Long userId) {
//        log.info("Fetching all transactions for user id {}", userId);
//
//        List<Transaction> transactions = transactionService.getTransactionsByUserId(userId);
//        List<TransactionDTO> response =
//                transactions.stream().map(transactionMapper::toDto).collect(Collectors.toList());
//
//        return ResponseEntity.ok(
//                ApiResponse.success("Transactions retrieved successfully", response));
//    }
//    @GetMapping("company/{companyId}")
//    public ResponseEntity<ApiResponse<List<TransactionDTO>>> getAllTransactionsForCompany(
//            @PathVariable Long companyId) {
//        log.info("Fetching all transactions for company id {}", companyId);
//
//        List<Transaction> transactions = transactionService.getTransactionsByCompanyId(companyId);
//        List<TransactionDTO> response =
//                transactions.stream().map(transactionMapper::toDto).collect(Collectors.toList());
//
//        return ResponseEntity.ok(
//                ApiResponse.success("Transactions retrieved successfully", response));
//    }
    


    @GetMapping("/{transactionId}")
    public ResponseEntity<ApiResponse<TransactionDTO>> getTransactionById(
            @PathVariable Long transactionId) {
        log.info("Fetching transaction by id {}", transactionId);

        Transaction transaction = transactionService.getTransactionById(transactionId);
        TransactionDTO response = transactionMapper.toDto(transaction);

        return ResponseEntity.ok(
                ApiResponse.success("Transaction retrieved successfully", response));
    }
}
