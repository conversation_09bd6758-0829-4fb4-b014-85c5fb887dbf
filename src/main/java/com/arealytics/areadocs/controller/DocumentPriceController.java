package com.arealytics.areadocs.controller;

import java.util.Collections;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.arealytics.areadocs.dto.requestDTO.DocumentPriceRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.ApiResponse;
import com.arealytics.areadocs.dto.responseDTO.DocumentPriceResponseDTO;
import com.arealytics.areadocs.exception.CompanyException;
import com.arealytics.areadocs.service.DocumentPriceService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.persistence.EntityNotFoundException;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/document-price")
@RequiredArgsConstructor
@Tag(name = "Document Price Controller", description = "APIs for managing document prices")
public class DocumentPriceController {

    private final DocumentPriceService documentPriceService;

    @PostMapping
    @Operation(
            summary = "Create a new document price",
            description =
                    "Creates a new document price with the provided details. Required fields:"
                            + " productCodeId, basePrice, effectiveDate, gst. Optional fields:"
                            + " documentTypeId, effectiveBasePrice, expiryDate.")
    public ResponseEntity<ApiResponse<DocumentPriceResponseDTO>> createDocumentPrice(
            @Valid @RequestBody DocumentPriceRequestDTO documentPriceRequestDTO) {
        try {
            DocumentPriceResponseDTO createdDocumentPrice =
                    documentPriceService.createDocumentPrice(documentPriceRequestDTO);
            return new ResponseEntity<>(
                    ApiResponse.success(
                            "Document price created successfully", createdDocumentPrice),
                    HttpStatus.CREATED);
        } catch (CompanyException e) {
            return new ResponseEntity<>(ApiResponse.error(e.getMessage()), HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return new ResponseEntity<>(
                    ApiResponse.error(
                            "An error occurred while creating the document price: "
                                    + e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @GetMapping
    @Operation(
            summary = "Get document prices",
            description =
                    "Retrieves a single document price by ID if provided, or a paginated list of"
                            + " document prices filtered by product code ID or document type ID if"
                            + " provided, or all document prices if no parameters are provided.")
    public ResponseEntity<ApiResponse<Page<DocumentPriceResponseDTO>>> getDocumentPrices(
            @Parameter(description = "Document price ID") @RequestParam(required = false) Long id,
            @Parameter(description = "Product code ID") @RequestParam(required = false)
                    Long productCodeId,
            @Parameter(description = "Document type ID") @RequestParam(required = false)
                    Long documentTypeId,
            @Parameter(description = "Page number") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort field and direction (e.g., id,desc)")
                    @RequestParam(defaultValue = "id,desc")
                    String sort) {
        try {
            if (id != null) {
                DocumentPriceResponseDTO documentPrice =
                        documentPriceService.getDocumentPriceById(id);
                Page<DocumentPriceResponseDTO> singlePage =
                        new PageImpl<>(
                                Collections.singletonList(documentPrice), PageRequest.of(0, 1), 1);
                return ResponseEntity.ok(ApiResponse.success(singlePage));
            }

            String[] sortParams = sort.split(",");
            Sort.Direction direction =
                    sortParams.length > 1 && sortParams[1].equalsIgnoreCase("desc")
                            ? Sort.Direction.DESC
                            : Sort.Direction.ASC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortParams[0]));

            Page<DocumentPriceResponseDTO> documentPrices =
                    documentPriceService.getDocumentPrices(productCodeId, documentTypeId, pageable);
            return ResponseEntity.ok(ApiResponse.success(documentPrices));
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>(ApiResponse.error(e.getMessage()), HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            return new ResponseEntity<>(
                    ApiResponse.error(
                            "An error occurred while retrieving document prices: "
                                    + e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PutMapping("/{id}")
    @Operation(
            summary = "Update a document price",
            description =
                    "Updates a document price with the provided details. Required fields:"
                        + " productCodeId, basePrice, effectiveDate, gst. Optional fields:"
                        + " documentTypeId, effectiveBasePrice, expiryDate. Updates will cascade to"
                        + " corresponding CompanySpecialPrice records.")
    public ResponseEntity<ApiResponse<DocumentPriceResponseDTO>> updateDocumentPrice(
            @Parameter(description = "Document price ID", required = true) @PathVariable Long id,
            @Valid @RequestBody DocumentPriceRequestDTO documentPriceRequestDTO) {
        try {
            DocumentPriceResponseDTO updatedDocumentPrice =
                    documentPriceService.updateDocumentPrice(id, documentPriceRequestDTO);
            return ResponseEntity.ok(
                    ApiResponse.success(
                            "Document price and related company special prices updated"
                                    + " successfully",
                            updatedDocumentPrice));
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>(ApiResponse.error(e.getMessage()), HttpStatus.NOT_FOUND);
        } catch (CompanyException e) {
            return new ResponseEntity<>(ApiResponse.error(e.getMessage()), HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return new ResponseEntity<>(
                    ApiResponse.error(
                            "An error occurred while updating the document price: "
                                    + e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @DeleteMapping("/{id}")
    @Operation(
            summary = "Delete a document price",
            description = "Soft deletes a document price by its ID")
    public ResponseEntity<ApiResponse<Void>> deleteDocumentPrice(
            @Parameter(description = "Document price ID", required = true) @PathVariable Long id) {
        try {
            documentPriceService.deleteDocumentPrice(id);
            return ResponseEntity.ok(ApiResponse.success("Document price deleted successfully"));
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>(ApiResponse.error(e.getMessage()), HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            return new ResponseEntity<>(
                    ApiResponse.error(
                            "An error occurred while deleting the document price: "
                                    + e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
