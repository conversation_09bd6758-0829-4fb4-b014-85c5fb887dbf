package com.arealytics.areadocs.controller;

import java.util.Collections;

import org.springframework.data.domain.*;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.arealytics.areadocs.dto.requestDTO.UserRequestDTO;
import com.arealytics.areadocs.dto.requestDTO.UserSchemaFilterDTO;
import com.arealytics.areadocs.dto.responseDTO.ApiResponse;
import com.arealytics.areadocs.dto.responseDTO.UserResponseDTO;
import com.arealytics.areadocs.enumeration.PasswordActionType;
import com.arealytics.areadocs.enumeration.SortOrder;
import com.arealytics.areadocs.enumeration.UserType;
import com.arealytics.areadocs.exception.KeycloakException;
import com.arealytics.areadocs.service.UserService;
import com.arealytics.areadocs.util.AuthUtils;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.persistence.EntityNotFoundException;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "User Controller", description = "APIs for managing users")
public class UserController {

    private final UserService userService;
    private final AuthUtils authUtils;

    /** Create a user and send an initial password setup email. */
    @PostMapping
    @Operation(
            summary = "Create a new user and send password setup email",
            description =
                    "Creates a new user with the provided details in Keycloak and the database, and"
                        + " sends an email with a link to set their initial password. Can associate"
                        + " with an existing company (using companyId). Company creation is not"
                        + " allowed during user creation. Required fields: email, password,"
                        + " firstName, lastName. Profile picture URL can be included in the request"
                        + " body.")
    public ResponseEntity<ApiResponse<UserResponseDTO>> createUser(
            @Valid @RequestBody UserRequestDTO userRequestDTO) {
        try {
            // Set default UserType based on companyId
            if (userRequestDTO.getUserType() == null) {
                userRequestDTO.setUserType(
                        userRequestDTO.getCompanyId() != null && userRequestDTO.getCompanyId() > 0
                                ? UserType.COMPANY
                                : UserType.INDIVIDUAL);
            }

            log.info("Creating user with email: {}", userRequestDTO.getEmail());
            UserResponseDTO createdUser =
                    userService.createUser(userRequestDTO, userRequestDTO.getProfilePictureUrl());
            userService.sendPasswordUpdateEmail(
                    createdUser.getKeycloakId(), false, PasswordActionType.INITIAL_SETUP);

            log.info("User created successfully with ID: {}", createdUser.getId());
            return ResponseEntity.status(HttpStatus.CREATED)
                    .body(ApiResponse.success("User created successfully", createdUser));

        } catch (IllegalArgumentException e) {
            log.error(
                    "Validation error creating user with email {}: {}",
                    userRequestDTO.getEmail(),
                    e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Validation error: " + e.getMessage()));
        } catch (KeycloakException e) {
            log.error(
                    "Keycloak error creating user with email {}: {}",
                    userRequestDTO.getEmail(),
                    e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_GATEWAY)
                    .body(ApiResponse.error("Keycloak error: " + e.getMessage()));
        } catch (Exception e) {
            log.error(
                    "Unexpected error creating user with email {}: {}",
                    userRequestDTO.getEmail(),
                    e.getMessage(),
                    e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("Error creating user: " + e.getMessage()));
        }
    }

    /** Send a password reset email to a user. */
    @PostMapping("/reset-password")
    public ResponseEntity<ApiResponse<UserResponseDTO>> resetPassword(
            @Parameter(description = "Email address of the user") @RequestParam String email) {
        try {
            log.info("Sending password reset email for: {}", email);
            userService.sendPasswordUpdateEmail(email, true, PasswordActionType.PASSWORD_RESET);
            log.info("Password reset email sent successfully for: {}", email);
            return ResponseEntity.ok(
                    ApiResponse.success("Password reset email sent successfully", null));
        } catch (EntityNotFoundException e) {
            log.error("User not found for email {}: {}", email, e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("User not found: " + e.getMessage()));
        } catch (KeycloakException e) {
            log.error(
                    "Keycloak error sending password reset email for {}: {}",
                    email,
                    e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_GATEWAY)
                    .body(ApiResponse.error("Keycloak error: " + e.getMessage()));
        } catch (Exception e) {
            log.error(
                    "Unexpected error sending password reset email for {}: {}",
                    email,
                    e.getMessage(),
                    e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(
                            ApiResponse.error(
                                    "Error sending password reset email: " + e.getMessage()));
        }
    }

    // GET - Unified User API
    @GetMapping
    @Operation(
            summary = "Get users",
            description =
                    "Returns users based on provided parameters. If no parameters are provided,"
                            + " returns all users.")
    public ResponseEntity<ApiResponse<Page<UserResponseDTO>>> getUsers(
            @Parameter(description = "User ID") @RequestParam(required = false) Long id,
            @Parameter(description = "User email") @RequestParam(required = false) String email,
            @Parameter(description = "Company ID") @RequestParam(required = false) Long companyId,
            @Parameter(description = "User type") @RequestParam(required = false) UserType userType,
            @Parameter(description = "Page number") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "id")
                    String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "ASCEND")
                    SortOrder sortDir) {
        Sort sort =
                sortDir.equals(SortOrder.DESCEND)
                        ? Sort.by(sortBy).descending()
                        : Sort.by(sortBy).ascending();

        Pageable pageable = PageRequest.of(page, size, sort);
        Page<UserResponseDTO> userPage;

        // If ID is provided, return a single user by ID
        if (id != null) {
            UserResponseDTO user = userService.getUserById(id);
            userPage = new PageImpl<>(Collections.singletonList(user), pageable, 1);
        }
        // If email is provided, return a single user by email
        else if (email != null && !email.isEmpty()) {
            UserResponseDTO user = userService.getUserByEmail(email);
            userPage = new PageImpl<>(Collections.singletonList(user), pageable, 1);
        }
        // If companyId is provided, return users associated with the company
        else if (companyId != null) {
            userPage = userService.getUsersByCompanyId(companyId, pageable);
        }
        // If userType or status is provided, use the search functionality
        else if (userType != null) {
            UserSchemaFilterDTO filterDTO = new UserSchemaFilterDTO();
            filterDTO.setUserType(userType);
            userPage = userService.getUsers(filterDTO, pageable);
        }
        // If no specific parameters, return all users with pagination
        else {
            userPage = userService.getAllUsers(pageable);
        }

        return ResponseEntity.ok(ApiResponse.success("Users retrieved successfully", userPage));
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update user", description = "Updates a user with the specified ID")
    public ResponseEntity<ApiResponse<UserResponseDTO>> updateUser(
            @Parameter(description = "User ID", required = true) @PathVariable Long id,
            @RequestBody @Valid UserRequestDTO userDTO) {
        UserResponseDTO updatedUser =
                userService.updateUser(id, userDTO, userDTO.getProfilePictureUrl());
        return ResponseEntity.ok(ApiResponse.success("User updated successfully", updatedUser));
    }

    // DELETE - Delete User
    @DeleteMapping("/{userId}")
    @Operation(
            summary = "Delete a user",
            description =
                    "Deletes a user from Keycloak and sets their status to DELETED in the"
                            + " database")
    public ResponseEntity<ApiResponse<String>> deleteUser(@PathVariable Long userId) {
        try {
            userService.deleteUser(userId);
            return ResponseEntity.ok(ApiResponse.success("User deleted successfully", null));
        } catch (EntityNotFoundException e) {
            log.error("User not found with ID: {}", userId);
            return new ResponseEntity<>(
                    ApiResponse.error("User not found with ID: " + userId), HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            log.error("Error deleting user: {}", e.getMessage());
            return new ResponseEntity<>(
                    ApiResponse.error("Error deleting user: " + e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * Get the current user's information.
     *
     * @return ResponseEntity with user information
     */
    @GetMapping("/current-user")
    @Operation(
            summary = "Get current user",
            description = "Retrieves the current user's information")
    public ResponseEntity<ApiResponse<UserResponseDTO>> getCurrentUser() {
        log.info("Retrieving current user");
        String email = authUtils.getLoggedInEmail();
        UserResponseDTO user = userService.getUserByEmail(email);
        return ResponseEntity.ok(ApiResponse.success("User retrieved successfully", user));
    }

    @PatchMapping("/{id}/active")
    @Operation(
            summary = "Set user active status",
            description =
                    "Activates or deactivates a user by its ID performing a soft delete when"
                            + " deactivated.")
    public ResponseEntity<ApiResponse<UserResponseDTO>> updateUserActiveStatus(
            @Parameter(description = "User ID", required = true) @PathVariable Long id,
            @Parameter(
                            description = "Set user active or inactive",
                            required = true,
                            example = "true")
                    @RequestParam
                    Boolean isActive) {
        UserResponseDTO updatedUser = userService.updateUserActiveStatus(id, isActive);
        return ResponseEntity.ok(
                ApiResponse.success("User active status updated successfully", updatedUser));
    }

    // Search functionality has been incorporated into the unified GET endpoint

}
