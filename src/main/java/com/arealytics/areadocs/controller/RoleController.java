package com.arealytics.areadocs.controller;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.arealytics.areadocs.dto.requestDTO.RoleFilterDTO;
import com.arealytics.areadocs.dto.requestDTO.RolePermissionAssignmentRequestDTO;
import com.arealytics.areadocs.dto.requestDTO.RoleRequestDTO;
import com.arealytics.areadocs.dto.requestDTO.RoleUserAssignmentRequestDTO;
import com.arealytics.areadocs.dto.requestDTO.UserRoleRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.ApiResponse;
import com.arealytics.areadocs.dto.responseDTO.RoleDTO;
import com.arealytics.areadocs.dto.responseDTO.RolePermissionDTO;
import com.arealytics.areadocs.dto.responseDTO.UserRoleDTO;
import com.arealytics.areadocs.service.RolePermissionService;
import com.arealytics.areadocs.service.RoleService;
import com.arealytics.areadocs.service.UserRoleService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/roles")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Role Controller", description = "APIs for managing roles and role permissions")
public class RoleController {

    private final RoleService roleService;
    private final RolePermissionService rolePermissionService;
    private final UserRoleService userRoleService;

    // POST - Create Role
    @PostMapping
    @Operation(
            summary = "Create a new role",
            description = "Creates a new role with the provided details")
    public ResponseEntity<ApiResponse<RoleDTO>> createRole(
            @Valid @RequestBody RoleRequestDTO roleRequestDTO) {
        RoleDTO createdRole = roleService.createRole(roleRequestDTO);
        return new ResponseEntity<>(
                ApiResponse.success("Role created successfully", createdRole), HttpStatus.CREATED);
    }

    // GET - Get Roles (unified endpoint for roles with filters)
    @GetMapping
    @Operation(
            summary = "Get roles",
            description = "Retrieves roles based on provided filters with pagination")
    public ResponseEntity<ApiResponse<?>> getRoles(
            @Parameter(description = "Role ID") @RequestParam(required = false) Long roleId,
            @Parameter(description = "Role Name") @RequestParam(required = false) String name,
            @Parameter(description = "Page number") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort field and direction (e.g., createdAt,desc)")
                    @RequestParam(defaultValue = "createdAt,desc")
                    String sort) {

        // If roleId query parameter is provided, return that specific role
        if (roleId != null) {
            Optional<RoleDTO> roleOpt = roleService.findRoleById(roleId);
            if (roleOpt.isPresent()) {
                Page<RoleDTO> rolePage = new PageImpl<>(Collections.singletonList(roleOpt.get()));
                return ResponseEntity.ok(ApiResponse.success(rolePage));
            } else {
                Page<RoleDTO> emptyPage = Page.empty();
                return ResponseEntity.ok(ApiResponse.success(emptyPage));
            }
        }

        // If name is provided, try to find by name
        if (name != null && !name.trim().isEmpty()) {
            try {
                RoleDTO role = roleService.getRoleByName(name);
                Page<RoleDTO> rolePage = new PageImpl<>(Collections.singletonList(role));
                return ResponseEntity.ok(ApiResponse.success(rolePage));
            } catch (Exception e) {
                // If not found, continue with filter search
            }
        }

        String[] sortParams = sort.split(",");
        Sort.Direction direction =
                sortParams.length > 1 && sortParams[1].equalsIgnoreCase("desc")
                        ? Sort.Direction.DESC
                        : Sort.Direction.ASC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortParams[0]));

        RoleFilterDTO filter = new RoleFilterDTO();
        filter.setName(name);

        Page<RoleDTO> roles = roleService.getRolesWithFilters(filter, pageable);
        return ResponseEntity.ok(ApiResponse.success(roles));
    }

    // PUT - Update Role
    @PutMapping("/{id}")
    @Operation(summary = "Update a role", description = "Updates a role with the provided details")
    public ResponseEntity<ApiResponse<RoleDTO>> updateRole(
            @Parameter(description = "Role ID", required = true) @PathVariable Long id,
            @Valid @RequestBody RoleRequestDTO roleRequestDTO) {
        RoleDTO updatedRole = roleService.updateRole(id, roleRequestDTO);
        return ResponseEntity.ok(ApiResponse.success("Role updated successfully", updatedRole));
    }

    // DELETE - Delete Role
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete a role", description = "Deletes a role by its ID")
    public ResponseEntity<ApiResponse<Void>> deleteRole(
            @Parameter(description = "Role ID", required = true) @PathVariable Long id) {
        roleService.deleteRole(id);
        return ResponseEntity.ok(ApiResponse.success("Role deleted successfully"));
    }

    // POST - Assign Permissions to Role
    @PostMapping("/{roleId}/permissions")
    @Operation(
            summary = "Assign permissions to role",
            description = "Assigns one or more permissions to a role")
    public ResponseEntity<ApiResponse<List<RolePermissionDTO>>> assignPermissionsToRole(
            @Parameter(description = "Role ID", required = true) @PathVariable Long roleId,
            @Valid @RequestBody RolePermissionAssignmentRequestDTO request) {

        log.info(
                "Assigning {} permissions to role ID: {}",
                request.getPermissionIds().size(),
                roleId);
        List<RolePermissionDTO> createdRolePermissions =
                rolePermissionService.assignPermissionsToRole(roleId, request.getPermissionIds());
        return new ResponseEntity<>(
                ApiResponse.success(
                        "Permissions assigned to role successfully", createdRolePermissions),
                HttpStatus.CREATED);
    }

    // GET - Get Role Permissions (redirects to unified endpoint)
    @GetMapping("/{roleId}/permissions")
    @Operation(
            summary = "Get role permissions",
            description = "Retrieves all permissions assigned to a role")
    public ResponseEntity<ApiResponse<List<RolePermissionDTO>>> getRolePermissions(
            @Parameter(description = "Role ID", required = true) @PathVariable Long roleId) {
        log.info("Redirecting to unified role-permissions endpoint with roleId: {}", roleId);
        return getRolePermissionsByParams(roleId, null);
    }

    // DELETE - Remove Permission from Role
    @DeleteMapping("/{roleId}/permissions/{permissionId}")
    @Operation(
            summary = "Remove permission from role",
            description = "Removes a specific permission from a role")
    public ResponseEntity<ApiResponse<Void>> removePermissionFromRole(
            @Parameter(description = "Role ID", required = true) @PathVariable Long roleId,
            @Parameter(description = "Permission ID", required = true) @PathVariable
                    Long permissionId) {
        log.info("Removing permission ID: {} from role ID: {}", permissionId, roleId);
        rolePermissionService.removePermissionFromRole(roleId, permissionId);
        return ResponseEntity.ok(ApiResponse.success("Permission removed from role successfully"));
    }

    // DELETE - Remove All Permissions from Role
    @DeleteMapping("/{roleId}/permissions")
    @Operation(
            summary = "Remove all permissions from role",
            description = "Removes all permissions from a role")
    public ResponseEntity<ApiResponse<Void>> removeAllPermissionsFromRole(
            @Parameter(description = "Role ID", required = true) @PathVariable Long roleId) {
        log.info("Removing all permissions from role ID: {}", roleId);
        rolePermissionService.removeAllPermissionsFromRole(roleId);
        return ResponseEntity.ok(
                ApiResponse.success("All permissions removed from role successfully"));
    }

    // GET - Get Permission Roles (redirects to unified endpoint)
    @GetMapping("/permissions/{permissionId}/roles")
    @Operation(
            summary = "Get permission roles",
            description = "Retrieves all roles assigned to a permission")
    public ResponseEntity<ApiResponse<List<RolePermissionDTO>>> getPermissionRoles(
            @Parameter(description = "Permission ID", required = true) @PathVariable
                    Long permissionId) {
        log.info(
                "Redirecting to unified role-permissions endpoint with permissionId: {}",
                permissionId);
        return getRolePermissionsByParams(null, permissionId);
    }

    // GET - Get Role Permissions with Request Parameters
    @GetMapping("/role-permissions")
    @Operation(
            summary = "Get role permissions",
            description = "Retrieves role permissions based on provided parameters")
    public ResponseEntity<ApiResponse<List<RolePermissionDTO>>> getRolePermissionsByParams(
            @Parameter(description = "Role ID") @RequestParam(required = false) Long roleId,
            @Parameter(description = "Permission ID") @RequestParam(required = false)
                    Long permissionId) {
        log.info(
                "Retrieving role permissions with roleId: {}, permissionId: {}",
                roleId,
                permissionId);

        List<RolePermissionDTO> rolePermissions =
                rolePermissionService.getRolePermissionsByParams(roleId, permissionId);

        return ResponseEntity.ok(
                ApiResponse.success("Role permissions retrieved successfully", rolePermissions));
    }

    // =============== User Role Management Endpoints ===============

    // GET - Get Role Users (redirects to unified endpoint)
    @GetMapping("/{roleId}/users")
    @Operation(summary = "Get role users", description = "Retrieves all users assigned to a role")
    public ResponseEntity<ApiResponse<?>> getRoleUsers(
            @Parameter(description = "Role ID", required = true) @PathVariable Long roleId) {
        log.info("Redirecting to unified user-roles endpoint with roleId: {}", roleId);
        return getUserRoles(null, null, roleId, 0, 10, "createdAt", "desc");
    }

    // POST - Assign Role to User
    @PostMapping("/{roleId}/users")
    @Operation(
            summary = "Assign role to users",
            description = "Assigns the role to one or more users")
    public ResponseEntity<ApiResponse<List<UserRoleDTO>>> assignRoleToUsers(
            @Parameter(description = "Role ID", required = true) @PathVariable Long roleId,
            @Valid @RequestBody RoleUserAssignmentRequestDTO request) {

        log.info("Assigning role ID: {} to {} users", roleId, request.getUserIds().size());

        List<UserRoleDTO> assignedRoles =
                request.getUserIds().stream()
                        .map(
                                userId -> {
                                    UserRoleRequestDTO requestDTO = new UserRoleRequestDTO();
                                    requestDTO.setRoleId(roleId);
                                    requestDTO.setUserId(userId);

                                    return userRoleService.assignRoleToUser(requestDTO);
                                })
                        .toList();

        return new ResponseEntity<>(
                ApiResponse.success("Role assigned to users successfully", assignedRoles),
                HttpStatus.CREATED);
    }

    // DELETE - Remove Role from User
    @DeleteMapping("/{roleId}/users/{userId}")
    @Operation(
            summary = "Remove role from user",
            description = "Removes the role from a specific user")
    public ResponseEntity<ApiResponse<Void>> removeRoleFromUser(
            @Parameter(description = "Role ID", required = true) @PathVariable Long roleId,
            @Parameter(description = "User ID", required = true) @PathVariable Long userId) {
        log.info("Removing role ID: {} from user ID: {}", roleId, userId);
        userRoleService.removeRoleFromUser(userId, roleId);
        return ResponseEntity.ok(ApiResponse.success("Role removed from user successfully"));
    }

    // GET - Unified User Roles Endpoint
    @GetMapping("/user-roles")
    @Operation(
            summary = "Get user roles",
            description = "Unified endpoint to retrieve user roles based on provided parameters")
    public ResponseEntity<ApiResponse<?>> getUserRoles(
            @Parameter(description = "User Role ID") @RequestParam(required = false) Long id,
            @Parameter(description = "User ID") @RequestParam(required = false) Long userId,
            @Parameter(description = "Role ID") @RequestParam(required = false) Long roleId,
            @Parameter(description = "Page number") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort field") @RequestParam(defaultValue = "createdAt")
                    String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc")
                    String sortDir) {

        // Case 1: Get specific user role by ID
        if (id != null) {
            log.info("Retrieving user role with ID: {}", id);
            UserRoleDTO userRole = userRoleService.getUserRoleById(id);
            return ResponseEntity.ok(
                    ApiResponse.success("User role retrieved successfully", userRole));
        }

        // Case 2: Get roles for a specific user
        if (userId != null && roleId == null) {
            log.info("Retrieving roles for user ID: {}", userId);
            UserRoleDTO userRole = userRoleService.getUserRoles(userId);
            return ResponseEntity.ok(
                    ApiResponse.success("User roles retrieved successfully", userRole));
        }

        // Case 3: Get users for a specific role
        if (roleId != null && userId == null) {
            log.info("Retrieving users for role ID: {}", roleId);
            List<UserRoleDTO> roleUsers = userRoleService.getRoleUsers(roleId);
            return ResponseEntity.ok(
                    ApiResponse.success("Role users retrieved successfully", roleUsers));
        }

        // Case 4: Get specific user-role mapping
        if (userId != null && roleId != null) {
            log.info(
                    "Retrieving user-role mapping for user ID: {} and role ID: {}", userId, roleId);
            Optional<UserRoleDTO> userRole =
                    userRoleService.getUserRoleByUserIdAndRoleId(userId, roleId);
            if (userRole.isPresent()) {
                return ResponseEntity.ok(
                        ApiResponse.success("User role retrieved successfully", userRole.get()));
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                        .body(
                                ApiResponse.error(
                                        "User role not found for user ID: "
                                                + userId
                                                + " and role ID: "
                                                + roleId));
            }
        }

        // Case 5: Get all user roles with pagination (default)
        log.info("Retrieving all user roles with pagination - page: {}, size: {}", page, size);

        Sort sort =
                sortDir.equalsIgnoreCase("desc")
                        ? Sort.by(sortBy).descending()
                        : Sort.by(sortBy).ascending();

        Pageable pageable = PageRequest.of(page, size, sort);
        Page<UserRoleDTO> userRoles = userRoleService.getAllUserRoles(pageable);

        return ResponseEntity.ok(
                ApiResponse.success("User roles retrieved successfully", userRoles));
    }

    // DELETE - Remove User Role by ID
    @DeleteMapping("/user-roles/{id}")
    @Operation(
            summary = "Remove user role",
            description = "Removes a user role assignment by its ID")
    public ResponseEntity<ApiResponse<Void>> removeUserRole(
            @Parameter(description = "User Role ID", required = true) @PathVariable Long id) {
        log.info("Removing user role with ID: {}", id);
        userRoleService.removeUserRole(id);
        return ResponseEntity.ok(ApiResponse.success("User role removed successfully"));
    }
}
