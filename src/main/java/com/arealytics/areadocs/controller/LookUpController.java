package com.arealytics.areadocs.controller;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.arealytics.areadocs.dto.lookUpDTO.StateDTO;
import com.arealytics.areadocs.dto.lookUpDTO.ZipCodeDTO;
import com.arealytics.areadocs.dto.responseDTO.ApiResponse;
import com.arealytics.areadocs.exception.ResourceNotFoundException;
import com.arealytics.areadocs.service.LookUpService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/lookup")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "LookUp Controller", description = "APIs for retrieving state and zip code lookup data")
public class LookUpController {

    private final LookUpService lookUpService;

    /**
     * Retrieves a list of all states.
     *
     * @return ResponseEntity containing an ApiResponse with a list of StateDTOs.
     */
    @GetMapping("/states")
    @Operation(summary = "Get all states", description = "Retrieves a list of all available states")
    public ResponseEntity<ApiResponse<List<StateDTO>>> getStates() {
        log.info("Controller: Request to fetch all states.");
        List<StateDTO> states = lookUpService.getAllStates();
        return ResponseEntity.ok(ApiResponse.success("States retrieved successfully", states));
    }

    /**
     * Retrieves a list of zip codes. Can be filtered by a specific state ID.
     *
     * @param stateId Optional parameter to filter zip codes by state ID.
     * @return ResponseEntity containing an ApiResponse with a list of ZipCodeDTOs.
     */
    @GetMapping("/zipCodes")
    @Operation(
            summary = "Get zip codes",
            description = "Retrieves zip codes, optionally filtered by state ID.")
    public ResponseEntity<ApiResponse<List<ZipCodeDTO>>> getZipCodes(
            @Parameter(description = "ID of the state to filter zip codes by")
                    @RequestParam(required = false)
                    Long stateId) {

        log.info("Controller: Request to fetch zip codes for state ID: {}", stateId);
        try {
            List<ZipCodeDTO> zipCodes = lookUpService.getZipCodesByStateIdOrAll(stateId);
            return ResponseEntity.ok(
                    ApiResponse.success("Zip codes retrieved successfully", zipCodes));
        } catch (ResourceNotFoundException e) {
            log.warn("Controller: Error fetching zip codes: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error(
                    "Controller: An unexpected error occurred while fetching zip codes: {}",
                    e.getMessage(),
                    e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("An unexpected error occurred: " + e.getMessage()));
        }
    }
}
