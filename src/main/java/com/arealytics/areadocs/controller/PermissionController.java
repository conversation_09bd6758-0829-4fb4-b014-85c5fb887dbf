package com.arealytics.areadocs.controller;

import java.util.Collections;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.arealytics.areadocs.dto.requestDTO.PermissionFilterDTO;
import com.arealytics.areadocs.dto.requestDTO.PermissionRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.ApiResponse;
import com.arealytics.areadocs.dto.responseDTO.PermissionDTO;
import com.arealytics.areadocs.service.PermissionService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/permissions")
@RequiredArgsConstructor
@Tag(name = "Permission Controller", description = "APIs for managing permissions")
public class PermissionController {

    private final PermissionService permissionService;

    // POST - Create Permission
    @PostMapping
    @Operation(
            summary = "Create a new permission",
            description = "Creates a new permission with the provided details")
    public ResponseEntity<ApiResponse<PermissionDTO>> createPermission(
            @Valid @RequestBody PermissionRequestDTO permissionRequestDTO) {
        PermissionDTO createdPermission = permissionService.createPermission(permissionRequestDTO);
        return new ResponseEntity<>(
                ApiResponse.success("Permission created successfully", createdPermission),
                HttpStatus.CREATED);
    }

    // GET - Get Permissions with filters
    @GetMapping
    @Operation(
            summary = "Get permissions",
            description = "Retrieves permissions based on provided filters with pagination")
    public ResponseEntity<ApiResponse<Page<PermissionDTO>>> getPermissions(
            @Parameter(description = "Permission ID") @RequestParam(required = false)
                    Long permissionId,
            @Parameter(description = "Permission Code") @RequestParam(required = false)
                    String permissionCode,
            @Parameter(description = "Permission Name") @RequestParam(required = false)
                    String permissionName,
            @Parameter(description = "Page number") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort field and direction (e.g., createdAt,desc)")
                    @RequestParam(defaultValue = "createdAt,desc")
                    String sort) {

        // If permissionId is provided, return that specific permission
        if (permissionId != null) {
            Optional<PermissionDTO> permissionOpt =
                    permissionService.findPermissionById(permissionId);
            if (permissionOpt.isPresent()) {
                Page<PermissionDTO> permissionPage =
                        new PageImpl<>(Collections.singletonList(permissionOpt.get()));
                return ResponseEntity.ok(ApiResponse.success(permissionPage));
            } else {
                Page<PermissionDTO> emptyPage = Page.empty();
                return ResponseEntity.ok(ApiResponse.success(emptyPage));
            }
        }

        // If permissionCode is provided, try to find by code
        if (permissionCode != null && !permissionCode.trim().isEmpty()) {
            try {
                PermissionDTO permission = permissionService.getPermissionByCode(permissionCode);
                Page<PermissionDTO> permissionPage =
                        new PageImpl<>(Collections.singletonList(permission));
                return ResponseEntity.ok(ApiResponse.success(permissionPage));
            } catch (Exception e) {
                // If not found, continue with filter search
            }
        }

        String[] sortParams = sort.split(",");
        Sort.Direction direction =
                sortParams.length > 1 && sortParams[1].equalsIgnoreCase("desc")
                        ? Sort.Direction.DESC
                        : Sort.Direction.ASC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortParams[0]));

        PermissionFilterDTO filter = new PermissionFilterDTO();
        filter.setPermissionCode(permissionCode);
        filter.setPermissionName(permissionName);

        Page<PermissionDTO> permissions =
                permissionService.getPermissionsWithFilters(filter, pageable);
        return ResponseEntity.ok(ApiResponse.success(permissions));
    }

    // GET - Get Permission by ID
    @GetMapping("/{id}")
    @Operation(summary = "Get permission by ID", description = "Retrieves a permission by its ID")
    public ResponseEntity<ApiResponse<PermissionDTO>> getPermissionById(
            @Parameter(description = "Permission ID", required = true) @PathVariable Long id) {
        PermissionDTO permission = permissionService.getPermissionById(id);
        return ResponseEntity.ok(ApiResponse.success(permission));
    }

    // PUT - Update Permission
    @PutMapping("/{id}")
    @Operation(
            summary = "Update a permission",
            description = "Updates a permission with the provided details")
    public ResponseEntity<ApiResponse<PermissionDTO>> updatePermission(
            @Parameter(description = "Permission ID", required = true) @PathVariable Long id,
            @Valid @RequestBody PermissionRequestDTO permissionRequestDTO) {
        PermissionDTO updatedPermission =
                permissionService.updatePermission(id, permissionRequestDTO);
        return ResponseEntity.ok(
                ApiResponse.success("Permission updated successfully", updatedPermission));
    }

    // DELETE - Delete Permission
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete a permission", description = "Deletes a permission by its ID")
    public ResponseEntity<Void> deletePermission(
            @Parameter(description = "Permission ID", required = true) @PathVariable Long id) {
        permissionService.deletePermission(id);
        return ResponseEntity.noContent().build();
    }
}
