package com.arealytics.areadocs.controller;

import java.util.List;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.arealytics.areadocs.domain.Company;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.dto.requestDTO.CartRequestDTO;
import com.arealytics.areadocs.dto.requestDTO.OrderRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.ApiResponse;
import com.arealytics.areadocs.dto.responseDTO.CartResponseDTO;
import com.arealytics.areadocs.dto.responseDTO.OrderItemDetailsResponse;
import com.arealytics.areadocs.dto.responseDTO.OrderResponseDTO;
import com.arealytics.areadocs.exception.ResourceNotFoundException;
import com.arealytics.areadocs.service.CartService;
import com.arealytics.areadocs.service.OrderService;
import com.arealytics.areadocs.util.AuthUtils;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequiredArgsConstructor
@RequestMapping("/orders")
@Slf4j
public class OrderController {

    private final OrderService orderService;
    private final AuthUtils authUtils;
    private final CartService cartService;

    @PostMapping("/")
    public ResponseEntity<ApiResponse<OrderResponseDTO>> createOrder(
            @RequestBody @Valid OrderRequestDTO request) {

        // Get the current user
        User currentUser = authUtils.getCurrentUser();
        Company currentCompany = authUtils.getCurrentUserCompany();

        try {
            // Process the order
            OrderResponseDTO response =
                    orderService.processOrder(request, currentUser, currentCompany);

            // Return a successful response
            return ResponseEntity.ok(ApiResponse.success("Order created successfully", response));
        } catch (Exception e) {
            log.error("Error processing order: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to process order: " + e.getMessage()));
        }
    }

    @GetMapping("/")
    public ResponseEntity<ApiResponse<?>> getCombinedOrderData(
            @RequestParam(required = false) Long orderId,
            @RequestParam(required = false) Long orderItemId) {

        User currentUser = authUtils.getCurrentUser();
        if (currentUser == null) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("No authenticated user found"));
        }

        try {
            if (orderId == null) {
                // Fetch all orders for the user
                List<OrderResponseDTO> orders = orderService.getOrdersByUser(currentUser);
                return ResponseEntity.ok(
                        ApiResponse.success("Orders retrieved successfully", orders));
            }

            if (orderItemId == null) {
                // Fetch details of a specific order
                OrderResponseDTO orderResponse = orderService.getOrderById(orderId);
                return ResponseEntity.ok(
                        ApiResponse.success("Order retrieved successfully", orderResponse));
            }

            // Fetch details of a specific order item
            OrderItemDetailsResponse itemResponse =
                    orderService.getOrderItemDetails(orderId, orderItemId);
            return ResponseEntity.ok(
                    ApiResponse.success("Order item retrieved successfully", itemResponse));

        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("Error retrieving order data: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to retrieve order data"));
        }
    }

    /**
     * Get current user's cart
     *
     * @return ResponseEntity with cart information
     */
    @GetMapping("/cart")
    public ResponseEntity<ApiResponse<CartResponseDTO>> getCart() {
        try {
            CartResponseDTO cart = cartService.getCart();
            return ResponseEntity.ok(ApiResponse.success("Cart retrieved successfully", cart));
        } catch (Exception e) {
            log.error("Error retrieving cart: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to retrieve cart: " + e.getMessage()));
        }
    }

    /**
     * Add items to cart
     *
     * @param request List of cart items to add
     * @return ResponseEntity with updated cart info
     */
    @PostMapping("/cart")
    public ResponseEntity<ApiResponse<CartResponseDTO>> addToCart(
            @RequestBody @Valid List<CartRequestDTO> request) {
        try {
            CartResponseDTO response = cartService.addToCart(request);
            return ResponseEntity.ok(
                    ApiResponse.success("Item added to cart successfully", response));
        } catch (Exception e) {
            log.error("Error adding item to cart: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to add item to cart: " + e.getMessage()));
        }
    }

    /**
     * Remove item from cart
     *
     * @param itemId ID of the order item to remove
     * @return ResponseEntity with updated cart info
     */
    @DeleteMapping("/cart/items/{itemId}")
    public ResponseEntity<ApiResponse<CartResponseDTO>> removeFromCart(@PathVariable Long itemId) {
        try {
            CartResponseDTO response = cartService.removeFromCart(itemId);
            return ResponseEntity.ok(
                    ApiResponse.success("Item removed from cart successfully", response));
        } catch (ResourceNotFoundException e) {
            log.warn("Item not found in cart: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error(e.getMessage()));
        } catch (Exception e) {
            log.error("Error removing item from cart: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to remove item from cart: " + e.getMessage()));
        }
    }

    /**
     * Clear all items from user's cart
     *
     * @return ResponseEntity with empty cart response
     */
    @DeleteMapping("/cart")
    public ResponseEntity<ApiResponse<CartResponseDTO>> clearCart() {
        try {
            CartResponseDTO response = cartService.clearCart();
            return ResponseEntity.ok(ApiResponse.success("Cart cleared successfully", response));
        } catch (Exception e) {
            log.error("Error clearing cart: {}", e.getMessage(), e);
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("Failed to clear cart: " + e.getMessage()));
        }
    }

    // get order by transaction id
    @GetMapping("/{transactionId}")
    public ResponseEntity<ApiResponse<OrderResponseDTO>> getOrderByTransactionId(
            @PathVariable Long transactionId) {

        OrderResponseDTO order = orderService.getOrderByTransactionId(transactionId);
        return ResponseEntity.ok(ApiResponse.success("Order retrieved successfully", order));
    }
}
