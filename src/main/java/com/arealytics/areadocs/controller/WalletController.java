package com.arealytics.areadocs.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.arealytics.areadocs.domain.Company;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.dto.requestDTO.WalletRechargeRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.AccountDTO;
import com.arealytics.areadocs.dto.responseDTO.WalletRechargeResponseDTO;
import com.arealytics.areadocs.enumeration.UserType;
import com.arealytics.areadocs.repository.CompanyRepository;
import com.arealytics.areadocs.repository.UserRepository;
import com.arealytics.areadocs.service.AccountService;
import com.arealytics.areadocs.service.WalletService;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/** Controller for wallet operations. */
@RestController
@RequestMapping("/account")
@RequiredArgsConstructor
@Slf4j
public class WalletController {

    private final WalletService walletService;
    private final UserRepository userRepository;
    private final CompanyRepository companyRepository;
    private final AccountService accountService;

    /**
     * Recharge a wallet for either a user or a company based on user information.
     *
     * @param request the recharge request
     * @param userId the ID of the user performing the recharge
     * @return the recharge response
     */
    @PostMapping("/recharge")
    public ResponseEntity<WalletRechargeResponseDTO> rechargeWallet(
            @Valid @RequestBody WalletRechargeRequestDTO request, @RequestParam Long userId) {

        log.info(
                "Received wallet recharge request from user ID: {}, amount: {}",
                userId,
                request.getAmount());

        // Get the user performing the recharge
        User user =
                userRepository
                        .findById(userId)
                        .orElseThrow(
                                () ->
                                        new IllegalArgumentException(
                                                "User not found with ID: " + userId));

        // Recharge the wallet
        WalletRechargeResponseDTO response = walletService.rechargeWallet(request, user);

        return ResponseEntity.ok(response);
    }

    /**
     * Get a user's account.
     *
     * @param userId the ID of the user
     * @return the account DTO
     */
    @GetMapping("/user")
    public ResponseEntity<AccountDTO> getUserAccount(@RequestParam Long userId) {
        log.info("Received request to get account for user ID: {}", userId);

        // Get the user
        User user =
                userRepository
                        .findById(userId)
                        .orElseThrow(
                                () ->
                                        new IllegalArgumentException(
                                                "User not found with ID: " + userId));

        // Determine if the user is associated with a company and retrieve company if so
        if (user.getUserType().equals(UserType.COMPANY)) {
            if (user.getCompanyMemberships() != null
                    && user.getCompanyMemberships().getCompany() != null) {
                Company company = user.getCompanyMemberships().getCompany();
                return ResponseEntity.ok(accountService.getAccountDTOByCompany(company));
            } else {
                return ResponseEntity.notFound().build();
            }
        }
        // Get the user's account DTO
        return ResponseEntity.ok(accountService.getAccountDTOByUser(user));
    }

    /**
     * Get a company's account.
     *
     * @param companyId the ID of the company
     * @return the account DTO
     */
    @GetMapping("/company")
    public ResponseEntity<AccountDTO> getCompanyAccount(@RequestParam Long companyId) {
        log.info("Received request to get account for company ID: {}", companyId);

        // Get the company
        Company company =
                companyRepository
                        .findById(companyId)
                        .orElseThrow(
                                () ->
                                        new IllegalArgumentException(
                                                "Company not found with ID: " + companyId));

        // Get the company's account DTO
        return ResponseEntity.ok(accountService.getAccountDTOByCompany(company));
    }

    /**
     * Get the balance of the current user.
     *
     * @return the account DTO
     */
    @GetMapping("/balance")
    public ResponseEntity<AccountDTO> getBalance() { //
        log.info("Received request to get balance for current user");
        return ResponseEntity.ok(accountService.getBalance());
    }
}
