package com.arealytics.areadocs.controller;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.arealytics.areadocs.dto.requestDTO.serv.PropertyRequest;
import com.arealytics.areadocs.dto.responseDTO.ApiResponse;
import com.arealytics.areadocs.dto.responseDTO.serv.*;
import com.arealytics.areadocs.dto.responseDTO.serv.CustomProductResponse;
import com.arealytics.areadocs.mapper.CustomProductMapper;
import com.arealytics.areadocs.mapper.ParcelResponseMapper;
import com.arealytics.areadocs.service.ServApiService;
import com.arealytics.areadocs.util.ServQueryBuilder;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.extern.slf4j.Slf4j;

@RestController
@Slf4j
public class ServApiController {

    private final ServApiService servApiService;
    private final CustomProductMapper customProductMapper;
    private final ParcelResponseMapper parcelResponseMapper;
    @Autowired private Validator validator;

    @Autowired
    public ServApiController(
            ServApiService servApiService,
            CustomProductMapper customProductMapper,
            ParcelResponseMapper parcelResponseMapper) {
        this.servApiService = servApiService;
        this.customProductMapper = customProductMapper;
        this.parcelResponseMapper = parcelResponseMapper;
    }

    @GetMapping("/properties-by-address")
    public ResponseEntity<ApiResponse<PropertyResponse>> getProperties(
            @RequestParam(required = false) String propertyPfi,
            @RequestParam(required = false) String propertyNumber,
            @RequestParam(required = false) String municipalityName,
            @RequestParam(required = false) String eziAddress) {

        // Validate parameter combinations
        validateSearchParameters(propertyPfi, propertyNumber, municipalityName, eziAddress);

        if (propertyPfi == null
                && propertyNumber == null
                && municipalityName == null
                && eziAddress == null) {
            return ResponseEntity.badRequest()
                    .body(ApiResponse.error("At least one search parameter must be provided."));
        }

        PropertyRequest request = new PropertyRequest();
        request.setPropertyPfi(propertyPfi);
        request.setPropertyNumber(propertyNumber);
        request.setMunicipalityName(municipalityName);
        request.setEziAddress(eziAddress);

        Set<ConstraintViolation<PropertyRequest>> violations = validator.validate(request);

        if (!violations.isEmpty()) {
            String errorMessages =
                    violations.stream()
                            .map(ConstraintViolation::getMessage)
                            .collect(Collectors.joining(", "));
            return ResponseEntity.badRequest().body(ApiResponse.error(errorMessages));
        }

        String propertyParams =
                ServQueryBuilder.buildPropertySearchQuery(
                        propertyPfi, propertyNumber, municipalityName, eziAddress);

        // Call the service to get the property data
        ResponseEntity<PropertyResponse> propertyResponse =
                servApiService.getProperties(propertyParams, PropertyResponse.class);

        if (propertyResponse == null) {
            throw new IllegalArgumentException("Property response cannot be null");
        }

        PropertyResponse propertyData = propertyResponse.getBody();

        // Check if the data is empty
        if (propertyData == null || propertyData.getPropertySummaries().isEmpty()) {
            return ResponseEntity.badRequest().build();
        }

        return ResponseEntity.ok(
                ApiResponse.success("Properties retrieved successfully", propertyData));
    }

    @GetMapping("/properties-by-parcel")
    public ResponseEntity<ApiResponse<CustomParcelResponse>> getParcel(
            @RequestParam
                    @Size(
                            min = 1,
                            max = 24,
                            message = "parcelIdentifier must be between 1 and 24 characters")
                    @NotBlank(message = "parcelIdentifier must not be blank") String parcelIdentifier) {

        ResponseEntity<ParcelResponse> responseEntity =
                servApiService.getParcel(parcelIdentifier, ParcelResponse.class);
        ParcelResponse parcelResponse = responseEntity.getBody();

        if (parcelResponse == null
                || parcelResponse.getProperties() == null
                || parcelResponse.getProperties().isEmpty()) {
            return ResponseEntity.badRequest().build();
        }

        CustomParcelResponse parcelResponseDTO =
                parcelResponseMapper.toCustomParcelResponse(parcelResponse);

        List<PropertyResponse.PropertyDTO> allPropertySummaries = new ArrayList<>();

        for (ParcelResponse.ParcelProperty parcelProperty : parcelResponse.getProperties()) {
            Long propertyPfi = parcelProperty.getPropertyPfi();

            if (propertyPfi != null) {
                String queryParam = "pfi=" + propertyPfi;

                ResponseEntity<PropertyResponse> propertyResponseEntity =
                        servApiService.getProperties(queryParam, PropertyResponse.class);

                PropertyResponse propertyResponse = propertyResponseEntity.getBody();

                if (propertyResponse != null
                        && propertyResponse.getPropertySummaries() != null
                        && !propertyResponse.getPropertySummaries().isEmpty()) {
                    allPropertySummaries.addAll(propertyResponse.getPropertySummaries());
                }
            }
        }

        parcelResponseDTO.setProperties(allPropertySummaries);

        return ResponseEntity.ok(
                ApiResponse.success("Parcel retrieved successfully", parcelResponseDTO));
    }

    @GetMapping("/properties-by-title")
    public ResponseEntity<ApiResponse<TitleResponse>> getTitle(@RequestParam String titleId) {

        ResponseEntity<TitleResponse> titleResponse =
                servApiService.getTitleById(titleId, TitleResponse.class);
        TitleResponse titleData = titleResponse.getBody();
        // Check if the data is empty
        if (titleData == null || titleData.getTitleSummaries().isEmpty()) {
            return ResponseEntity.badRequest().build();
        }

        return ResponseEntity.ok(ApiResponse.success("Title retrieved successfully", titleData));
    }

    /*
    @GetMapping("/townships")
    public ResponseEntity<ApiResponse<TownshipResponse>> getTownships() {
        TownshipResponse response =
                servApiService.getTownships(null, TownshipResponse.class).getBody();
        return ResponseEntity.ok(ApiResponse.success("Townships retrieved successfully", response));
    }

    @GetMapping("/parishes")
    public ResponseEntity<ApiResponse<ParishesResponse>> getParishes() {
        ParishesResponse response =
                servApiService.getParishes(null, ParishesResponse.class).getBody();
        return ResponseEntity.ok(ApiResponse.success("Parishes retrieved successfully", response));
    }
    */
    @GetMapping("/properties-by-proprietors")
    public ResponseEntity<ApiResponse<ProprietorsResponse>> getProprietors(
            @RequestParam(required = false) String lastName,
            @RequestParam(required = false) String givenName,
            @RequestParam(required = false) String companyName,
            @RequestParam(defaultValue = "1") Integer pageNumber) {

        String proprietorsParams =
                ServQueryBuilder.buildProprietorsSearchQuery(
                        lastName, givenName, companyName, pageNumber);

        // Call the service to get the proprietors data
        ProprietorsResponse proprietorsResponse =
                servApiService
                        .getProprietors(proprietorsParams, ProprietorsResponse.class)
                        .getBody();

        if (proprietorsResponse == null
                || proprietorsResponse.getProprietorSummaries() == null
                || proprietorsResponse.getProprietorSummaries().isEmpty()) {
            return ResponseEntity.badRequest().build();
        }

        return ResponseEntity.ok(
                ApiResponse.success("Proprietors retrieved successfully", proprietorsResponse));
    }

    /*
    @PostMapping("/titles/subscriptions")
    public ResponseEntity<ApiResponse<SubscriptionResponse>> subscriptionAlerts(
            @RequestBody @Valid SubscriptionRequest request) {
        SubscriptionResponse response = servApiService.subscriptionAlerts(request).getBody();
        return ResponseEntity.ok(
                ApiResponse.success("Subscription created successfully", response));
    }

    @PostMapping("/ownership-check")
    public ResponseEntity<ApiResponse<VerifyOwnershipResponse>> verifyOwnership(
            @RequestBody @Valid VerifyOwnershipRequest request) {
        VerifyOwnershipResponse response = servApiService.verifyOwnership(request).getBody();
        return ResponseEntity.ok(ApiResponse.success("Ownership verified successfully", response));
    }

    @GetMapping("/extra-data/owners-corporation-managers")
    public ResponseEntity<ApiResponse<OwnersCorporationManagersResponse>>
            getOwnersCorporationManagers() {
        OwnersCorporationManagersResponse response =
                servApiService
                        .getOwnersCorporationManagers(OwnersCorporationManagersResponse.class)
                        .getBody();
        return ResponseEntity.ok(
                ApiResponse.success(
                        "Owners Corporation Managers retrieved successfully", response));
    }

    @GetMapping("/extra-data/municipalities")
    public ResponseEntity<ApiResponse<MunicipalitiesResponse>> getMunicipalities() {
        MunicipalitiesResponse response =
                servApiService.getMunicipalities(MunicipalitiesResponse.class).getBody();
        return ResponseEntity.ok(
                ApiResponse.success("Municipalities retrieved successfully`", response));
    }
    */
    @GetMapping("/products")
    public ResponseEntity<ApiResponse<CustomProductResponse>> getProducts(
            @RequestParam(required = false) String titleId,
            @RequestParam(required = false) String propertyPfi,
            @RequestParam(required = false) String documentId) {

        // Validate that at least one parameter is provided
        if ((titleId == null || titleId.isEmpty())
                && (propertyPfi == null || propertyPfi.isEmpty())
                && (documentId == null || documentId.isEmpty())) {
            throw new IllegalArgumentException(
                    "Either titleId or propertyPfi or documentId must be provided.");
        }

        // Use the utility method to build the query string
        String orderParams =
                ServQueryBuilder.buildProductsSearchQuery(titleId, propertyPfi, documentId);

        // Call the service to get the products data
        ProductsResponse response =
                servApiService.getProducts(orderParams, ProductsResponse.class).getBody();

        CustomProductResponse customResponse = customProductMapper.toCustomProduct(response);

        return ResponseEntity.ok(
                ApiResponse.success("Products retrieved successfully", customResponse));
    }

    /*
    @PostMapping("/titles/decrypt")
    public ResponseEntity<ApiResponse<DecryptTitleIdResponse>> decryptTitleIds(
            @RequestBody @Valid DecryptTitleIdRequest request) {
        DecryptTitleIdResponse response = servApiService.decryptTitleIds(request).getBody();
        return ResponseEntity.ok(ApiResponse.success("Title IDs decrypted successfully", response));
    }
    */
    // Validation method
    private void validateSearchParameters(
            String propertyPfi, String propertyNumber, String municipalityName, String eziAddress) {

        // Check for exclusive parameter rules first (before count)
        if (propertyPfi != null
                && (propertyNumber != null || municipalityName != null || eziAddress != null)) {
            throw new IllegalArgumentException("propertyPfi cannot be used with other parameters");
        }

        if (eziAddress != null
                && (propertyPfi != null || propertyNumber != null || municipalityName != null)) {
            throw new IllegalArgumentException("eziAddress cannot be used with other parameters");
        }

        if (municipalityName != null && propertyNumber == null) {
            throw new IllegalArgumentException(
                    "propertyNumber must be provided with municipalityName");
        }
        long providedParametersCount =
                Stream.of(propertyPfi, propertyNumber, municipalityName, eziAddress)
                        .filter(Objects::nonNull)
                        .count();

        if (providedParametersCount > 2) {
            throw new IllegalArgumentException("Too many search parameters provided");
        }
    }

    private List<CustomProductResponse.ProductDetails> fetchProducts(String key, String value) {
        if (value == null) return null;
        try {
            String productsParams = key + "=" + value;

            ResponseEntity<ProductsResponse> productResponse =
                    servApiService.getProducts(productsParams, ProductsResponse.class);

            ProductsResponse products =
                    (productResponse != null) ? productResponse.getBody() : null;

            if (products != null) {
                CustomProductResponse customProductResponse =
                        customProductMapper.toCustomProduct(products);
                return customProductResponse.getProducts();
            }
        } catch (Exception ex) {
            log.warn(
                    "Failed to retrieve products for {} [{}]: {}", key, value, ex.getMessage(), ex);
        }
        return null;
    }
}
