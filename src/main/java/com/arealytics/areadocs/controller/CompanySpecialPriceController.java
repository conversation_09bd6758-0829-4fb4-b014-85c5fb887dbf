package com.arealytics.areadocs.controller;

import java.util.Collections;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.arealytics.areadocs.dto.requestDTO.CompanySpecialPriceRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.ApiResponse;
import com.arealytics.areadocs.dto.responseDTO.CompanySpecialPriceResponseDTO;
import com.arealytics.areadocs.exception.CompanyException;
import com.arealytics.areadocs.service.CompanySpecialPriceService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.persistence.EntityNotFoundException;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/company-special-price")
@RequiredArgsConstructor
@Tag(
        name = "Company Special Price Controller",
        description = "APIs for managing company special prices")
public class CompanySpecialPriceController {

    private final CompanySpecialPriceService companySpecialPriceService;

    @GetMapping
    @Operation(
            summary = "Get company special prices",
            description =
                    "Retrieves a single company special price by ID if provided, or a paginated"
                        + " list of company special prices filtered by company ID, document type"
                        + " ID, or product code ID if provided, or all company special prices if no"
                        + " parameters are provided.")
    public ResponseEntity<ApiResponse<Page<CompanySpecialPriceResponseDTO>>>
            getCompanySpecialPrices(
                    @Parameter(description = "Company special price ID")
                            @RequestParam(required = false)
                            Long id,
                    @Parameter(description = "Company ID") @RequestParam(required = false)
                            Long companyId,
                    @Parameter(description = "Document price ID") @RequestParam(required = false)
                            Long documentPriceId,
                    @Parameter(description = "Page number") @RequestParam(defaultValue = "0")
                            int page,
                    @Parameter(description = "Page size") @RequestParam(defaultValue = "10")
                            int size,
                    @Parameter(description = "Sort field and direction (e.g., id,desc)")
                            @RequestParam(defaultValue = "id,desc")
                            String sort) {
        try {
            if (id != null) {
                CompanySpecialPriceResponseDTO companySpecialPrice =
                        companySpecialPriceService.getCompanySpecialPriceById(id);
                Page<CompanySpecialPriceResponseDTO> singlePage =
                        new PageImpl<>(
                                Collections.singletonList(companySpecialPrice),
                                PageRequest.of(0, 1),
                                1);
                return ResponseEntity.ok(ApiResponse.success(singlePage));
            }

            String[] sortParams = sort.split(",");
            Sort.Direction direction =
                    sortParams.length > 1 && sortParams[1].equalsIgnoreCase("desc")
                            ? Sort.Direction.DESC
                            : Sort.Direction.ASC;
            Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortParams[0]));

            Page<CompanySpecialPriceResponseDTO> companySpecialPrices =
                    companySpecialPriceService.getCompanySpecialPrices(
                            companyId, documentPriceId, pageable);
            return ResponseEntity.ok(ApiResponse.success(companySpecialPrices));
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>(ApiResponse.error(e.getMessage()), HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            return new ResponseEntity<>(
                    ApiResponse.error(
                            "An error occurred while retrieving company special prices: "
                                    + e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PutMapping("/{id}")
    @Operation(
            summary = "Update a company special price",
            description =
                    "Updates a company special price with the provided details. Required fields:"
                        + " companyId, productCodeId, specialPrice, effectiveDate, gst. Optional"
                        + " fields: documentTypeId, expiryDate.")
    public ResponseEntity<ApiResponse<CompanySpecialPriceResponseDTO>> updateCompanySpecialPrice(
            @Parameter(description = "Company special price ID", required = true) @PathVariable
                    Long id,
            @Valid @RequestBody CompanySpecialPriceRequestDTO requestDTO) {
        try {
            CompanySpecialPriceResponseDTO updatedCompanySpecialPrice =
                    companySpecialPriceService.updateCompanySpecialPrice(id, requestDTO);
            return ResponseEntity.ok(
                    ApiResponse.success(
                            "Company special price updated successfully",
                            updatedCompanySpecialPrice));
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>(ApiResponse.error(e.getMessage()), HttpStatus.NOT_FOUND);
        } catch (CompanyException e) {
            return new ResponseEntity<>(ApiResponse.error(e.getMessage()), HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            return new ResponseEntity<>(
                    ApiResponse.error(
                            "An error occurred while updating the company special price: "
                                    + e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @DeleteMapping("/{id}")
    @Operation(
            summary = "Delete a company special price",
            description = "Soft deletes a company special price by its ID")
    public ResponseEntity<ApiResponse<Void>> deleteCompanySpecialPrice(
            @Parameter(description = "Company special price ID", required = true) @PathVariable
                    Long id) {
        try {
            companySpecialPriceService.deleteCompanySpecialPrice(id);
            return ResponseEntity.ok(
                    ApiResponse.success("Company special price deleted successfully"));
        } catch (EntityNotFoundException e) {
            return new ResponseEntity<>(ApiResponse.error(e.getMessage()), HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            return new ResponseEntity<>(
                    ApiResponse.error(
                            "An error occurred while deleting the company special price: "
                                    + e.getMessage()),
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
