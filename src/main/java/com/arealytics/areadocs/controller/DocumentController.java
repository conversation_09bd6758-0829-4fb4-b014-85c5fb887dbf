package com.arealytics.areadocs.controller;

import java.math.BigDecimal;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import com.arealytics.areadocs.dto.requestDTO.DocumentFilterDTO;
import com.arealytics.areadocs.dto.requestDTO.DocumentUpdateDTO;
import com.arealytics.areadocs.dto.responseDTO.ApiResponse;
import com.arealytics.areadocs.dto.responseDTO.DocumentDTO;
import com.arealytics.areadocs.service.DocumentService;
import com.arealytics.areadocs.util.Utils;

import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("/documents")
public class DocumentController {

    private final DocumentService documentService;

    // -------------------------- DOCUMENT --------------------------

    @GetMapping("")
    @Operation(
            summary = "Get documents",
            description = "Retrieves documents based on provided filters with pagination")
    public ResponseEntity<ApiResponse<Page<DocumentDTO>>> getDocuments(
            @RequestParam(required = false) Long documentId,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String description,
            @RequestParam(required = false) Long externalId,
            @RequestParam(required = false) Long folderId,
            @RequestParam(required = false) Long sourceId,
            @RequestParam(required = false) Long categoryId,
            @RequestParam(required = false) String documentType,
            @RequestParam(required = false) BigDecimal minBasePrice,
            @RequestParam(required = false) BigDecimal maxBasePrice,
            @RequestParam(required = false) BigDecimal minFinalPrice,
            @RequestParam(required = false) BigDecimal maxFinalPrice,
            @RequestParam(required = false) Integer minAccessCount,
            @RequestParam(required = false) Integer maxAccessCount,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt,desc") String sort) {

        Pageable pageable = PageRequest.of(page, size, Utils.parseSort(sort));

        // Populate all filter criteria into the DTO
        DocumentFilterDTO filter = new DocumentFilterDTO();
        filter.setDocumentId(documentId);
        filter.setTitle(title);
        filter.setDescription(description);
        filter.setExternalId(externalId);
        filter.setFolderId(folderId);
        filter.setSourceId(sourceId);
        filter.setCategoryId(categoryId);
        filter.setDocumentType(documentType);
        filter.setMinBasePrice(minBasePrice);
        filter.setMaxBasePrice(maxBasePrice);
        filter.setMinFinalPrice(minFinalPrice);
        filter.setMaxFinalPrice(maxFinalPrice);
        filter.setMinAccessCount(minAccessCount);
        filter.setMaxAccessCount(maxAccessCount);

        Page<DocumentDTO> documents = documentService.getDocumentsWithFilters(filter, pageable);

        return ResponseEntity.ok(
                ApiResponse.success("Documents retrieved successfully", documents));
    }

    @PutMapping("/{id}")
    @Operation(
            summary = "Update document",
            description = "Updates a document's title, description, or folder.")
    public ResponseEntity<ApiResponse<DocumentDTO>> updateDocument(
            @PathVariable Long id,
            @RequestBody DocumentUpdateDTO requestDTO) { // Changed back to DocumentRequestDTO
        DocumentDTO updatedDocument = documentService.updateDocument(id, requestDTO);
        return ResponseEntity.ok(
                ApiResponse.success("Document updated successfully", updatedDocument));
    }

    @PutMapping("/{documentId}/move")
    @Operation(summary = "Move document", description = "Moves a document to a different folder.")
    public ResponseEntity<ApiResponse<DocumentDTO>> moveDocument(
            @PathVariable Long documentId, @RequestParam Long newFolderId) {
        DocumentDTO movedDocument = documentService.moveDocument(documentId, newFolderId);
        return ResponseEntity.ok(ApiResponse.success("Document moved successfully", movedDocument));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete document", description = "Deletes a document by ID")
    public ResponseEntity<String> deleteDocument(@PathVariable Long id) {
        documentService.deleteDocument(id);
        return ResponseEntity.ok("Document deleted successfully!");
    }

    // -------------------------- SOURCES --------------------------

    /*
    @PostMapping("/document-sources")
    @Operation(summary = "Create document source", description = "Creates a new document source")
    public ResponseEntity<DocumentSourceDTO> createDocumentSource(
            @RequestBody DocumentSourceRequestDTO requestDTO) {
        return ResponseEntity.ok(documentSourceService.createDocumentSource(requestDTO));
    }

    @GetMapping("/document-sources")
    @Operation(
            summary = "Get document sources",
            description = "Retrieves document sources based on provided filters with pagination")
    public ResponseEntity<ApiResponse<Page<DocumentSourceDTO>>> getDocumentSources(
            @RequestParam(required = false) Long id,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String baseUrl,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt,desc") String sort) {

        try {
            if (id != null) {
                DocumentSourceDTO dto = documentSourceService.getDocumentSourceById(id);
                Page<DocumentSourceDTO> singlePage = new PageImpl<>(Collections.singletonList(dto));
                return ResponseEntity.ok(ApiResponse.success(singlePage));
            }

            Pageable pageable = PageRequest.of(page, size, Utils.parseSort(sort));

            DocumentSourceFilterDTO filter = new DocumentSourceFilterDTO();
            filter.setName(name);
            filter.setBaseUrl(baseUrl);
            filter.setStatus(status);

            return ResponseEntity.ok(
                    ApiResponse.success(
                            documentSourceService.getDocumentSourcesWithFilters(filter, pageable)));

        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(
                            ApiResponse.error(
                                    "An error occurred while retrieving document sources: "
                                            + e.getMessage()));
        }
    }

    @PutMapping("/document-sources/{id}")
    @Operation(summary = "Update document source", description = "Updates a document source by ID")
    public ResponseEntity<DocumentSourceDTO> updateDocumentSource(
            @PathVariable Long id, @RequestBody DocumentSourceRequestDTO requestDTO) {
        return ResponseEntity.ok(documentSourceService.updateDocumentSource(id, requestDTO));
    }

    @DeleteMapping("/document-sources/{id}")
    @Operation(summary = "Delete document source", description = "Deletes a document source by ID")
    public ResponseEntity<Void> deleteDocumentSource(@PathVariable Long id) {
        documentSourceService.deleteDocumentSource(id);
        return ResponseEntity.noContent().build();
    }

    // -------------------------- CATEGORIES --------------------------

    @PostMapping("/document-categories")
    @Operation(
            summary = "Create document category",
            description = "Creates a new document category")
    public ResponseEntity<DocumentCategoryDTO> createDocumentCategory(
            @RequestBody DocumentCategoryDTO dto) {
        return ResponseEntity.ok(documentCategoryService.createDocumentCategory(dto));
    }

    @GetMapping("/document-categories")
    @Operation(
            summary = "Get document categories",
            description = "Retrieve document categories with filters and pagination")
    public ResponseEntity<ApiResponse<Page<DocumentCategoryDTO>>> getDocumentCategories(
            @RequestParam(required = false) Long id,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String description,
            @RequestParam(required = false) Long parentCategoryId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt,desc") String sort) {

        try {
            if (id != null) {
                DocumentCategoryDTO category = documentCategoryService.getDocumentCategoryById(id);
                Page<DocumentCategoryDTO> singlePage =
                        new PageImpl<>(Collections.singletonList(category));
                return ResponseEntity.ok(ApiResponse.success(singlePage));
            }

            Pageable pageable = PageRequest.of(page, size, Utils.parseSort(sort));

            DocumentCategoryFilterDTO filter = new DocumentCategoryFilterDTO();
            filter.setName(name);
            filter.setDescription(description);
            filter.setParentCategoryId(parentCategoryId);

            return ResponseEntity.ok(
                    ApiResponse.success(
                            documentCategoryService.getDocumentCategoriesWithFilters(
                                    filter, pageable)));

        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(
                            ApiResponse.error(
                                    "Error retrieving document categories: " + e.getMessage()));
        }
    }

    @PutMapping("/document-categories/{id}")
    @Operation(
            summary = "Update document category",
            description = "Updates a document category by ID")
    public ResponseEntity<DocumentCategoryDTO> updateDocumentCategory(
            @PathVariable Long id, @RequestBody DocumentCategoryDTO dto) {
        return ResponseEntity.ok(documentCategoryService.updateDocumentCategory(id, dto));
    }

    @DeleteMapping("/document-categories/{id}")
    @Operation(
            summary = "Delete document category",
            description = "Deletes a document category by ID")
    public ResponseEntity<Void> deleteDocumentCategory(@PathVariable Long id) {
        documentCategoryService.deleteDocumentCategory(id);
        return ResponseEntity.noContent().build();
    }
    */
}
