package com.arealytics.areadocs.repository;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import com.arealytics.areadocs.domain.Account;
import com.arealytics.areadocs.domain.Company;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.enumeration.AccountType;

/** Repository for Account entity. */
@Repository
public interface AccountRepository
        extends JpaRepository<Account, Long>, JpaSpecificationExecutor<Account> {

    /**
     * Check if an account with the given account type exists.
     *
     * @param accountType the account type to check
     * @return true if an account with the given account type exists, false otherwise
     */
    boolean existsByAccountType(AccountType accountType);

    /**
     * Find an account by its account type.
     *
     * @param accountType the account type to search for
     * @return an Optional containing the account if found, empty otherwise
     */
    Optional<Account> findByAccountType(AccountType accountType);

    /**
     * Find an account by its user.
     *
     * @param user the user to search for
     * @return an Optional containing the account if found, empty otherwise
     */
    Optional<Account> findByUser(User user);

    /**
     * Find an account by its company.
     *
     * @param company the company to search for
     * @return an Optional containing the account if found, empty otherwise
     */
    Optional<Account> findByCompany(Company company);

    /**
     * Check if an account with the given user exists.
     *
     * @param user the user to check
     * @return true if an account with the given user exists, false otherwise
     */
    boolean existsByUser(User user);

    /**
     * Check if an account with the given company exists.
     *
     * @param company the company to check
     * @return true if an account with the given company exists, false otherwise
     */
    boolean existsByCompany(Company company);
}
