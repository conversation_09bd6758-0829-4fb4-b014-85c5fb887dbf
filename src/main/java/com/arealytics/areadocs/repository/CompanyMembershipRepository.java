package com.arealytics.areadocs.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import com.arealytics.areadocs.domain.Company;
import com.arealytics.areadocs.domain.CompanyMembership;
import com.arealytics.areadocs.domain.User;

@Repository
public interface CompanyMembershipRepository
        extends JpaRepository<CompanyMembership, Long>,
                JpaSpecificationExecutor<CompanyMembership> {
    CompanyMembership findByUser(User user);

    CompanyMembership findByUserId(Long userId);

    CompanyMembership findByCompany(Company company);

    List<CompanyMembership> findByCompanyId(Long companyId);

    Optional<CompanyMembership> findByUserIdAndCompanyId(Long userId, Long companyId);

    boolean existsByCompanyIdAndRoleNameAndIsActiveTrue(Long companyId, String companyAdmin);

    List<CompanyMembership> findByUserIdIn(List<Long> userIds);
}
