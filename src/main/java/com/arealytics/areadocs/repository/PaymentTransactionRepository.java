package com.arealytics.areadocs.repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import com.arealytics.areadocs.domain.Company;
import com.arealytics.areadocs.domain.PaymentTransaction;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.enumeration.PaymentStatus;

/** Repository for PaymentTransaction entity. */
@Repository
public interface PaymentTransactionRepository
        extends JpaRepository<PaymentTransaction, Long>,
                JpaSpecificationExecutor<PaymentTransaction> {

    /**
     * Find payment transactions by their order ID.
     *
     * @param orderId the order ID to search for
     * @return an Optional containing the payment transaction if found, empty otherwise
     */
    Optional<PaymentTransaction> findByOrderId(String orderId);

    /**
     * Find payment transactions by their transaction ID.
     *
     * @param transactionId the transaction ID to search for
     * @return an Optional containing the payment transaction if found, empty otherwise
     */
    Optional<PaymentTransaction> findByTransactionId(String transactionId);

    /**
     * Find payment transactions by their status.
     *
     * @param status the status to search for
     * @return a list of payment transactions with the given status
     */
    List<PaymentTransaction> findByStatus(PaymentStatus status);

    /**
     * Find payment transactions by their user.
     *
     * @param user the user to search for
     * @return a list of payment transactions for the given user
     */
    List<PaymentTransaction> findByUser(User user);

    /**
     * Find payment transactions by their company.
     *
     * @param company the company to search for
     * @return a list of payment transactions for the given company
     */
    List<PaymentTransaction> findByCompany(Company company);

    /**
     * Find payment transactions by their transaction timestamp between the given dates.
     *
     * @param startDate the start date
     * @param endDate the end date
     * @return a list of payment transactions with transaction timestamps between the given dates
     */
    List<PaymentTransaction> findByTransactionTimestampBetween(
            LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Check if a payment transaction with the given order ID exists.
     *
     * @param orderId the order ID to check
     * @return true if a payment transaction with the given order ID exists, false otherwise
     */
    boolean existsByOrderId(String orderId);

    /**
     * Check if a payment transaction with the given transaction ID exists.
     *
     * @param transactionId the transaction ID to check
     * @return true if a payment transaction with the given transaction ID exists, false otherwise
     */
    boolean existsByTransactionId(String transactionId);
}
