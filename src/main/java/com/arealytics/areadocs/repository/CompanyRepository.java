package com.arealytics.areadocs.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import com.arealytics.areadocs.domain.Company;

@Repository
public interface CompanyRepository
        extends JpaRepository<Company, Long>, JpaSpecificationExecutor<Company> {
    boolean existsByName(String name);

    boolean existsByBillingEmail(String billingEmail);

    Optional<Company> findByName(String name);

    Optional<Company> findByABN(String abn);

    Optional<Company> findByACN(String acn);

    Optional<Company> findByBillingEmail(String billingEmail);

    Optional<Company> findByAccountsContactName(String accountsContactName);

    Optional<Company> findByAccountsContactNumber(String accountsContactNumber);

    List<Company> findByIndustry(String industry);

    List<Company> findByEmployeeCount<PERSON>reate<PERSON><PERSON><PERSON>(Integer employeeCount);

    List<Company> findByEmployeeCountLessThan(Integer employeeCount);
}
