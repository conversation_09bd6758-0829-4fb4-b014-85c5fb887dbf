package com.arealytics.areadocs.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import com.arealytics.areadocs.domain.Document;

@Repository
public interface DocumentRepository
        extends JpaRepository<Document, Long>, JpaSpecificationExecutor<Document> {
    /**
     * Check if a document with the given folder ID exists.
     *
     * @param folderId The ID of the folder
     * @return true if a document with the given folder ID exists, false otherwise
     */
    boolean existsByFolder_Id(Long folderId);

    /**
     * Check if a document with the given folder ID and title exists.
     *
     * @param folderId The ID of the folder
     * @param title The title of the document
     * @return true if a document with the given folder ID and title exists, false otherwise
     */
    boolean existsByFolderIdAndTitle(Long folderId, String title);

    /**
     * Check if a document with the given folder ID, title, and ID does not exist.
     *
     * @param folderId The ID of the folder
     * @param title The title of the document
     * @param documentId The ID of the document
     * @return true if a document with the given folder ID, title, and ID does not exist, false
     *     otherwise
     */
    boolean existsByFolderIdAndTitleAndIdNot(Long folderId, String title, Long documentId);

    /**
     * Get the total count of documents for a company.
     *
     * @param companyId The ID of the company
     * @return The total count of documents
     */
    long countByCompanyId(Long companyId);
}
