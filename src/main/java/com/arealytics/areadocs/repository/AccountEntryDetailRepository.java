package com.arealytics.areadocs.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import com.arealytics.areadocs.domain.Account;
import com.arealytics.areadocs.domain.AccountEntry;
import com.arealytics.areadocs.domain.AccountEntryDetail;
import com.arealytics.areadocs.enumeration.AccountTransactionType;

/** Repository for AccountEntryDetail entity. */
@Repository
public interface AccountEntryDetailRepository
        extends JpaRepository<AccountEntryDetail, Long>,
                JpaSpecificationExecutor<AccountEntryDetail> {

    /**
     * Find account entry details by their account entry.
     *
     * @param accountEntry the account entry to search for
     * @return a list of account entry details for the given account entry
     */
    List<AccountEntryDetail> findByAccountEntry(AccountEntry accountEntry);

    /**
     * Find account entry details by their account.
     *
     * @param account the account to search for
     * @return a list of account entry details for the given account
     */
    List<AccountEntryDetail> findByAccount(Account account);

    /**
     * Find account entry details by their account entry and account transaction type.
     *
     * @param accountEntry the account entry to search for
     * @param accountTransactionType the account transaction type to search for (DEBITED or
     *     CREDITED)
     * @return a list of account entry details for the given account entry and account transaction
     *     type
     */
    List<AccountEntryDetail> findByAccountEntryAndAccountTransactionType(
            AccountEntry accountEntry, AccountTransactionType accountTransactionType);

    /**
     * Find account entry details by their account and account transaction type.
     *
     * @param account the account to search for
     * @param accountTransactionType the account transaction type to search for (DEBITED or
     *     CREDITED)
     * @return a list of account entry details for the given account and account transaction type
     */
    List<AccountEntryDetail> findByAccountAndAccountTransactionType(
            Account account, AccountTransactionType accountTransactionType);
}
