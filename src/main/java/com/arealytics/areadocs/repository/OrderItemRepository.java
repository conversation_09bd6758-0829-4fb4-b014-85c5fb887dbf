package com.arealytics.areadocs.repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.arealytics.areadocs.domain.Order;
import com.arealytics.areadocs.domain.OrderItem;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.enumeration.OrderItemStatus;

/** Spring Data JPA repository for the OrderItem entity. */
@Repository
public interface OrderItemRepository
        extends JpaRepository<OrderItem, Long>, JpaSpecificationExecutor<OrderItem> {

    /**
     * Find all order items by order.
     *
     * @param order the order
     * @return the list of order items
     */
    List<OrderItem> findByOrder(Order order);

    /**
     * Find an order item by its ID and order.
     *
     * @param id the ID of the order item
     * @param order the order
     * @return the order item
     */
    Optional<OrderItem> findByIdAndOrder(Long id, Order order);

    /**
     * Find an order item by its product code and user.
     *
     * @param user the user
     * @param productCode the product code
     * @param status the status
     * @return the order item
     */
    Optional<OrderItem> findByUserAndProductCodeAndStatus(
            User user, String productCode, OrderItemStatus status);

    /**
     * Find all order items by user and status.
     *
     * @param currentUser the user
     * @param orderItemStatus the status
     * @return the list of order items
     */
    List<OrderItem> findByUserAndStatus(User currentUser, OrderItemStatus orderItemStatus);

    /**
     * Find an order item by its ID, user, and status.
     *
     * @param orderItemId the ID of the order item
     * @param currentUser the user
     * @param orderItemStatus the status
     * @return the order item
     */
    Optional<OrderItem> findByIdAndUserAndStatus(
            Long orderItemId, User currentUser, OrderItemStatus orderItemStatus);

    /**
     * Find an order item by its ID and order ID.
     *
     * @param orderItemId the ID of the order item
     * @param orderId the ID of the order
     * @return the order item
     */
    Optional<OrderItem> findByIdAndOrderId(Long orderItemId, Long orderId);

    /**
     * Get the sum of final prices for order items where the associated document belongs to a
     * specific company. This method uses JPQL to traverse relationships for aggregation.
     *
     * @param companyId The ID of the company
     * @return The sum of final prices for relevant order items.
     */
    @Query(
            "SELECT SUM(oi.finalPrice) FROM OrderItem oi JOIN oi.document d WHERE d.company.id ="
                    + " :companyId")
    BigDecimal sumFinalPriceByDocumentCompanyId(Long companyId);
}
