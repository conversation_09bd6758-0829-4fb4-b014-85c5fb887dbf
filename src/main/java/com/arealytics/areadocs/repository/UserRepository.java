package com.arealytics.areadocs.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.enumeration.UserType;

@Repository
public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {

    Optional<User> findByEmail(String email);

    Page<User> findByUserType(UserType userType, Pageable pageable);

    boolean existsByEmail(String email);

    List<User> findByFirstNameContainingOrLastNameContaining(String firstName, String lastName);

    /**
     * Find users by company ID and active company membership
     *
     * @param companyId The ID of the company
     * @param pageable Pagination information
     * @return Page of users
     */
    Page<User> findByCompanyMemberships_Company_IdAndCompanyMemberships_IsActiveTrue(
            Long companyId, Pageable pageable);

    List<User> findByCompanyMemberships_Company_IdAndCompanyMemberships_IsActiveTrue(
            Long companyId);

    /**
     * Count the number of active users in a company
     *
     * @param companyId The ID of the company
     * @return The number of active users in the company
     */
    long countByIsActiveTrueAndCompanyMemberships_IsActiveTrueAndCompanyMemberships_Company_Id(
            Long companyId);
}
