package com.arealytics.areadocs.repository;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.arealytics.areadocs.domain.DocumentType;

@Repository
public interface DocumentTypeRepository extends JpaRepository<DocumentType, Long> {
    boolean existsByName(String name);

    Optional<DocumentType> findByName(String name);

    Optional<DocumentType> findByTypeCode(String typeCode);
}
