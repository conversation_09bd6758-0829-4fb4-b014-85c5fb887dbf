package com.arealytics.areadocs.repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.arealytics.areadocs.domain.Account;
import com.arealytics.areadocs.domain.Order;
import com.arealytics.areadocs.domain.PaymentTransaction;
import com.arealytics.areadocs.domain.Transaction;
import com.arealytics.areadocs.enumeration.TransactionStatus;
import com.arealytics.areadocs.enumeration.TransactionType;

/** Repository for Transaction entity. */
@Repository
public interface TransactionRepository
        extends JpaRepository<Transaction, Long>, JpaSpecificationExecutor<Transaction> {

    /**
     * Find transactions by their transaction type.
     *
     * @param transactionType the transaction type to search for
     * @return a list of transactions with the given transaction type
     */
    List<Transaction> findByTransactionType(TransactionType transactionType);

    /**
     * Find transactions by their status.
     *
     * @param status the status to search for
     * @return a list of transactions with the given status
     */
    List<Transaction> findByStatus(TransactionStatus status);

    /**
     * Find transactions by their user.
     *
     * @param userId the user to search for
     * @return a list of transactions for the given user
     */
    @Query(
            "SELECT t FROM Transaction t LEFT JOIN FETCH t.paymentTransaction WHERE t.user.id ="
                    + " :userId")
    List<Transaction> findByUserId(@Param("userId") Long userId);

    List<Transaction> findByAccountCompanyId(Long companyId);


    /**
     * Find transactions by their account.
     *
     * @param account the account to search for
     * @return a list of transactions for the given account
     */
    List<Transaction> findByAccount(Account account);

    /**
     * Find transactions by their transaction date between the given dates.
     *
     * @param startDate the start date
     * @param endDate the end date
     * @return a list of transactions with transaction dates between the given dates
     */
    List<Transaction> findByTransactionDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Find transactions by their payment transaction.
     *
     * @param paymentTransaction the payment transaction to search for
     * @return a list of transactions associated with the given payment transaction
     */
    List<Transaction> findByPaymentTransaction(PaymentTransaction paymentTransaction);

    /**
     * Find the latest transaction associated with a given order.
     *
     * @param order the order to search for
     * @return an Optional containing the latest transaction if found
     */
    Optional<Transaction> findTopByOrderOrderByTransactionDateDesc(Order order);
}
