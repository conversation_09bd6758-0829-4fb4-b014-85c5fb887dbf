package com.arealytics.areadocs.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.arealytics.areadocs.domain.RolePermissionMapping;

@Repository
public interface RolePermissionMappingRepository
        extends JpaRepository<RolePermissionMapping, Long> {
    List<RolePermissionMapping> findByRoleId(Long roleId);

    List<RolePermissionMapping> findByPermissionId(Long permissionId);

    List<RolePermissionMapping> findByRoleIdIn(List<Long> roleIds);

    Optional<RolePermissionMapping> findByRoleIdAndPermissionId(Long roleId, Long permissionId);

    boolean existsByRoleIdAndPermissionId(Long roleId, Long permissionId);

    void deleteByRoleId(Long roleId);
}
