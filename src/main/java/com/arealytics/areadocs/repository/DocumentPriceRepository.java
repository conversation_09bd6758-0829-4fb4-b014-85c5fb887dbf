package com.arealytics.areadocs.repository;

import java.time.LocalDateTime;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import com.arealytics.areadocs.domain.DocumentPrice;

@Repository
public interface DocumentPriceRepository
        extends JpaRepository<DocumentPrice, Long>, JpaSpecificationExecutor<DocumentPrice> {

    boolean existsByProductCodeAndEffectiveDate(String productCode, LocalDateTime effectiveDate);

    boolean existsByProductCode(String productCode);

    Optional<DocumentPrice> findByProductCodeAndEffectiveDate(
            String productCode, LocalDateTime effectiveDate);

    Page<DocumentPrice> findByDocumentTypeId(Long documentTypeId, Pageable pageable);

    Page<DocumentPrice> findByProductCode(String productCode, Pageable pageable);

    Optional<DocumentPrice> findFirstByDocumentTypeId(Long documentTypeId);

    Optional<DocumentPrice> findFirstByProductCode(String productCode);
}
