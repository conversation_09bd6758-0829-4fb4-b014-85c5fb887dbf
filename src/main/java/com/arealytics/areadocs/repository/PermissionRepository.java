package com.arealytics.areadocs.repository;

import com.arealytics.areadocs.domain.Permission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface PermissionRepository extends JpaRepository<Permission, Long>, JpaSpecificationExecutor<Permission> {
    boolean existsByPermissionCode(String permissionCode);
    boolean existsByPermissionCodeAndIdNot(String permissionCode, Long id);
    Optional<Permission> findByPermissionCode(String permissionCode);
}
