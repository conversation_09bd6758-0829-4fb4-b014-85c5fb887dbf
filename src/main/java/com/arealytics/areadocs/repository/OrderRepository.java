package com.arealytics.areadocs.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import com.arealytics.areadocs.domain.Order;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.enumeration.OrderStatus;

/** Spring Data JPA repository for the Order entity. */
@Repository
public interface OrderRepository
        extends JpaRepository<Order, Long>, JpaSpecificationExecutor<Order> {

    /**
     * Find all orders by user.
     *
     * @param user the user
     * @return the list of orders
     */
    List<Order> findByUser(User user);

    /**
     * Find an order by its ID and user.
     *
     * @param id the ID of the order
     * @param user the user
     * @return the order
     */
    Optional<Order> findByIdAndUser(Long id, User user);

    /**
     * Find an order by its user and status.
     *
     * @param user the user
     * @param status the status
     * @return the order
     */
    Optional<Order> findByUserAndStatus(User user, OrderStatus status);
}
