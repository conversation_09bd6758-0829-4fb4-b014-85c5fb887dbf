package com.arealytics.areadocs.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import com.arealytics.areadocs.domain.Company;
import com.arealytics.areadocs.domain.Folder;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.enumeration.FolderType;

public interface FolderRepository
        extends JpaRepository<Folder, Long>, JpaSpecificationExecutor<Folder> {
    /*
     * Check if a folder with the given name and user ID exists.
     *
     * @param userId The ID of the user
     * @param name The name of the folder
     * @return true if a folder with the given name and user ID exists, false otherwise
     */
    boolean existsByUser_IdAndName(Long userId, String name);

    /*
     * Check if a folder with the given name and company ID exists.
     *
     * @param companyId The ID of the company
     * @param name The name of the folder
     * @return true if a folder with the given name and company ID exists, false otherwise
     */
    boolean existsByCompany_IdAndName(Long companyId, String name);

    /*
     * Find all folders by user ID.
     *
     * @param userId The ID of the user
     * @return A list of folders
     */
    List<Folder> findByUser_Id(Long userId);

    /*
     * Find all folders by company ID.
     *
     * @param companyId The ID of the company
     * @return A list of folders
     */
    List<Folder> findByCompany_Id(Long companyId);

    /*
     * Find all folders by user ID and order by name.
     *
     * @param userId The ID of the user
     * @return A list of folders
     */
    List<Folder> findByUser_IdOrderByNameAsc(Long userId);

    /*
     * Find all folders by company ID and order by name.
     *
     * @param companyId The ID of the company
     * @return A list of folders
     */
    List<Folder> findByCompany_IdOrderByNameAsc(Long companyId);

    /*
     * Find all folders by parent folder ID and user ID and order by name.
     *
     * @param parentFolderId The ID of the parent folder
     * @param userId The ID of the user
     * @return A list of folders
     */
    List<Folder> findByParentFolder_IdAndUser_IdOrderByNameAsc(Long parentFolderId, Long userId);

    /*
     * Check if a folder with the given name, user ID, and parent folder ID exists.
     *
     * @param userId The ID of the user
     * @param name The name of the folder
     * @param parentFolderId The ID of the parent folder
     * @return true if a folder with the given name, user ID, and parent folder ID exists, false
     *     otherwise
     */
    boolean existsByUser_IdAndNameAndParentFolder_Id(Long userId, String name, Long parentFolderId);

    /*
     * Check if a folder with the given parent folder ID exists.
     *
     * @param parentFolderId The ID of the parent folder
     * @return true if a folder with the given parent folder ID exists, false otherwise
     */
    boolean existsByParentFolder_Id(Long parentFolderId);

    /*
     * Find a folder by user ID and folder type.
     *
     * @param userId The ID of the user
     * @param folderType The folder type
     * @return An optional containing the folder if found, empty otherwise
     */
    Optional<Folder> findByUser_IdAndFolderType(Long userId, FolderType folderType);

    /*
     * Check if a folder with the given user and folder type exists.
     *
     * @param user The user
     * @param folderType The folder type
     * @return true if a folder with the given user and folder type exists, false otherwise
     */
    boolean existsByUserAndFolderType(User user, FolderType folderType);

    /*
     * Check if a folder with the given company and folder type exists.
     *
     * @param company The company
     * @param folderType The folder type
     * @return true if a folder with the given company and folder type exists, false otherwise
     */
    boolean existsByCompanyAndFolderType(Company company, FolderType folderType);

    /*
     * Find all folders by parent folder ID.
     *
     * @param parentFolderId The ID of the parent folder
     * @return A list of folders
     */
    List<Folder> findByParentFolder_Id(Long parentFolderId);

    /*
     * Find all folders by parent folder ID and user ID.
     *
     * @param parentFolderId The ID of the parent folder
     * @param userId The ID of the user
     * @return A list of folders
     */
    Optional<Folder> findByCompany_IdAndFolderType(Long companyId, FolderType folderType);
}
