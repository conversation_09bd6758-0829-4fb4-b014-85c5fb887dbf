package com.arealytics.areadocs.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import com.arealytics.areadocs.domain.CompanySpecialPrice;

@Repository
public interface CompanySpecialPriceRepository
        extends JpaRepository<CompanySpecialPrice, Long>,
                JpaSpecificationExecutor<CompanySpecialPrice> {

    Page<CompanySpecialPrice> findByCompanyId(Long companyId, Pageable pageable);

    Page<CompanySpecialPrice> findByCompanyIdAndDocumentPriceId(
            Long companyId, Long documentPriceId, Pageable pageable);

    Page<CompanySpecialPrice> findByDocumentPriceId(Long documentPriceId, Pageable pageable);

    List<CompanySpecialPrice> findAllByDocumentPriceId(Long documentPriceId);

    Optional<CompanySpecialPrice> findFirstByCompanyIdAndDocumentPriceId(
            Long companyId, Long documentPriceId);

    boolean existsByCompanyIdAndDocumentPriceId(Long companyId, Long documentPriceId);
}
