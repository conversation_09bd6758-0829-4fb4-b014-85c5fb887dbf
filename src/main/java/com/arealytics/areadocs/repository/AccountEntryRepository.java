package com.arealytics.areadocs.repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import com.arealytics.areadocs.domain.AccountEntry;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.enumeration.EntryStatus;
import com.arealytics.areadocs.enumeration.EntryType;

/** Repository for AccountEntry entity. */
@Repository
public interface AccountEntryRepository
        extends JpaRepository<AccountEntry, Long>, JpaSpecificationExecutor<AccountEntry> {

    /**
     * Find an account entry by its reference number.
     *
     * @param referenceNumber the reference number to search for
     * @return an Optional containing the account entry if found, empty otherwise
     */
    Optional<AccountEntry> findByReferenceNumber(String referenceNumber);

    /**
     * Find account entries by their entry type.
     *
     * @param entryType the entry type to search for
     * @return a list of account entries with the given entry type
     */
    List<AccountEntry> findByEntryType(EntryType entryType);

    /**
     * Find account entries by their status.
     *
     * @param status the status to search for
     * @return a list of account entries with the given status
     */
    List<AccountEntry> findByStatus(EntryStatus status);

    /**
     * Find account entries by their created by user.
     *
     * @param createdBy the user who created the entries
     * @return a list of account entries created by the given user
     */
    List<AccountEntry> findByCreatedBy(User createdBy);

    /**
     * Find account entries by their entry date.
     *
     * @param entryDate the entry date to search for
     * @return a list of account entries with the given entry date
     */
    List<AccountEntry> findByEntryDate(LocalDate entryDate);

    /**
     * Find account entries by their entry date between the given dates.
     *
     * @param startDate the start date
     * @param endDate the end date
     * @return a list of account entries with entry dates between the given dates
     */
    List<AccountEntry> findByEntryDateBetween(LocalDate startDate, LocalDate endDate);

    /**
     * Check if an account entry with the given reference number exists.
     *
     * @param referenceNumber the reference number to check
     * @return true if an account entry with the given reference number exists, false otherwise
     */
    boolean existsByReferenceNumber(String referenceNumber);
}
