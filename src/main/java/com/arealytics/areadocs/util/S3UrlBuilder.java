package com.arealytics.areadocs.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class S3UrlBuilder {
    @Value("${aws.s3.bucket.name}")
    private String bucketName;

    @Value("${aws.region}")
    private String region;

    public String buildS3Url(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return null;
        }
        // It's good practice to check if the injected values are present
        if (bucketName == null || region == null) {
            // Log a warning or handle this case as appropriate
            return null;
        }
        return String.format("https://%s.s3.%s.amazonaws.com/%s", bucketName, region, filePath);
    }
}
