package com.arealytics.areadocs.util;


public class EmailValidatorUtil {

    public static void validateEmailDomain(String email, String primaryDomain) {
        if (email != null && primaryDomain != null) {
            String primaryExpected = "@" + primaryDomain;
            if (!email.toLowerCase().endsWith(primaryExpected.toLowerCase())) {
                throw new IllegalArgumentException("Email must contain the primary domain " + primaryExpected);
            }
        }
    }
}