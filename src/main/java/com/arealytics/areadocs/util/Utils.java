package com.arealytics.areadocs.util;

import org.springframework.data.domain.Sort;

public class Utils {

    public static Sort parseSort(String sort) {
        String[] sortParams = sort.split(",");
        String property = sortParams[0];
        Sort.Direction direction =
                sortParams.length > 1 && sortParams[1].equalsIgnoreCase("desc")
                        ? Sort.Direction.DESC
                        : Sort.Direction.ASC;
        return Sort.by(direction, property);
    }
}
