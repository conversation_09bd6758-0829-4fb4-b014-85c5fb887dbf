package com.arealytics.areadocs.util;

/**
 * Utility class to store and retrieve the current user ID in a ThreadLocal variable. This is used
 * for audit logging when Spring Security is not available.
 */
public class UserContext {

    private static final ThreadLocal<Long> currentUserId = new ThreadLocal<>();

    /**
     * Set the current user ID for the current thread.
     *
     * @param userId The ID of the current user
     */
    public static void setCurrentUserId(Long userId) {
        currentUserId.set(userId);
    }

    /**
     * Get the current user ID for the current thread.
     *
     * @return The ID of the current user, or null if not set
     */
    public static Long getCurrentUserId() {
        return currentUserId.get();
    }

    /**
     * Clear the current user ID for the current thread. This should be called at the end of request
     * processing to prevent memory leaks.
     */
    public static void clear() {
        currentUserId.remove();
    }
}
