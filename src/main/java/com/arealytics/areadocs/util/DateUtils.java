package com.arealytics.areadocs.util;

import java.time.LocalDateTime;

public class DateUtils {

    /**
     * Checks if the current date is within the effective and expiry period.
     *
     * @param now the current date and time
     * @param effectiveDate the start of the validity (nullable)
     * @param expiryDate the end of the validity (nullable)
     * @return true if the current date is within the period
     */
    public static boolean isWithinEffectivePeriod(
            LocalDateTime now, LocalDateTime effectiveDate, LocalDateTime expiryDate) {
        return (effectiveDate == null || !now.isBefore(effectiveDate))
                && (expiryDate == null || !now.isAfter(expiryDate));
    }
}
