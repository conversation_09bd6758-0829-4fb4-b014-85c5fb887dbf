package com.arealytics.areadocs.util;

import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

@Component
public class ServQueryBuilder {

    private static void addQueryParam(UriComponentsBuilder builder, String key, String value) {
        if (value != null && !value.isEmpty()) {
            builder.queryParam(key, value);
        }
    }

    public static String buildPropertySearchQuery(
            String propertyPfi, String propertyNumber, String municipalityName, String eziAddress) {
        UriComponentsBuilder builder = UriComponentsBuilder.newInstance();

        if (propertyPfi != null && !propertyPfi.isEmpty()) {
            addQueryParam(builder, "propertyPfi", propertyPfi);
        } else if (propertyNumber != null && municipalityName != null) {
            addQueryParam(builder, "propertyNumber", propertyNumber);
            addQueryParam(builder, "municipalityName", municipalityName);
        } else if (eziAddress != null && !eziAddress.isEmpty()) {
            addQueryParam(builder, "eziAddress", eziAddress);
        } else {
            throw new IllegalArgumentException(
                    "At least one valid parameter combination is required");
        }

        return builder.build().getQuery();
    }

    public static String buildProprietorsSearchQuery(
            String lastName, String givenName, String companyName, Integer pageNumber) {
        UriComponentsBuilder builder = UriComponentsBuilder.newInstance();

        addQueryParam(builder, "lastName", lastName);
        addQueryParam(builder, "givenName", givenName);
        addQueryParam(builder, "companyName", companyName);

        builder.queryParam("pageNumber", pageNumber);

        return builder.build().getQuery();
    }

    public static String buildProductsSearchQuery(
            String titleId, String propertyPfi, String documentId) {
        UriComponentsBuilder builder = UriComponentsBuilder.newInstance();

        addQueryParam(builder, "titleId", titleId);
        addQueryParam(builder, "propertyPfi", propertyPfi);
        addQueryParam(builder, "documentId", documentId);
        return builder.build().getQuery();
    }

    public static String buildProductQueryFromTitleId(String titleId) {
        UriComponentsBuilder builder = UriComponentsBuilder.newInstance();

        addQueryParam(builder, "titleId", titleId);

        return builder.build().getQuery();
    }
}
