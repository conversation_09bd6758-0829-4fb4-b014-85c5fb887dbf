package com.arealytics.areadocs.util;

public class StringMatchingUtil {

    /**
     * Normalizes a string by trimming whitespace and converting to lowercase.
     *
     * @param input the string to normalize
     * @return the normalized string
     */
    public static String normalizeString(String input) {
        if (input == null || input.isEmpty() || input.trim().isEmpty()) {
            return null;
        }
        return input.trim().toLowerCase();
    }

    /**
     * Compares two strings, ignoring case and leading/trailing whitespace.
     *
     * @param str1 - input from frontend (can be null, empty, or with whitespace)
     * @param str2 - value from a database (always lowercase)
     * @return true if they match after normalization
     */
    public static boolean equalsIgnoreCaseAndTrim(String str1, String str2) {

        // Normalize the strings before comparison. Direct comparison (db value is already
        // lowercase)
        String normalizedString1 = normalizeString(str1);
        String normalizedString2 = normalizeString(str2);

        if (normalizedString1 == null && normalizedString2 == null) {
            return true;
        }

        if (normalizedString1 == null || normalizedString2 == null) {
            return false;
        }
        return normalizedString1.equals(normalizedString2);
    }
}
