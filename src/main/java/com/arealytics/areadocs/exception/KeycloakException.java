package com.arealytics.areadocs.exception;

public class KeycloakException extends RuntimeException {
    public KeycloakException(String message) {
        super(message);
    }

    public KeycloakException(String message, Throwable cause) {
        super(message, cause);
    }

    public static class UserNotFoundException extends KeycloakException {
        public UserNotFoundException(String message) {
            super(message);
        }
    }

    public static class UserStatusUpdateException extends KeycloakException {
        public UserStatusUpdateException(String message, Throwable cause) {
            super(message, cause);
        }
    }

    public static class KeycloakUserCreationException extends KeycloakException {
        public KeycloakUserCreationException(String message, Throwable cause) {
            super(message, cause);
        }
    }
}
