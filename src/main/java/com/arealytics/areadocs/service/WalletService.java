package com.arealytics.areadocs.service;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.areadocs.domain.Account;
import com.arealytics.areadocs.domain.Company;
import com.arealytics.areadocs.domain.CompanyMembership;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.dto.requestDTO.WalletRechargeRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.WalletRechargeResponseDTO;
import com.arealytics.areadocs.repository.CompanyMembershipRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/** Service for wallet operations. */
@Service
@RequiredArgsConstructor
@Slf4j
public class WalletService {

    private final AccountService accountService;
    private final CompanyMembershipRepository companyMembershipRepository;
    private final TransactionService transactionService;

    /**
     * Recharge a wallet based on user information.
     *
     * @param request the recharge request
     * @param user the user performing the recharge
     * @return the recharge response
     */
    @Transactional
    public WalletRechargeResponseDTO rechargeWallet(WalletRechargeRequestDTO request, User user) {

        // Get the account to recharge
        Account accountToRecharge;

        // Check if user is of type COMPANY
        if (user.getUserType() != null
                && user.getUserType()
                        .equals(com.arealytics.areadocs.enumeration.UserType.COMPANY)) {

            CompanyMembership membership = companyMembershipRepository.findByUser(user);

            if (membership == null) {
                // If no company membership found, use user's personal account
                log.info(
                        "No company membership found for user ID: {}, recharging personal account,"
                                + " amount: {}",
                        user.getId(),
                        request.getAmount());

                accountToRecharge = accountService.getAccountByUser(user);
            } else {
                Company company = membership.getCompany();

                log.info(
                        "Recharging wallet for company ID: {}, amount: {}",
                        company.getId(),
                        request.getAmount());

                accountToRecharge = accountService.getAccountByCompany(company);
            }
        } else {
            // For individual users, recharge their personal account
            log.info(
                    "Recharging wallet for user ID: {}, amount: {}",
                    user.getId(),
                    request.getAmount());

            accountToRecharge = accountService.getAccountByUser(user);
        }

        // Process the wallet recharge using the transaction utility
        TransactionService.TransactionResult result =
                transactionService.rechargeAccount(
                        accountToRecharge, request.getAmount(), request.getCurrency(), user);

        if (!result.isSuccess()) {
            throw new IllegalStateException("Failed to recharge wallet: " + result.getMessage());
        }

        // Build and return the response
        return WalletRechargeResponseDTO.builder()
                .accountEntryId(result.getAccountEntry().getId())
                .referenceNumber(result.getAccountEntry().getReferenceNumber())
                .entryDate(result.getAccountEntry().getEntryDate())
                .status(result.getAccountEntry().getStatus())
                .amount(result.getAmount())
                .currency(request.getCurrency())
                .newBalance(result.getDestinationAccount().getBalance())
                .description(result.getAccountEntry().getDescription())
                // Transaction information
                .transactionId(result.getTransaction().getId())
                .transactionType(result.getTransaction().getTransactionType())
                .transactionDate(result.getTransaction().getTransactionDate())
                .transactionStatus(result.getTransaction().getStatus())
                // Payment transaction information
                .paymentTransactionId(result.getPaymentTransaction().getId())
                .orderId(result.getPaymentTransaction().getOrderId())
                .paymentTxnId(result.getPaymentTransaction().getTransactionId())
                .paymentStatus(result.getPaymentTransaction().getStatus())
                .paymentTimestamp(result.getPaymentTransaction().getTransactionTimestamp())
                .paymentMethod(result.getPaymentTransaction().getPaymentMethod())
                .paymentGateway(result.getPaymentTransaction().getPaymentGateway())
                .build();
    }
}
