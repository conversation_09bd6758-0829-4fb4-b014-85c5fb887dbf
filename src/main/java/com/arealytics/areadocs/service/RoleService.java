package com.arealytics.areadocs.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.arealytics.areadocs.domain.Permission;
import com.arealytics.areadocs.domain.Role;
import com.arealytics.areadocs.domain.RolePermissionMapping;
import com.arealytics.areadocs.dto.requestDTO.RoleFilterDTO;
import com.arealytics.areadocs.dto.requestDTO.RoleRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.RoleDTO;
import com.arealytics.areadocs.exception.PermissionException;
import com.arealytics.areadocs.exception.RoleException;
import com.arealytics.areadocs.mapper.RoleMapper;
import com.arealytics.areadocs.repository.PermissionRepository;
import com.arealytics.areadocs.repository.RolePermissionMappingRepository;
import com.arealytics.areadocs.repository.RoleRepository;

import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class RoleService {

    private final RoleRepository roleRepository;
    private final RoleMapper roleMapper;
    private final PermissionRepository permissionRepository;
    private final RolePermissionMappingRepository rolePermissionMappingRepository;

    /**
     * Create a new role
     *
     * @param roleRequestDTO The role to create
     * @return The created role DTO
     */
    @Transactional
    public RoleDTO createRole(RoleRequestDTO roleRequestDTO) {
        log.info("Creating new role with name: {}", roleRequestDTO.getName());

        if (roleRepository.existsByName(roleRequestDTO.getName())) {
            throw new RoleException.DuplicateRoleNameException(
                    "Role name already exists: " + roleRequestDTO.getName());
        }

        Role role = roleMapper.toEntity(roleRequestDTO);
        Role savedRole = roleRepository.save(role);

        // Assign permissions if provided
        if (!CollectionUtils.isEmpty(roleRequestDTO.getPermissionIds())) {
            log.info(
                    "Assigning {} permissions to new role",
                    roleRequestDTO.getPermissionIds().size());
            assignPermissionsToRole(savedRole, roleRequestDTO.getPermissionIds());
        }

        return roleMapper.toDto(savedRole);
    }

    /**
     * Get all roles
     *
     * @return List of all role DTOs
     */
    @Transactional(readOnly = true)
    public List<RoleDTO> getAllRoles() {
        return roleRepository.findAll().stream()
                .map(roleMapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Get a role by ID
     *
     * @param id The role ID
     * @return The role DTO
     */
    @Transactional(readOnly = true)
    public RoleDTO getRoleById(Long id) {
        Role role =
                roleRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new RoleException.RoleNotFoundException(
                                                "Role not found with id: " + id));
        return roleMapper.toDto(role);
    }

    /**
     * Get a role by name
     *
     * @param name The role name
     * @return The role DTO
     */
    @Transactional(readOnly = true)
    public RoleDTO getRoleByName(String name) {
        Role role =
                roleRepository
                        .findByName(name)
                        .orElseThrow(
                                () ->
                                        new RoleException.RoleNotFoundException(
                                                "Role not found with name: " + name));
        return roleMapper.toDto(role);
    }

    /**
     * Update a role
     *
     * @param id The role ID
     * @param roleRequestDTO The updated role data
     * @return The updated role DTO
     */
    @Transactional
    public RoleDTO updateRole(Long id, RoleRequestDTO roleRequestDTO) {
        log.info("Updating role with ID: {}", id);

        Role existingRole =
                roleRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new RoleException.RoleNotFoundException(
                                                "Role not found with id: " + id));

        // Check if role name is being changed and if it already exists
        if (!existingRole.getName().equals(roleRequestDTO.getName())
                && roleRepository.existsByName(roleRequestDTO.getName())) {
            throw new RoleException.DuplicateRoleNameException(
                    "Role name already exists: " + roleRequestDTO.getName());
        }

        // Update the entity with DTO values
        roleMapper.updateEntityFromDto(roleRequestDTO, existingRole);

        Role savedRole = roleRepository.save(existingRole);

        // Update permissions if provided
        if (roleRequestDTO.getPermissionIds() != null) {
            log.info("Updating permissions for role ID: {}", id);
            // Remove existing permissions
            rolePermissionMappingRepository.deleteByRoleId(id);

            // Assign new permissions
            if (!roleRequestDTO.getPermissionIds().isEmpty()) {
                assignPermissionsToRole(savedRole, roleRequestDTO.getPermissionIds());
            }
        }

        return roleMapper.toDto(savedRole);
    }

    /**
     * Delete a role
     *
     * @param id The role ID
     */
    @Transactional
    public void deleteRole(Long id) {
        log.info("Deleting role with ID: {}", id);

        if (!roleRepository.existsById(id)) {
            throw new RoleException.RoleNotFoundException("Role not found with id: " + id);
        }

        // Role permissions will be deleted automatically due to CascadeType.ALL and
        // orphanRemoval=true
        roleRepository.deleteById(id);
        log.info("Role with ID: {} deleted successfully", id);
    }

    /**
     * Get roles with filters and pagination
     *
     * @param filter The filter criteria
     * @param pageable The pagination information
     * @return Page of role DTOs
     */
    @Transactional(readOnly = true)
    public Page<RoleDTO> getRolesWithFilters(RoleFilterDTO filter, Pageable pageable) {
        Specification<Role> spec =
                (root, query, criteriaBuilder) -> {
                    List<Predicate> predicates = new ArrayList<>();

                    if (filter != null) {
                        if (StringUtils.hasText(filter.getName())) {
                            predicates.add(
                                    criteriaBuilder.like(
                                            criteriaBuilder.lower(root.get("name")),
                                            "%" + filter.getName().toLowerCase() + "%"));
                        }
                    }

                    return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
                };

        return roleRepository.findAll(spec, pageable).map(roleMapper::toDto);
    }

    /**
     * Get role by ID as Optional
     *
     * @param id The role ID
     * @return Optional of role DTO
     */
    @Transactional(readOnly = true)
    public Optional<RoleDTO> findRoleById(Long id) {
        return roleRepository.findById(id).map(roleMapper::toDto);
    }

    /**
     * Helper method to assign permissions to a role
     *
     * @param role The role entity
     * @param permissionIds List of permission IDs to assign
     */
    private void assignPermissionsToRole(Role role, List<Long> permissionIds) {
        permissionIds.forEach(
                permissionId -> {
                    try {
                        Permission permission =
                                permissionRepository
                                        .findById(permissionId)
                                        .orElseThrow(
                                                () ->
                                                        new PermissionException
                                                                .PermissionNotFoundException(
                                                                "Permission not found with id: "
                                                                        + permissionId));

                        // Check if this permission is already assigned to the role
                        if (!rolePermissionMappingRepository.existsByRoleIdAndPermissionId(
                                role.getId(), permissionId)) {
                            RolePermissionMapping mapping = new RolePermissionMapping();
                            mapping.setRole(role);
                            mapping.setPermission(permission);
                            mapping.setStatus("ACTIVE");

                            rolePermissionMappingRepository.save(mapping);
                            log.info(
                                    "Permission {} assigned to role {}",
                                    permission.getPermissionCode(),
                                    role.getName());
                        }
                    } catch (Exception e) {
                        log.error(
                                "Error assigning permission ID {} to role {}: {}",
                                permissionId,
                                role.getName(),
                                e.getMessage());
                    }
                });
    }
}
