package com.arealytics.areadocs.service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.arealytics.areadocs.domain.DocumentSource;
import com.arealytics.areadocs.dto.requestDTO.DocumentSourceFilterDTO;
import com.arealytics.areadocs.dto.requestDTO.DocumentSourceRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.DocumentSourceDTO;
import com.arealytics.areadocs.exception.DocumentSourceNotFoundException;
import com.arealytics.areadocs.mapper.DocumentSourceMapper;
import com.arealytics.areadocs.repository.DocumentSourceRepository;

import jakarta.persistence.criteria.Predicate;

@Service
public class DocumentSourceService {

    private final DocumentSourceRepository documentSourceRepository;
    private final DocumentSourceMapper documentSourceMapper;

    @Autowired
    public DocumentSourceService(
            DocumentSourceRepository documentSourceRepository,
            DocumentSourceMapper documentSourceMapper) {
        this.documentSourceRepository = documentSourceRepository;
        this.documentSourceMapper = documentSourceMapper;
    }

    public DocumentSourceDTO createDocumentSource(DocumentSourceRequestDTO requestDTO) {
        DocumentSource entity = documentSourceMapper.toEntity(requestDTO);

        DocumentSource saved = documentSourceRepository.save(entity);
        return documentSourceMapper.toDto(saved);
    }

    public DocumentSourceDTO createDocumentSource(DocumentSourceDTO dto) {

        DocumentSourceRequestDTO requestDTO = new DocumentSourceRequestDTO();
        requestDTO.setName(dto.getName());
        requestDTO.setBaseUrl(dto.getBaseUrl());
        requestDTO.setStatus(dto.getStatus());
        return createDocumentSource(requestDTO);
    }

    @Transactional(readOnly = true)
    public List<DocumentSourceDTO> getAllDocumentSources() {
        return documentSourceRepository.findAll().stream()
                .map(documentSourceMapper::toDto)
                .collect(Collectors.toList());
    }

    public DocumentSourceDTO getDocumentSourceById(Long id) {
        DocumentSource source =
                documentSourceRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new DocumentSourceNotFoundException(
                                                "DocumentSource with ID " + id + " not found"));
        return documentSourceMapper.toDto(source);
    }

    public DocumentSourceDTO updateDocumentSource(Long id, DocumentSourceRequestDTO requestDTO) {
        DocumentSource existing =
                documentSourceRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new DocumentSourceNotFoundException(
                                                "DocumentSource with ID " + id + " not found"));

        existing.setName(requestDTO.getName());
        existing.setBaseUrl(requestDTO.getBaseUrl());

        if (requestDTO.getStatus() != null) {
            existing.setStatus(
                    Enum.valueOf(
                            com.arealytics.areadocs.enumeration.DocumentSourceStatus.class,
                            requestDTO.getStatus()));
        }

        DocumentSource updated = documentSourceRepository.save(existing);
        return documentSourceMapper.toDto(updated);
    }

    public DocumentSourceDTO updateDocumentSource(Long id, DocumentSourceDTO dto) {

        DocumentSourceRequestDTO requestDTO = new DocumentSourceRequestDTO();
        requestDTO.setName(dto.getName());
        requestDTO.setBaseUrl(dto.getBaseUrl());
        requestDTO.setStatus(dto.getStatus());
        return updateDocumentSource(id, requestDTO);
    }

    public void deleteDocumentSource(Long id) {
        DocumentSource existing =
                documentSourceRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new DocumentSourceNotFoundException(
                                                "DocumentSource with ID " + id + " not found"));

        existing.setDeleteAt(Instant.now());
        documentSourceRepository.save(existing);
    }

    /**
     * Get companies with filters and pagination
     *
     * @param filter The filter criteria
     * @param pageable The pagination information
     * @return Page of company DTOs
     */
    @Transactional(readOnly = true)
    public Page<DocumentSourceDTO> getDocumentSourcesWithFilters(
            DocumentSourceFilterDTO filter, Pageable pageable) {
        Specification<DocumentSource> spec =
                (root, query, cb) -> {
                    List<Predicate> predicates = new ArrayList<>();

                    if (filter != null) {
                        if (StringUtils.hasText(filter.getName())) {
                            predicates.add(
                                    cb.like(
                                            cb.lower(root.get("name")),
                                            "%" + filter.getName().toLowerCase() + "%"));
                        }

                        if (StringUtils.hasText(filter.getBaseUrl())) {
                            predicates.add(
                                    cb.like(
                                            cb.lower(root.get("baseUrl")),
                                            "%" + filter.getBaseUrl().toLowerCase() + "%"));
                        }

                        if (StringUtils.hasText(filter.getStatus())) {
                            predicates.add(
                                    cb.equal(
                                            cb.lower(root.get("status")),
                                            filter.getStatus().toLowerCase()));
                        }
                    }

                    return cb.and(predicates.toArray(new Predicate[0]));
                };

        return documentSourceRepository.findAll(spec, pageable).map(documentSourceMapper::toDto);
    }
}
