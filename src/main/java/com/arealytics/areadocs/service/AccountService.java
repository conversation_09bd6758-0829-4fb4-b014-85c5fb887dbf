package com.arealytics.areadocs.service;

import java.math.BigDecimal;
import java.util.Optional;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.areadocs.domain.Account;
import com.arealytics.areadocs.domain.Company;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.dto.responseDTO.AccountDTO;
import com.arealytics.areadocs.enumeration.AccountType;
import com.arealytics.areadocs.enumeration.UserType;
import com.arealytics.areadocs.mapper.AccountMapper;
import com.arealytics.areadocs.repository.AccountRepository;
import com.arealytics.areadocs.util.AuthUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/** Service for managing accounts. */
@Service
@RequiredArgsConstructor
@Slf4j
public class AccountService {

    private final AccountRepository accountRepository;
    private final AccountMapper accountMapper;
    private final AuthUtils authUtils;

    /**
     * Create an account for a user.
     *
     * @param user the user to create an account for
     * @return the created account
     */
    @Transactional
    public Account createUserAccount(User user) {
        // Check if user already has an account
        Optional<Account> existingAccount = accountRepository.findByUser(user);
        if (existingAccount.isPresent()) {
            log.info("User {} already has an account, skipping creation", user.getId());
            return existingAccount.get();
        }

        // Check if user is of type COMPANY
        if (user.getUserType() == com.arealytics.areadocs.enumeration.UserType.COMPANY) {
            log.info("User {} is of type COMPANY, skipping account creation", user.getId());
            return null;
        }

        log.info("Creating account for user {}", user.getId());
        Account account =
                Account.builder()
                        .accountType(AccountType.INDIVIDUAL)
                        .user(user)
                        .balance(BigDecimal.ZERO)
                        .currency("AUD")
                        .build();

        return accountRepository.save(account);
    }

    /**
     * Create an account for a company.
     *
     * @param company the company to create an account for
     * @return the created account
     */
    @Transactional
    public Account createCompanyAccount(Company company) {
        // Check if company already has an account
        Optional<Account> existingAccount = accountRepository.findByCompany(company);
        if (existingAccount.isPresent()) {
            log.info("Company {} already has an account, skipping creation", company.getId());
            return existingAccount.get();
        }

        log.info("Creating account for company {}", company.getId());
        Account account =
                Account.builder()
                        .accountType(AccountType.COMPANY)
                        .company(company)
                        .balance(BigDecimal.ZERO)
                        .currency("AUD")
                        .build();

        return accountRepository.save(account);
    }

    /**
     * Get the platform account.
     *
     * @return the platform account
     * @throws IllegalStateException if the platform account does not exist
     */
    @Transactional(readOnly = true)
    public Account getPlatformAccount() {
        return accountRepository
                .findByAccountType(AccountType.PLATFORM)
                .orElseThrow(() -> new IllegalStateException("Platform account does not exist"));
    }

    /**
     * Get an account by its ID.
     *
     * @param id the ID of the account to get
     * @return the account
     * @throws IllegalArgumentException if the account does not exist
     */
    @Transactional(readOnly = true)
    public Account getAccountById(Long id) {
        return accountRepository
                .findById(id)
                .orElseThrow(
                        () -> new IllegalArgumentException("Account not found with ID: " + id));
    }

    /**
     * Get an account by its user.
     *
     * @param user the user to get the account for
     * @return the account
     * @throws IllegalArgumentException if the account does not exist
     */
    @Transactional(readOnly = true)
    public Account getAccountByUser(User user) {
        return accountRepository
                .findByUser(user)
                .orElseThrow(
                        () ->
                                new IllegalArgumentException(
                                        "Account not found for user: " + user.getId()));
    }

    /**
     * Get an account DTO by its user.
     *
     * @param user the user to get the account for
     * @return the account DTO
     * @throws IllegalArgumentException if the account does not exist
     */
    @Transactional(readOnly = true)
    public AccountDTO getAccountDTOByUser(User user) {
        Account account = getAccountByUser(user);
        return accountMapper.toDto(account);
    }

    /**
     * Get an account by its company.
     *
     * @param company the company to get the account for
     * @return the account
     * @throws IllegalArgumentException if the account does not exist
     */
    @Transactional(readOnly = true)
    public Account getAccountByCompany(Company company) {
        return accountRepository
                .findByCompany(company)
                .orElseThrow(
                        () ->
                                new IllegalArgumentException(
                                        "Account not found for company: " + company.getId()));
    }

    /**
     * Get an account DTO by its company.
     *
     * @param company the company to get the account for
     * @return the account DTO
     * @throws IllegalArgumentException if the account does not exist
     */
    @Transactional(readOnly = true)
    public AccountDTO getAccountDTOByCompany(Company company) {
        Account account = getAccountByCompany(company);
        return accountMapper.toDto(account);
    }

    /**
     * Update the balance of an account.
     *
     * @param account the account to update
     * @param amount the amount to add to the balance (can be negative)
     * @return the updated account
     */
    @Transactional
    public Account updateBalance(Account account, BigDecimal amount) {
        account.setBalance(account.getBalance().add(amount));
        return accountRepository.save(account);
    }

    /**
     * Get the balance of the current user.
     *
     * @return the account DTO
     */
    @Transactional(readOnly = true)
    public AccountDTO getBalance() {
        User user = authUtils.getCurrentUser();
        Company company = authUtils.getCurrentUserCompany();

        if (user.getUserType() == UserType.COMPANY && company != null) {
            return accountMapper.toDto(getAccountByCompany(company));
        }

        return accountMapper.toDto(getAccountByUser(user));
    }
}
