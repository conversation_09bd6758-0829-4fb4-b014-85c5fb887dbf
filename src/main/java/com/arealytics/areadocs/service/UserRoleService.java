package com.arealytics.areadocs.service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.areadocs.domain.Role;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.domain.UserRole;
import com.arealytics.areadocs.dto.requestDTO.UserRoleRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.UserRoleDTO;
import com.arealytics.areadocs.exception.RoleException;
import com.arealytics.areadocs.mapper.UserRoleMapper;
import com.arealytics.areadocs.repository.RoleRepository;
import com.arealytics.areadocs.repository.UserRepository;
import com.arealytics.areadocs.repository.UserRoleRepository;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/** Service for managing user roles. */
@Service
@RequiredArgsConstructor
@Slf4j
public class UserRoleService {

    private final UserRoleRepository userRoleRepository;
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final UserRoleMapper userRoleMapper;

    /**
     * Assign a role to a user
     *
     * @param userRoleRequestDTO The user role assignment request
     * @return The created user role DTO
     */
    @Transactional
    public UserRoleDTO assignRoleToUser(UserRoleRequestDTO userRoleRequestDTO) {
        log.info(
                "Assigning role {} to user {}",
                userRoleRequestDTO.getRoleId(),
                userRoleRequestDTO.getUserId());

        // Check if user exists
        User user =
                userRepository
                        .findById(userRoleRequestDTO.getUserId())
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "User not found with id: "
                                                        + userRoleRequestDTO.getUserId()));

        // Check if role exists
        Role role =
                roleRepository
                        .findById(userRoleRequestDTO.getRoleId())
                        .orElseThrow(
                                () ->
                                        new RoleException.RoleNotFoundException(
                                                "Role not found with id: "
                                                        + userRoleRequestDTO.getRoleId()));

        // Check if the user already has this role
        if (userRoleRepository.existsByUserIdAndRoleId(user.getId(), role.getId())) {
            throw new IllegalStateException("User already has this role assigned");
        }

        // Create and save the user role
        UserRole userRole = new UserRole();
        userRole.setUser(user);
        userRole.setRole(role);
        //
        //        if (userRoleRequestDTO.getStatus() != null) {
        //            userRole.setStatus(userRoleRequestDTO.getStatus());
        //        }

        UserRole savedUserRole = userRoleRepository.save(userRole);
        log.info("Role {} assigned to user {} successfully", role.getName(), user.getEmail());

        return userRoleMapper.toDto(savedUserRole);
    }

    /**
     * Get all roles assigned to a user
     *
     * @param userId The user ID
     * @return List of user role DTOs
     */
    @Transactional(readOnly = true)
    public UserRoleDTO getUserRoles(Long userId) {
        // Check if user exists
        if (!userRepository.existsById(userId)) {
            throw new EntityNotFoundException("User not found with id: " + userId);
        }

        return userRoleMapper.toDto(userRoleRepository.findByUserId(userId));
    }

    /**
     * Get all users assigned to a role
     *
     * @param roleId The role ID
     * @return List of user role DTOs
     */
    @Transactional(readOnly = true)
    public List<UserRoleDTO> getRoleUsers(Long roleId) {
        // Check if role exists
        if (!roleRepository.existsById(roleId)) {
            throw new RoleException.RoleNotFoundException("Role not found with id: " + roleId);
        }

        return userRoleRepository.findByRoleId(roleId).stream()
                .map(userRoleMapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Get a specific user role assignment
     *
     * @param id The user role ID
     * @return The user role DTO
     */
    @Transactional(readOnly = true)
    public UserRoleDTO getUserRoleById(Long id) {
        UserRole userRole =
                userRoleRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "User role not found with id: " + id));

        return userRoleMapper.toDto(userRole);
    }

    /**
     * Update a user role assignment
     *
     * @param id The user role ID
     * @param userRoleRequestDTO The updated user role data
     * @return The updated user role DTO
     */
    @Transactional
    public UserRoleDTO updateUserRole(Long id, UserRoleRequestDTO userRoleRequestDTO) {
        UserRole existingUserRole =
                userRoleRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "User role not found with id: " + id));

        // If user ID is changing, check if the new user exists
        if (userRoleRequestDTO.getUserId() != null
                && !existingUserRole.getUser().getId().equals(userRoleRequestDTO.getUserId())) {
            User newUser =
                    userRepository
                            .findById(userRoleRequestDTO.getUserId())
                            .orElseThrow(
                                    () ->
                                            new EntityNotFoundException(
                                                    "User not found with id: "
                                                            + userRoleRequestDTO.getUserId()));
            existingUserRole.setUser(newUser);
        }

        // If role ID is changing, check if the new role exists
        if (userRoleRequestDTO.getRoleId() != null
                && !existingUserRole.getRole().getId().equals(userRoleRequestDTO.getRoleId())) {
            Role newRole =
                    roleRepository
                            .findById(userRoleRequestDTO.getRoleId())
                            .orElseThrow(
                                    () ->
                                            new RoleException.RoleNotFoundException(
                                                    "Role not found with id: "
                                                            + userRoleRequestDTO.getRoleId()));
            existingUserRole.setRole(newRole);
        }

        // Update status if provided
        //        if (userRoleRequestDTO.getStatus() != null) {
        //            existingUserRole.setStatus(userRoleRequestDTO.getStatus());
        //        }

        UserRole updatedUserRole = userRoleRepository.save(existingUserRole);
        log.info("User role with ID {} updated successfully", id);

        return userRoleMapper.toDto(updatedUserRole);
    }

    /**
     * Remove a role from a user
     *
     * @param id The user role ID
     */
    @Transactional
    public void removeUserRole(Long id) {
        if (!userRoleRepository.existsById(id)) {
            throw new EntityNotFoundException("User role not found with id: " + id);
        }

        userRoleRepository.deleteById(id);
        log.info("User role with ID {} removed successfully", id);
    }

    /**
     * Remove a specific role from a user
     *
     * @param userId The user ID
     * @param roleId The role ID
     */
    @Transactional
    public void removeRoleFromUser(Long userId, Long roleId) {
        UserRole userRole =
                userRoleRepository
                        .findByUserIdAndRoleId(userId, roleId)
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "User role not found for user ID: "
                                                        + userId
                                                        + " and role ID: "
                                                        + roleId));

        userRoleRepository.delete(userRole);
        log.info("Role {} removed from user {} successfully", roleId, userId);
    }

    /**
     * Get all user role assignments with pagination
     *
     * @param pageable The pagination information
     * @return Page of user role DTOs
     */
    @Transactional(readOnly = true)
    public Page<UserRoleDTO> getAllUserRoles(Pageable pageable) {
        return userRoleRepository.findAll(pageable).map(userRoleMapper::toDto);
    }

    /**
     * Get a user role by user ID and role ID
     *
     * @param userId The user ID
     * @param roleId The role ID
     * @return Optional containing the user role DTO if found
     */
    @Transactional(readOnly = true)
    public Optional<UserRoleDTO> getUserRoleByUserIdAndRoleId(Long userId, Long roleId) {
        return userRoleRepository.findByUserIdAndRoleId(userId, roleId).map(userRoleMapper::toDto);
    }
}
