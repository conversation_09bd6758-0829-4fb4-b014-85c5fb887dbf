package com.arealytics.areadocs.service;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.Duration;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.PresignedPutObjectRequest;
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest;

@Service
public class S3Service {

    private final S3Client s3Client;
    private final S3Presigner s3Presigner;

    @Value("${aws.s3.bucket.name}")
    private String bucketName;

    @Value("${aws.region}")
    private String region;

    @Value("${s3.paths.user-profile-images-uploads}")
    private String userProfileImagesPath;

    public S3Service(S3Client s3Client, S3Presigner s3Presigner) {
        this.s3Client = s3Client;
        this.s3Presigner = s3Presigner;
    }

    public void uploadFile(byte[] fileData, String keyName) {
        if (fileData == null) {
            throw new IllegalArgumentException("File data cannot be null");
        }

        if (s3Client == null) {
            System.err.println(
                    "WARNING: S3 client is not initialized. File upload skipped for key: "
                            + keyName);
            return;
        }

        try {
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(fileData);

            PutObjectRequest putObjectRequest =
                    PutObjectRequest.builder().bucket(bucketName).key(keyName).build();

            PutObjectResponse response =
                    s3Client.putObject(
                            putObjectRequest,
                            software.amazon.awssdk.core.sync.RequestBody.fromBytes(fileData));

            System.out.println("File uploaded successfully to S3 with key: " + keyName);
        } catch (Exception e) {
            System.err.println("ERROR: Failed to upload file to S3: " + e.getMessage());
        }
    }

    public void uploadStream(
            InputStream inputStream, long contentLength, String keyName, String contentType) {
        if (inputStream == null) {
            throw new IllegalArgumentException("Input stream cannot be null");
        }

        if (s3Client == null) {
            System.err.println(
                    "WARNING: S3 client is not initialized. File upload skipped for key: "
                            + keyName);
            return;
        }

        try {
            PutObjectRequest putObjectRequest =
                    PutObjectRequest.builder()
                            .bucket(bucketName)
                            .key(keyName)
                            .contentType(contentType)
                            .build();

            s3Client.putObject(
                    putObjectRequest,
                    software.amazon.awssdk.core.sync.RequestBody.fromInputStream(
                            inputStream, contentLength));

            System.out.println("File stream uploaded successfully to S3 with key: " + keyName);
        } catch (Exception e) {
            System.err.println("ERROR: Failed to upload stream to S3: " + e.getMessage());
        }
    }

    public String generatePresignedUrl(String userEmail, String fileName) {
        if (s3Presigner == null) {
            throw new IllegalStateException("S3Presigner is not initialized");
        }

        String fileExtension = "";
        if (fileName != null && fileName.contains(".")) {
            fileExtension = fileName.substring(fileName.lastIndexOf("."));
        }
        String uniqueFileName = UUID.randomUUID().toString() + userEmail + fileExtension;

        String cleanedProfilePath =
                (userProfileImagesPath != null) ? userProfileImagesPath.trim() : "";
        if (cleanedProfilePath.endsWith("/")) {
            cleanedProfilePath = cleanedProfilePath.substring(0, cleanedProfilePath.length() - 1);
        }

        String keyName =
                cleanedProfilePath.isEmpty()
                        ? uniqueFileName
                        : cleanedProfilePath + "/" + uniqueFileName;

        PutObjectRequest putObjectRequest =
                PutObjectRequest.builder().bucket(bucketName).key(keyName).build();

        PutObjectPresignRequest presignRequest =
                PutObjectPresignRequest.builder()
                        .signatureDuration(Duration.ofMinutes(10))
                        .putObjectRequest(putObjectRequest)
                        .build();

        PresignedPutObjectRequest presignedRequest = s3Presigner.presignPutObject(presignRequest);
        return presignedRequest.url().toString();
    }

    public String getFileUrlFromPresignedUrl(String presignedUrl) {
        return presignedUrl.split("\\?")[0];
    }
}
