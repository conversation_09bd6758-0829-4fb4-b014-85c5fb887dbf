package com.arealytics.areadocs.service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.areadocs.domain.*;
import com.arealytics.areadocs.dto.requestDTO.OrderRequestDTO;
import com.arealytics.areadocs.dto.requestDTO.serv.CreateOrderRequest;
import com.arealytics.areadocs.dto.responseDTO.OrderItemDetailsResponse;
import com.arealytics.areadocs.dto.responseDTO.OrderResponseDTO;
import com.arealytics.areadocs.dto.responseDTO.serv.CreateOrderResponse;
import com.arealytics.areadocs.dto.responseDTO.serv.OrderItemResponse;
import com.arealytics.areadocs.enumeration.*;
import com.arealytics.areadocs.enumeration.SERV.ProductStatus;
import com.arealytics.areadocs.exception.DocumentNotFoundException;
import com.arealytics.areadocs.exception.FailedToUpdateOrderStatusException;
import com.arealytics.areadocs.exception.OrderProcessingException;
import com.arealytics.areadocs.exception.PaymentFailedException;
import com.arealytics.areadocs.mapper.OrderMapper;
import com.arealytics.areadocs.repository.*;
import com.arealytics.areadocs.util.S3UrlBuilder;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Service for managing orders and order processing workflow. Handles order creation, payment
 * processing, and external API integration.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderService {

    private static final String DEFAULT_CURRENCY = "AUD";
    private static final String DEFAULT_PAYMENT_METHOD = "WALLET";
    private final OrderRepository orderRepository;
    private final AccountService accountService;
    private final TransactionService transactionService;
    private final MockServApiService mockServApiService;
    private final OrderMapper orderMapper;
    private final OrderItemRepository orderItemRepository;
    private final ServApiService servApiService;
    private final OrderRequestMapperService orderRequestMapperService;
    private final FolderService folderService;
    private final DocumentService documentService;
    private final DocumentSourceRepository documentSourceRepository;
    private final DocumentRepository documentRepository;
    private final TransactionRepository transactionRepository;
    private final S3UrlBuilder s3UrlBuilder;

    @Value("${serv.api.useMockResponses:false}")
    private boolean useMockResponses;

    @Value("${s3.paths.document-uploads}")
    private String fileUploadDirectory;

    /**
     * Process an order from the CreateOrderRequest.
     *
     * <p>This method handles the complete order workflow: 1. Validate and fetch order items 2.
     * Calculate total amount 3. Create order 4. Process payment 5. Place external order 6. Finalize
     * order
     *
     * @param request the order request containing product details
     * @param user the user placing the order
     * @param company the company associated with the order
     * @return OrderResponseDTO with complete order information
     * @throws OrderProcessingException if order processing fails
     * @throws PaymentFailedException if payment processing fails
     */
    @Transactional(rollbackFor = {OrderProcessingException.class, PaymentFailedException.class})
    public OrderResponseDTO processOrder(OrderRequestDTO request, User user, Company company) {
        log.info(
                "Processing order for user ID: {} with {} items",
                user.getId(),
                request.getOrderItemIds() != null ? request.getOrderItemIds().size() : 0);

        try {
            // Validate and fetch order items
            List<OrderItem> orderItems = validateAndFetchOrderItems(request, user);

            // Calculate total amount
            BigDecimal totalAmount = calculateTotalAmount(orderItems);
            log.debug("Calculated total amount: {}", totalAmount);

            // Create order
            Order order = createOrder(user, company, totalAmount, orderItems);
            log.info("Created order with ID: {}", order.getId());

            // Process payment
            TransactionService.TransactionResult paymentResult =
                    processPayment(user, company, order, totalAmount);
            log.info("Payment processed successfully for order: {}", order.getId());

            // Place external order
            CreateOrderResponse externalOrderResponse =
                    placeExternalOrder(orderItems, user, company);

            // Finalize order
            finalizeOrder(order, externalOrderResponse, orderItems);
            log.info("Order {} finalized successfully", order.getId());

            return buildOrderResponse(order, orderItems, user, paymentResult);

        } catch (PaymentFailedException e) {
            log.error("Payment failed for order by user ID {}: {}", user.getId(), e.getMessage());
            rollbackOrderItems(request.getOrderItemIds());
            throw new OrderProcessingException("Payment failed: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("Error processing order for user ID {}: {}", user.getId(), e.getMessage(), e);
            rollbackOrderItems(request.getOrderItemIds());
            throw new OrderProcessingException("Failed to process order: " + e.getMessage(), e);
        }
    }

    /**
     * Validates order items and ensures they belong to the user.
     *
     * @param request the order request containing order item IDs
     * @param user the user placing the order
     * @return list of validated OrderItem objects
     * @throws OrderProcessingException if validation fails
     */
    private List<OrderItem> validateAndFetchOrderItems(OrderRequestDTO request, User user) {
        // Validate request
        if (request.getOrderItemIds() == null || request.getOrderItemIds().isEmpty()) {
            throw new OrderProcessingException("No order items provided");
        }

        // Remove duplicates from request
        List<Long> uniqueOrderItemIds = new ArrayList<>(new HashSet<>(request.getOrderItemIds()));

        // Fetch items
        List<OrderItem> orderItems = orderItemRepository.findAllById(uniqueOrderItemIds);
        if (orderItems.isEmpty()) {
            throw new OrderProcessingException("No valid order items found");
        }

        // Check for missing items
        Set<Long> foundIds = orderItems.stream().map(OrderItem::getId).collect(Collectors.toSet());
        Set<Long> missingIds =
                uniqueOrderItemIds.stream()
                        .filter(id -> !foundIds.contains(id))
                        .collect(Collectors.toSet());

        if (!missingIds.isEmpty()) {
            throw new OrderProcessingException("Order items not found: " + missingIds);
        }

        // Validate ownership using existing method
        validateOrderItemsOwnership(orderItems, user);

        // Validate item statuses
        List<Long> alreadyOrderedItems =
                orderItems.stream()
                        .filter(
                                item ->
                                        item.getOrder() != null
                                                || OrderItemStatus.PENDING.equals(item.getStatus())
                                                || OrderItemStatus.DELIVERED.equals(
                                                        item.getStatus()))
                        .map(OrderItem::getId)
                        .toList();

        List<Long> notInCartItems =
                orderItems.stream()
                        .filter(item -> !OrderItemStatus.CART.equals(item.getStatus()))
                        .map(OrderItem::getId)
                        .toList();

        // Build error messages for status validation
        List<String> errors = new ArrayList<>();
        if (!alreadyOrderedItems.isEmpty()) {
            errors.add("Items already in an order: " + alreadyOrderedItems);
        }
        if (!notInCartItems.isEmpty()) {
            errors.add("Items not in cart status: " + notInCartItems);
        }

        if (!errors.isEmpty()) {
            log.error(
                    "Order validation failed for user {}: {}",
                    user.getId(),
                    String.join("; ", errors));
            throw new OrderProcessingException("Invalid order items: " + String.join("; ", errors));
        }

        // Validate prices are set
        List<Long> itemsWithoutPrice =
                orderItems.stream()
                        .filter(
                                item ->
                                        item.getFinalPrice() == null
                                                || item.getFinalPrice().compareTo(BigDecimal.ZERO)
                                                        <= 0)
                        .map(OrderItem::getId)
                        .toList();

        if (!itemsWithoutPrice.isEmpty()) {
            throw new OrderProcessingException("Invalid prices for items: " + itemsWithoutPrice);
        }

        return orderItems;
    }

    /**
     * Validates that all order items belong to the user.
     *
     * @param orderItems the list of order items to validate
     * @param user the user placing the order
     * @throws OrderProcessingException if any order item does not belong to the user
     */
    private void validateOrderItemsOwnership(List<OrderItem> orderItems, User user) {
        boolean hasInvalidItems =
                orderItems.stream()
                        .anyMatch(
                                item ->
                                        item.getUser() == null
                                                || !item.getUser().getId().equals(user.getId()));

        if (hasInvalidItems) {
            log.error("User {} attempted to order items that don't belong to them", user.getId());
            throw new OrderProcessingException(
                    "Invalid order items: some items don't belong to the user");
        }
    }

    /**
     * Calculate the total amount for all order items.
     *
     * @param orderItems list of order items
     * @return the total amount as BigDecimal
     */
    private BigDecimal calculateTotalAmount(List<OrderItem> orderItems) {
        return orderItems.stream()
                .map(OrderItem::getFinalPrice)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * Create a new order and associate order items.
     *
     * @param user the user placing the order
     * @param totalAmount the total order amount
     * @param orderItems the list of order items to associate
     * @return the created and saved Order entity
     */
    private Order createOrder(
            User user, Company company, BigDecimal totalAmount, List<OrderItem> orderItems) {
        Order order =
                Order.builder()
                        .user(user)
                        .company(company)
                        .totalAmount(totalAmount)
                        .status(OrderStatus.PENDING)
                        .orderDate(LocalDateTime.now())
                        .paymentMethod(DEFAULT_PAYMENT_METHOD)
                        .orderItems(new ArrayList<>())
                        .build();

        final Order savedOrder = orderRepository.save(order);

        // Establish bidirectional relationship
        orderItems.forEach(
                item -> {
                    item.setOrder(savedOrder);
                    item.setStatus(OrderItemStatus.PENDING);
                });

        orderItemRepository.saveAll(orderItems);

        return orderRepository.findById(savedOrder.getId()).orElseThrow();
    }

    /**
     * Process payment for the order.
     *
     * @param user the user making the payment
     * @param order the order being paid for
     * @param totalAmount the total amount to pay
     * @return the result of the payment transaction
     * @throws PaymentFailedException if payment processing fails
     */
    private TransactionService.TransactionResult processPayment(
            User user, Company company, Order order, BigDecimal totalAmount) {

        if (user.getUserType() == UserType.COMPANY && company != null) {

            if (company.getIsActive()) {
                log.info(
                        "Processing payment for order {} for company {}",
                        order.getId(),
                        company.getId());
                return transactionService.transferFunds(
                        accountService.getAccountByCompany(company),
                        accountService.getPlatformAccount(),
                        totalAmount,
                        EntryType.PURCHASE,
                        "Payment for order",
                        user,
                        order);
            }
        }
        Account userAccount = accountService.getAccountByUser(user);
        Account platformAccount = accountService.getPlatformAccount();

        String description =
                transactionService.createTransactionDescription(
                        user, userAccount, totalAmount, DEFAULT_CURRENCY, "Payment for order");

        return transactionService.transferFunds(
                userAccount,
                platformAccount,
                totalAmount,
                EntryType.PURCHASE,
                description,
                user,
                order);
    }

    /**
     * Place an order with the external service API.
     *
     * @param orderItems the order items to include in the request
     * @param user the user placing the order
     * @param company the company associated with the order
     * @return the external order response, or null if the request fails
     */
    private CreateOrderResponse placeExternalOrder(
            List<OrderItem> orderItems, User user, Company company) {
        try {
            CreateOrderRequest servAPIRequest =
                    orderRequestMapperService.buildServOrderRequestFromItems(
                            orderItems, user, company);

            if (useMockResponses) {
                log.info("Using mock response for external service API");
                return mockServApiService.getMockCreateOrder(servAPIRequest).getBody();
            } else {
                log.info("Placing order with external service API");
                return servApiService
                        .createOrder(servAPIRequest, CreateOrderResponse.class)
                        .getBody();
            }
        } catch (Exception e) {
            log.warn("Could not place order with external service API: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Finalize order processing by updating external IDs and marking items as delivered.
     *
     * @param order the order to finalize
     * @param response the external service API response
     * @param orderItems the order items to update
     */
    private void finalizeOrder(
            Order order, CreateOrderResponse response, List<OrderItem> orderItems) {
        order.setStatus(OrderStatus.PROCESSING);

        // Update external order IDs if available
        if (response != null && response.getOrderId() != null) {
            order.setExternalOrderId(response.getOrderId());
            updateOrderItemExternalIds(orderItems, response);
        }

        orderRepository.save(order);

        // Complete order items
        orderItems.forEach(item -> item.setStatus(OrderItemStatus.IN_TRANSIT));
        orderItemRepository.saveAll(orderItems);

        // Mark order as completed
        order.setStatus(OrderStatus.PAID);
        orderRepository.save(order);

        log.info("Order ID {} completed successfully", order.getId());
    }

    /**
     * Update order items with external IDs from the API response.
     *
     * @param orderItems the order items to update
     * @param response the external service API response containing external IDs
     */
    private void updateOrderItemExternalIds(
            List<OrderItem> orderItems, CreateOrderResponse response) {
        if (response.getItemsOrdered() == null) return;

        Map<String, String> productCodeToOrderItemNumber =
                response.getItemsOrdered().stream()
                        .filter(item -> item.getProductCode() != null)
                        .collect(
                                Collectors.toMap(
                                        CreateOrderResponse.ItemOrdered::getProductCode,
                                        item -> String.valueOf(item.getOrderItemNumber()),
                                        (existing, replacement) -> existing));

        log.info(
                "Updating order items with external IDs from external service API {}",
                productCodeToOrderItemNumber);
        orderItems.stream()
                .filter(orderItem -> orderItem.getProductCode() != null)
                .forEach(
                        orderItem -> {
                            String orderItemNumber =
                                    productCodeToOrderItemNumber.get(orderItem.getProductCode());
                            if (orderItemNumber != null) {
                                orderItem.setExternalOrderItemId(orderItemNumber);
                            }
                        });

        orderItemRepository.saveAll(orderItems);
    }

    /**
     * Rollback order items to CART status on failure.
     *
     * @param orderItemIds the list of order item IDs to rollback
     */
    private void rollbackOrderItems(List<Long> orderItemIds) {
        if (orderItemIds == null || orderItemIds.isEmpty()) return;

        try {
            List<OrderItem> orderItems = orderItemRepository.findAllById(orderItemIds);
            orderItems.forEach(
                    item -> {
                        if (OrderItemStatus.PENDING.equals(item.getStatus())
                                || OrderItemStatus.DELIVERED.equals(item.getStatus())) {
                            item.setStatus(OrderItemStatus.CART);
                            item.setOrder(null);
                        }
                    });
            orderItemRepository.saveAll(orderItems);
            log.info("Rolled back {} order items to CART status", orderItems.size());
        } catch (Exception e) {
            log.warn("Failed to rollback order items: {}", e.getMessage());
        }
    }

    /**
     * Build order response DTO with all necessary details.
     *
     * @param order the processed order
     * @param orderItems the list of order items
     * @param user the user who placed the order
     * @param paymentResult the payment transaction result
     * @return complete order response DTO
     */
    private OrderResponseDTO buildOrderResponse(
            Order order,
            List<OrderItem> orderItems,
            User user,
            TransactionService.TransactionResult paymentResult) {
        try {
            OrderResponseDTO response = orderMapper.toDto(order);

            // Set external order ID
            if (order.getExternalOrderId() != null) {
                response.setExternalOrderId(order.getExternalOrderId());
            }
            // Set transaction details
            if (paymentResult != null && paymentResult.getTransaction() != null) {
                response.setTransaction(
                        orderMapper.transactionToDto(paymentResult.getTransaction()));
            }

            // Enrich with product details
            enrichOrderItemsWithProductDetails(response, orderItems);

            return response;
        } catch (Exception e) {
            log.error("Error building order response: {}", e.getMessage(), e);
            return createFallbackResponse(order);
        }
    }

    /**
     * Build full name from first and last name with null safety.
     *
     * @param firstName the first name
     * @param lastName the last name
     * @return the combined full name, trimmed of whitespace
     */
    private String buildFullName(String firstName, String lastName) {
        String first = firstName != null ? firstName : "";
        String last = lastName != null ? lastName : "";
        return (first + " " + last).trim();
    }

    /**
     * Enrich order item DTOs with product details.
     *
     * @param response the order response DTO to enrich
     * @param orderItems the list of order items with product details
     */
    private void enrichOrderItemsWithProductDetails(
            OrderResponseDTO response, List<OrderItem> orderItems) {
        if (response.getItems() == null || orderItems == null) return;

        Map<Long, OrderItem> orderItemMap =
                orderItems.stream()
                        .collect(Collectors.toMap(OrderItem::getId, item -> item, (a, b) -> a));

        response.getItems()
                .forEach(
                        responseItem -> {
                            if (responseItem.getId() != null) {
                                OrderItem orderItem = orderItemMap.get(responseItem.getId());
                                if (orderItem != null) {
                                    responseItem.setProductCode(orderItem.getProductCode());
                                    responseItem.setProductName(orderItem.getProductName());
                                }
                            }
                        });
    }

    /**
     * Create a fallback response in case of error.
     *
     * @param order the order to create fallback response for
     * @return minimal order response DTO with basic information
     */
    private OrderResponseDTO createFallbackResponse(Order order) {
        OrderResponseDTO response = new OrderResponseDTO();
        response.setId(order.getId());
        response.setStatus(OrderStatus.valueOf(order.getStatus().name()));
        response.setTotalAmount(order.getTotalAmount());
        return response;
    }

    /**
     * Get an order by its ID.
     *
     * @param id the ID of the order
     * @return the order response DTO
     * @throws IllegalArgumentException if the order does not exist
     */
    @Transactional(readOnly = true)
    public OrderResponseDTO getOrderById(Long id) {
        Order order =
                orderRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new IllegalArgumentException(
                                                "Order not found with ID: " + id));
        // First, check for and trigger the latest status from the external service
        if (order.getStatus() == OrderStatus.PAID || order.getStatus() == OrderStatus.PROCESSING) {
            try {
                updateOrderStatusAsync(order).join(); // Block until async update is complete
            } catch (Exception e) {
                log.error(
                        "Error during pre-fetch status update for order {}: {}",
                        id,
                        e.getMessage());
                throw new FailedToUpdateOrderStatusException("Failed to update order status", e);
            }
        }

        // Re-fetch the order to get the most up-to-date data after the async call
        if (!orderRepository.existsById(id)) {
            throw new IllegalArgumentException("Order not found with ID: " + id);
        }
        Order updatedOrder =
                orderRepository.findById(id).isPresent()
                        ? orderRepository.findById(id).get()
                        : null;
        OrderResponseDTO orderResponseDTO = orderMapper.toDto(updatedOrder);

        // convert s3 URL to full URL
        if (updatedOrder != null && updatedOrder.getOrderItems() != null) {
            for (OrderItem item : updatedOrder.getOrderItems()) {
                if (item.getDocument() != null && item.getDocument().getFilePath() != null) {
                    String fullUrl = s3UrlBuilder.buildS3Url(item.getDocument().getFilePath());
                    item.getDocument().setFilePath(fullUrl);
                }
            }
        }

        // Populate transaction details from the repository
        transactionRepository
                .findTopByOrderOrderByTransactionDateDesc(updatedOrder)
                .ifPresent(
                        transaction ->
                                orderResponseDTO.setTransaction(
                                        orderMapper.transactionToDto(transaction)));

        return orderResponseDTO;
    }

    /**
     * Get all orders for a user.
     *
     * @param user the user
     * @return the list of order response DTOs
     */
    @Transactional(readOnly = true)
    public List<OrderResponseDTO> getOrdersByUser(User user) {
        return orderMapper.toDto(orderRepository.findByUser(user));
    }

    /**
     * Get details of a specific order item.
     *
     * @param orderId the ID of the order
     * @param orderItemId the ID of the order item
     * @return the order item details response
     * @throws IllegalArgumentException if the order item does not exist
     */
    @Transactional(readOnly = true)
    public OrderItemDetailsResponse getOrderItemDetails(Long orderId, Long orderItemId) {
        OrderItem orderItem =
                orderItemRepository
                        .findByIdAndOrderId(orderItemId, orderId)
                        .orElseThrow(
                                () ->
                                        new IllegalArgumentException(
                                                "Order item not found with ID: "
                                                        + orderItemId
                                                        + " for order: "
                                                        + orderId));
        return orderMapper.toOrderItemDetailsResponse(orderItem);
    }

    // get order by transaction id
    @Transactional(readOnly = true)
    public OrderResponseDTO getOrderByTransactionId(Long transactionId) {
        Transaction transaction = transactionService.getTransactionById(transactionId);

        if (transaction == null
                || transaction.getOrder() == null
                || transaction.getTransactionType() != TransactionType.PURCHASE) {
            throw new IllegalArgumentException(
                    "Order not found for transaction ID: " + transactionId);
        }
        // Update order status asynchronously
        updateOrderStatusAsync(transaction.getOrder()).join();
        OrderResponseDTO dto = orderMapper.toDto(transaction.getOrder());
        // Convert S3 URL to full URL
        if (dto.getItems() != null) {
            for (OrderResponseDTO.OrderItemDTO item : dto.getItems()) {
                if (item.getDocument() != null && item.getDocument().getFilePath() != null) {
                    String fullUrl = s3UrlBuilder.buildS3Url(item.getDocument().getFilePath());
                    item.getDocument().setFilePath(fullUrl);
                }
            }
        }
        return dto;
    }

    @Async
    @Transactional
    public CompletableFuture<Void> updateOrderStatusAsync(Order order) {
        List<OrderItem> orderItems = orderItemRepository.findByOrder(order);

        log.info("Processing order items for order ID {}", order.getId());

        orderItems.stream()
                .filter(item -> !OrderItemStatus.DELIVERED.equals(item.getStatus()))
                .forEach(
                        item -> {
                            System.out.println("Order item ID: " + item.getId());
                            System.out.println("Order Status: " + item.getStatus());
                        });
        // Create async tasks for all DELIVERED order items
        List<CompletableFuture<Void>> futures =
                orderItems.stream()
                        .filter(item -> !OrderItemStatus.DELIVERED.equals(item.getStatus()))
                        .map(this::processOrderItemAsync)
                        .toList();

        log.info("Processed {} order items for order ID {}", futures.size(), order.getId());
        // Wait for all async operations to complete
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
    }

    public CompletableFuture<Void> processOrderItemAsync(OrderItem orderItem) {
        log.info("Processing order item ID {}", orderItem.getId());
        return CompletableFuture.supplyAsync(
                        () -> {
                            // Re-fetch the orderItem to ensure we have the latest version for
                            // optimistic locking
                            OrderItem currentOrderItem =
                                    orderItemRepository
                                            .findById(orderItem.getId())
                                            .orElseThrow(
                                                    () ->
                                                            new DocumentNotFoundException(
                                                                    "Order item not found while"
                                                                            + " processing"
                                                                            + " asynchronously: "
                                                                            + orderItem.getId()));

                            // Re-fetch the associated Order explicitly to ensure it's managed by
                            // the current session
                            Order associatedOrder =
                                    orderRepository
                                            .findById(currentOrderItem.getOrder().getId())
                                            .orElseThrow(
                                                    () ->
                                                            new DocumentNotFoundException(
                                                                    "Associated order not found"
                                                                        + " while processing item: "
                                                                            + currentOrderItem
                                                                                    .getOrder()
                                                                                    .getId()));

                            // Call external API
                            return servApiService.getServOrderItem(
                                    associatedOrder
                                            .getExternalOrderId(), // Use the re-fetched order
                                    currentOrderItem.getExternalOrderItemId(),
                                    OrderItemResponse.class);
                        })
                .thenAccept(
                        response -> {
                            // Re-fetch the orderItem again just before updating, to ensure
                            // consistency
                            OrderItem finalOrderItem =
                                    orderItemRepository
                                            .findById(orderItem.getId())
                                            .orElseThrow(
                                                    () ->
                                                            new DocumentNotFoundException(
                                                                    "Order item not found before"
                                                                            + " final update: "
                                                                            + orderItem.getId()));

                            // Re-fetch the associated Order again for the final updates within this
                            // transaction
                            Order finalOrder =
                                    orderRepository
                                            .findById(finalOrderItem.getOrder().getId())
                                            .orElseThrow(
                                                    () ->
                                                            new DocumentNotFoundException(
                                                                    "Associated order not found"
                                                                        + " before final update of"
                                                                        + " order: "
                                                                            + finalOrderItem
                                                                                    .getOrder()
                                                                                    .getId()));

                            if (response == null || response.getBody() == null) {
                                throw new DocumentNotFoundException(
                                        "No documents found for order item: "
                                                + finalOrderItem.getId());
                            }

                            OrderItemResponse apiResponse = response.getBody();

                            if (ProductStatus.COMPLETE
                                    .name()
                                    .equals(apiResponse.getProductStatus())) {

                                // Check if a document is ALREADY associated with this order item
                                if (finalOrderItem.getDocument() == null) {
                                    // Create and save document source
                                    DocumentSource documentSource = new DocumentSource();
                                    documentSource.setName(apiResponse.getProductName());
                                    documentSource.setBaseUrl(apiResponse.getResourceLocation());
                                    documentSource.setStatus(DocumentSourceStatus.ACTIVE);
                                    documentSourceRepository.save(documentSource);

                                    // Download and upload to S3
                                    String s3Url =
                                            documentService.streamAndUploadToS3(
                                                    apiResponse.getResourceLocation(),
                                                    fileUploadDirectory);

                                    // Create and save a document
                                    Document document = new Document();
                                    document.setTitle(apiResponse.getProductName());
                                    document.setExpiryDate(
                                            OffsetDateTime.parse(apiResponse.getResourceExpiry())
                                                    .toLocalDateTime());
                                    document.setFilePath(s3Url);
                                    document.setSource(documentSource);
                                    document.setUser(finalOrder.getUser());
                                    document.setCompany(finalOrder.getCompany());
                                    document.setFolder(
                                            folderService.getFolderById(
                                                    finalOrderItem.getFolderId()));
                                    Document savedDocument = documentRepository.save(document);

                                    finalOrderItem.setDocument(savedDocument);
                                }
                                // Update order item status
                                finalOrderItem.setStatus(OrderItemStatus.COMPLETE);
                                orderItemRepository.save(finalOrderItem);

                                // Fetch the *latest* list of order items for the final order to
                                // correctly check `allItemsComplete`
                                List<OrderItem> latestOrderItemsForFinalOrder =
                                        orderItemRepository.findByOrder(finalOrder);

                                // Check if all items in the order are now in a final state
                                boolean allItemsComplete =
                                        latestOrderItemsForFinalOrder.stream()
                                                .allMatch(
                                                        item ->
                                                                item.getStatus()
                                                                        == OrderItemStatus
                                                                                .COMPLETE);

                                if (allItemsComplete) {
                                    finalOrder.setStatus(OrderStatus.COMPLETED);
                                    orderRepository.save(finalOrder); // Use the re-fetched order
                                    log.info(
                                            "Order {} is now fully COMPLETED as all items are"
                                                    + " processed.",
                                            finalOrder.getId());
                                } else {
                                    log.info(
                                            "Order {} has more items to process.",
                                            finalOrder.getId());
                                }
                            } else {
                                // Update order item status from API response
                                finalOrderItem.setStatus(
                                        OrderItemStatus.valueOf(apiResponse.getProductStatus()));
                                orderItemRepository.save(finalOrderItem);
                            }
                        })
                .exceptionally(
                        ex -> {
                            log.error(
                                    "Error processing order item ID {} for order ID {}",
                                    orderItem.getId(),
                                    orderItem.getOrder().getId(),
                                    ex);
                            return null;
                        });
    }
}
