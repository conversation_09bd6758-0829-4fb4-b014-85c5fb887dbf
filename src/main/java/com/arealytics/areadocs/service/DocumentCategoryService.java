package com.arealytics.areadocs.service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.areadocs.domain.DocumentCategory;
import com.arealytics.areadocs.dto.requestDTO.DocumentCategoryFilterDTO;
import com.arealytics.areadocs.dto.responseDTO.DocumentCategoryDTO;
import com.arealytics.areadocs.exception.DocumentCategoryNotFoundException;
import com.arealytics.areadocs.mapper.DocumentCategoryMapper;
import com.arealytics.areadocs.repository.DocumentCategoryRepository;

@Service
public class DocumentCategoryService {
    private LocalDateTime createdAt;
    private LocalDateTime modifiedAt;

    private final DocumentCategoryRepository documentCategoryRepository;
    private final DocumentCategoryMapper documentCategoryMapper;

    @Autowired
    public DocumentCategoryService(
            DocumentCategoryRepository documentCategoryRepository,
            DocumentCategoryMapper documentCategoryMapper) {
        this.documentCategoryRepository = documentCategoryRepository;
        this.documentCategoryMapper = documentCategoryMapper;
    }

    public List<DocumentCategoryDTO> getAllDocumentCategories() {
        return documentCategoryRepository.findAll().stream()
                .map(documentCategoryMapper::toDTO)
                .collect(Collectors.toList());
    }

    public DocumentCategoryDTO getDocumentCategoryById(Long id) {
        DocumentCategory documentCategory =
                documentCategoryRepository
                        .findById(id)
                        .orElseThrow(
                                () -> new DocumentCategoryNotFoundException("Category not found"));
        return documentCategoryMapper.toDTO(documentCategory);
    }

    @Transactional
    public DocumentCategoryDTO updateDocumentCategory(Long id, DocumentCategoryDTO dto) {
        DocumentCategory existing =
                documentCategoryRepository
                        .findById(id)
                        .orElseThrow(
                                () -> new DocumentCategoryNotFoundException("Category not found"));

        existing.setName(dto.getName());
        existing.setDescription(dto.getDescription());
        existing.setModifiedAt(Instant.now());

        resolveAndSetParent(
                existing, dto.getParentCategory() != null ? dto.getParentCategory().getId() : null);

        DocumentCategory saved = documentCategoryRepository.save(existing);
        return documentCategoryMapper.toDTO(saved);
    }

    @Transactional
    public DocumentCategoryDTO createDocumentCategory(DocumentCategoryDTO dto) {
        DocumentCategory entity = documentCategoryMapper.toEntity(dto);
        entity.setCreatedAt(Instant.now());
        resolveAndSetParent(
                entity, dto.getParentCategory() != null ? dto.getParentCategory().getId() : null);

        DocumentCategory saved = documentCategoryRepository.save(entity);
        return documentCategoryMapper.toDTO(saved);
    }

    @Transactional
    public void deleteDocumentCategory(Long id) {
        if (!documentCategoryRepository.existsById(id)) {
            throw new DocumentCategoryNotFoundException("Category not found");
        }
        documentCategoryRepository.deleteById(id);
    }

    private void resolveAndSetParent(DocumentCategory entity, Long parentCategoryId) {
        if (parentCategoryId != null && parentCategoryId > 0) {
            DocumentCategory parent =
                    documentCategoryRepository
                            .findById(parentCategoryId)
                            .orElseThrow(
                                    () ->
                                            new DocumentCategoryNotFoundException(
                                                    "Parent category not found"));
            entity.setParentCategory(parent);
        } else {
            entity.setParentCategory(null);
        }
    }

    /**
     * Get companies with filters and pagination
     *
     * @param filter The filter criteria
     * @param pageable The pagination information
     * @return Page of company DTOs
     */
    @Transactional(readOnly = true)
    public Page<DocumentCategoryDTO> getDocumentCategoriesWithFilters(
            DocumentCategoryFilterDTO filter, Pageable pageable) {
        Specification<DocumentCategory> spec = Specification.where(null);

        if (filter.getName() != null && !filter.getName().isEmpty()) {
            spec =
                    spec.and(
                            (root, query, cb) ->
                                    cb.like(
                                            cb.lower(root.get("name")),
                                            "%" + filter.getName().toLowerCase() + "%"));
        }

        if (filter.getDescription() != null && !filter.getDescription().isEmpty()) {
            spec =
                    spec.and(
                            (root, query, cb) ->
                                    cb.like(
                                            cb.lower(root.get("description")),
                                            "%" + filter.getDescription().toLowerCase() + "%"));
        }

        if (filter.getParentCategoryId() != null) {
            spec =
                    spec.and(
                            (root, query, cb) ->
                                    cb.equal(
                                            root.get("parentCategory").get("id"),
                                            filter.getParentCategoryId()));
        }

        return documentCategoryRepository
                .findAll(spec, pageable)
                .map(documentCategoryMapper::toDTO);
    }
}
