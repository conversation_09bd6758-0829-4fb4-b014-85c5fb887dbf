package com.arealytics.areadocs.service;

import com.arealytics.areadocs.domain.Permission;
import com.arealytics.areadocs.dto.requestDTO.PermissionFilterDTO;
import com.arealytics.areadocs.dto.requestDTO.PermissionRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.PermissionDTO;
import com.arealytics.areadocs.exception.PermissionException;
import com.arealytics.areadocs.mapper.PermissionMapper;
import com.arealytics.areadocs.repository.PermissionRepository;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PermissionService {

    private final PermissionRepository permissionRepository;
    private final PermissionMapper permissionMapper;

    /**
     * Create a new permission
     *
     * @param permissionRequestDTO The permission to create
     * @return The created permission DTO
     */
    @Transactional
    public PermissionDTO createPermission(PermissionRequestDTO permissionRequestDTO) {
        if (permissionRepository.existsByPermissionCode(permissionRequestDTO.getPermissionCode())) {
            throw new PermissionException.DuplicatePermissionCodeException(
                    "Permission code already exists: " + permissionRequestDTO.getPermissionCode());
        }

        Permission permission = permissionMapper.toEntity(permissionRequestDTO);
        Permission savedPermission = permissionRepository.save(permission);
        return permissionMapper.toDto(savedPermission);
    }

    /**
     * Get all permissions
     *
     * @return List of all permission DTOs
     */
    @Transactional(readOnly = true)
    public List<PermissionDTO> getAllPermissions() {
        return permissionRepository.findAll().stream()
                .map(permissionMapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Get a permission by ID
     *
     * @param id The permission ID
     * @return The permission DTO
     */
    @Transactional(readOnly = true)
    public PermissionDTO getPermissionById(Long id) {
        Permission permission = permissionRepository.findById(id)
                .orElseThrow(() -> new PermissionException.PermissionNotFoundException("Permission not found with id: " + id));
        return permissionMapper.toDto(permission);
    }

    /**
     * Get a permission by code
     *
     * @param code The permission code
     * @return The permission DTO
     */
    @Transactional(readOnly = true)
    public PermissionDTO getPermissionByCode(String code) {
        Permission permission = permissionRepository.findByPermissionCode(code)
                .orElseThrow(() -> new PermissionException.PermissionNotFoundException("Permission not found with code: " + code));
        return permissionMapper.toDto(permission);
    }

    /**
     * Update a permission
     *
     * @param id The permission ID
     * @param permissionRequestDTO The updated permission data
     * @return The updated permission DTO
     */
    @Transactional
    public PermissionDTO updatePermission(Long id, PermissionRequestDTO permissionRequestDTO) {
        Permission existingPermission = permissionRepository.findById(id)
                .orElseThrow(() -> new PermissionException.PermissionNotFoundException("Permission not found with id: " + id));

        // Check if permission code is being changed and if it already exists
        if (!existingPermission.getPermissionCode().equals(permissionRequestDTO.getPermissionCode()) &&
                permissionRepository.existsByPermissionCode(permissionRequestDTO.getPermissionCode())) {
            throw new PermissionException.DuplicatePermissionCodeException(
                    "Permission code already exists: " + permissionRequestDTO.getPermissionCode());
        }

        // Update the entity with DTO values
        permissionMapper.updateEntityFromDto(permissionRequestDTO, existingPermission);

        Permission savedPermission = permissionRepository.save(existingPermission);
        return permissionMapper.toDto(savedPermission);
    }

    /**
     * Delete a permission
     *
     * @param id The permission ID
     */
    @Transactional
    public void deletePermission(Long id) {
        if (!permissionRepository.existsById(id)) {
            throw new PermissionException.PermissionNotFoundException("Permission not found with id: " + id);
        }

        permissionRepository.deleteById(id);
    }

    /**
     * Get permissions with filters and pagination
     *
     * @param filter The filter criteria
     * @param pageable The pagination information
     * @return Page of permission DTOs
     */
    @Transactional(readOnly = true)
    public Page<PermissionDTO> getPermissionsWithFilters(PermissionFilterDTO filter, Pageable pageable) {
        Specification<Permission> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (filter != null) {
                if (StringUtils.hasText(filter.getPermissionCode())) {
                    predicates.add(criteriaBuilder.like(
                            criteriaBuilder.lower(root.get("permissionCode")),
                            "%" + filter.getPermissionCode().toLowerCase() + "%"));
                }

                if (StringUtils.hasText(filter.getPermissionName())) {
                    predicates.add(criteriaBuilder.like(
                            criteriaBuilder.lower(root.get("permissionName")),
                            "%" + filter.getPermissionName().toLowerCase() + "%"));
                }
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        return permissionRepository.findAll(spec, pageable).map(permissionMapper::toDto);
    }

    /**
     * Get permission by ID as Optional
     *
     * @param id The permission ID
     * @return Optional of permission DTO
     */
    @Transactional(readOnly = true)
    public Optional<PermissionDTO> findPermissionById(Long id) {
        return permissionRepository.findById(id).map(permissionMapper::toDto);
    }
}
