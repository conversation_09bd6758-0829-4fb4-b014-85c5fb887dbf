package com.arealytics.areadocs.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Optional;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.areadocs.domain.CompanySpecialPrice;
import com.arealytics.areadocs.domain.DocumentPrice;
import com.arealytics.areadocs.dto.responseDTO.serv.ProductsResponse;
import com.arealytics.areadocs.repository.CompanySpecialPriceRepository;
import com.arealytics.areadocs.repository.DocumentPriceRepository;
import com.arealytics.areadocs.util.AuthUtils;
import com.arealytics.areadocs.util.DateUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Service responsible for calculating and managing document pricing including special prices and GST calculations.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DocumentPricingService {

    // Default values
    private static final int PRICE_SCALE = 2;

    private final DocumentPriceRepository documentPriceRepository;
    private final CompanySpecialPriceRepository companySpecialPriceRepository;
    private final AuthUtils authUtils;
    private final MockServApiService mockServApiService;

    /**
     * Processes product data from the API and calculates final prices based on business rules
     *
     * @param apiResponse The raw product response from the Serv API
     * @param productCode Optional product code to process a specific product only
     * @return ProductsResponse with all pricing calculations applied
     */
    @Transactional
    public ProductsResponse addPlatformPricesToProducts(
            ProductsResponse apiResponse, String productCode) {
        if (apiResponse == null || apiResponse.getProducts() == null) {
            return new ProductsResponse();
        }

        // Apply pricing logic to products
        if (productCode != null && !productCode.isEmpty()) {
            // Process specific product if productCode is provided
            apiResponse.getProducts().stream()
                    .filter(product -> product.getProductCode().equals(productCode))
                    .forEach(this::calculateAndUpdateProductPrice);
        } else {
            // Process all products if no productCode specified
            apiResponse.getProducts().forEach(this::calculateAndUpdateProductPrice);
        }

        return apiResponse;
    }

    /**
     * Applies company-specific special pricing if available for the product code
     *
     * @param productCode Product code identifier
     * @param basePrice Original base price
     * @param effectiveBasePrice Effective base price from DocumentPrice
     * @return Price after applying special pricing or effectiveBasePrice/basePrice if no special pricing applies
     */
    public BigDecimal getCompanySpecialPricingForProduct(String productCode, BigDecimal basePrice, BigDecimal effectiveBasePrice) {
        Long companyId = authUtils.getCurrentUserCompanyId();
        if (companyId == null) {
            return effectiveBasePrice != null ? effectiveBasePrice : basePrice;
        }

        LocalDateTime currentDateTime = LocalDateTime.now();

        // Find the DocumentPrice for the productCode
        Optional<DocumentPrice> documentPriceOpt = documentPriceRepository
                .findFirstByProductCode(productCode)
                .filter(price -> Boolean.TRUE.equals(price.getIsActive())
                        && DateUtils.isWithinEffectivePeriod(
                                currentDateTime, price.getEffectiveDate(), price.getExpiryDate()));

        if (documentPriceOpt.isEmpty()) {
            return effectiveBasePrice != null ? effectiveBasePrice : basePrice;
        }

        DocumentPrice documentPrice = documentPriceOpt.get();

        // Find applicable company-specific special price
        Optional<CompanySpecialPrice> specialPricing = companySpecialPriceRepository
                .findFirstByCompanyIdAndDocumentPriceId(companyId, documentPrice.getId())
                .filter(sp -> Boolean.TRUE.equals(sp.getIsActive())
                        && DateUtils.isWithinEffectivePeriod(
                                currentDateTime, documentPrice.getEffectiveDate(), documentPrice.getExpiryDate()));

        // Return specialPrice if available, otherwise effectiveBasePrice or basePrice
        return specialPricing.map(CompanySpecialPrice::getSpecialPrice)
                .orElse(effectiveBasePrice != null ? effectiveBasePrice : basePrice);
    }

    /**
     * Calculates and updates the price of a product, applying business rules for pricing
     *
     * @param product Product to update with calculated pricing
     */
    @Transactional
    private void calculateAndUpdateProductPrice(ProductsResponse.Product product) {
        if (product == null || product.getProductInformation() == null) {
            return;
        }

        String productCode = product.getProductCode();

        // Fetch the active DocumentPrice for the productCode
        Optional<DocumentPrice> documentPriceOpt = documentPriceRepository
                .findFirstByProductCode(productCode)
                .filter(price -> Boolean.TRUE.equals(price.getIsActive())
                        && DateUtils.isWithinEffectivePeriod(
                                LocalDateTime.now(), price.getEffectiveDate(), price.getExpiryDate()));

        if (documentPriceOpt.isEmpty()) {
            return;
        }

        DocumentPrice documentPrice = documentPriceOpt.get();
        BigDecimal basePrice = documentPrice.getBasePrice();
        BigDecimal effectiveBasePrice = documentPrice.getEffectiveBasePrice();
        BigDecimal effectivePriceGst = documentPrice.getEffectivePriceGst();

        // Get company-specific special pricing if available
        BigDecimal finalBasePrice = getCompanySpecialPricingForProduct(productCode, basePrice, effectiveBasePrice);

        // Determine GST: use specialPriceGst if specialPrice exists, otherwise use effectivePriceGst
        Long companyId = authUtils.getCurrentUserCompanyId();
        BigDecimal gstAmount;
        if (companyId != null) {
            Optional<CompanySpecialPrice> specialPricing = companySpecialPriceRepository
                    .findFirstByCompanyIdAndDocumentPriceId(companyId, documentPrice.getId())
                    .filter(sp -> Boolean.TRUE.equals(sp.getIsActive())
                            && DateUtils.isWithinEffectivePeriod(
                                    LocalDateTime.now(), documentPrice.getEffectiveDate(), documentPrice.getExpiryDate()));
            gstAmount = specialPricing.map(CompanySpecialPrice::getSpecialPriceGst)
                    .orElse(effectivePriceGst);
        } else {
            gstAmount = effectivePriceGst;
        }

        // Calculate final total price including GST
        BigDecimal finalTotalPrice = finalBasePrice.add(gstAmount);

        // Update product with new calculated price and GST
        product.setPrice(finalTotalPrice.setScale(PRICE_SCALE, RoundingMode.HALF_UP));
        product.setGst(gstAmount.setScale(PRICE_SCALE, RoundingMode.HALF_UP));
    }
}
