package com.arealytics.areadocs.service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.areadocs.domain.Company;
import com.arealytics.areadocs.domain.CompanyMembership;
import com.arealytics.areadocs.domain.Role;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.dto.requestDTO.CompanyMembershipRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.CompanyMembershipDTO;
import com.arealytics.areadocs.exception.CompanyException;
import com.arealytics.areadocs.mapper.CompanyMembershipMapper;
import com.arealytics.areadocs.repository.CompanyMembershipRepository;
import com.arealytics.areadocs.repository.CompanyRepository;
import com.arealytics.areadocs.repository.RoleRepository;
import com.arealytics.areadocs.repository.UserRepository;

import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class CompanyMembershipService {

    private final CompanyMembershipRepository companyMembershipRepository;
    private final UserRepository userRepository;
    private final CompanyRepository companyRepository;
    private final RoleRepository roleRepository;
    private final CompanyMembershipMapper companyMembershipMapper;

    /**
     * Create a new company membership
     *
     * @param requestDTO The company membership request data
     * @return The created company membership DTO
     */
    @Transactional
    public CompanyMembershipDTO createCompanyMembership(CompanyMembershipRequestDTO requestDTO) {
        User user =
                userRepository
                        .findById(requestDTO.getUserId())
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "User not found with id: "
                                                        + requestDTO.getUserId()));

        Company company =
                companyRepository
                        .findById(requestDTO.getCompanyId())
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "Company not found with id: "
                                                        + requestDTO.getCompanyId()));

        if (companyMembershipRepository
                .findByUserIdAndCompanyId(user.getId(), company.getId())
                .isPresent()) {
            throw new CompanyException("User is already a member of this company");
        }

        // Find the role by name
        Role role =
                roleRepository
                        .findById(requestDTO.getRoleId())
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "Role not found with name: "
                                                        + requestDTO.getRoleId()));

        CompanyMembership companyMembership = companyMembershipMapper.toEntity(requestDTO);
        companyMembership.setUser(user);
        companyMembership.setCompany(company);
        companyMembership.setRole(role);
        companyMembership.setIsActive(true); // Explicitly set isActive to true

        CompanyMembership savedCompanyMembership =
                companyMembershipRepository.save(companyMembership);
        return companyMembershipMapper.toDto(savedCompanyMembership);
    }

    /**
     * Create a new company membership (overloaded method for backward compatibility)
     *
     * @param companyMembershipDTO The company membership DTO
     * @return The created company membership DTO
     */
    @Transactional
    public CompanyMembershipDTO createCompanyMembership(CompanyMembershipDTO companyMembershipDTO) {
        // Convert to request DTO to ensure audit fields are ignored
        CompanyMembershipRequestDTO requestDTO = new CompanyMembershipRequestDTO();
        requestDTO.setUserId(companyMembershipDTO.getUserId());
        requestDTO.setCompanyId(companyMembershipDTO.getCompanyId());
        requestDTO.setRoleId(companyMembershipDTO.getRoleId());

        return createCompanyMembership(requestDTO);
    }

    /**
     * Get a company membership by ID
     *
     * @param id The company membership ID
     * @return The company membership DTO
     */
    @Transactional(readOnly = true)
    public CompanyMembershipDTO getCompanyMembershipById(Long id) {
        CompanyMembership companyMembership =
                companyMembershipRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "Company membership not found with id: " + id));
        return companyMembershipMapper.toDto(companyMembership);
    }

    /**
     * Get company memberships by company ID
     *
     * @param companyId The company ID
     * @return List of company membership DTOs
     */
    @Transactional(readOnly = true)
    public List<CompanyMembershipDTO> getCompanyMembershipsByCompanyId(Long companyId) {
        return companyMembershipRepository.findByCompanyId(companyId).stream()
                .map(companyMembershipMapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Get all company memberships
     *
     * @return List of all company membership DTOs
     */
    @Transactional(readOnly = true)
    public List<CompanyMembershipDTO> getAllCompanyMemberships() {
        return companyMembershipRepository.findAll().stream()
                .map(companyMembershipMapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Get company memberships by user ID
     *
     * @param userId The user ID
     * @return List of company membership DTOs
     */
    @Transactional(readOnly = true)
    public CompanyMembershipDTO getCompanyMembershipsByUserId(Long userId) {
        return companyMembershipMapper.toDto(companyMembershipRepository.findByUserId(userId));
    }

    /**
     * Update a company membership
     *
     * @param id The company membership ID
     * @param requestDTO The updated company membership data
     * @return The updated company membership DTO
     */
    @Transactional
    public CompanyMembershipDTO updateCompanyMembership(
            Long id, CompanyMembershipRequestDTO requestDTO) {
        CompanyMembership existingMembership =
                companyMembershipRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "Company membership not found with id: " + id));

        // Ensure we're not changing the user or company
        if (!existingMembership.getUser().getId().equals(requestDTO.getUserId())
                || !existingMembership.getCompany().getId().equals(requestDTO.getCompanyId())) {
            throw new CompanyException(
                    "Cannot change the user or company association of a membership");
        }

        // Find the role by name
        Role role =
                roleRepository
                        .findById(requestDTO.getRoleId())
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "Role not found with name: "
                                                        + requestDTO.getRoleId()));

        existingMembership.setRole(role);

        CompanyMembership savedMembership = companyMembershipRepository.save(existingMembership);
        return companyMembershipMapper.toDto(savedMembership);
    }

    /**
     * Delete a company membership
     *
     * @param id The company membership ID
     */
    @Transactional
    public void deleteCompanyMembership(Long id) {
        if (!companyMembershipRepository.existsById(id)) {
            throw new EntityNotFoundException("Company membership not found with id: " + id);
        }

        companyMembershipRepository.deleteById(id);
    }

    // Add this batch method
    @Transactional(readOnly = true)
    public Map<Long, CompanyMembershipDTO> getCompanyMembershipsByUserIds(List<Long> userIds) {
        if (userIds.isEmpty()) {
            return Collections.emptyMap();
        }

        return companyMembershipRepository.findByUserIdIn(userIds).stream()
                .filter(CompanyMembership::getIsActive) // Filter active memberships
                .map(companyMembershipMapper::toDto)
                .peek(
                        dto ->
                                log.info(
                                        "Mapped CompanyMembershipDTO: userId={}, roleName={}",
                                        dto.getUserId(),
                                        dto.getRoleName()))
                .collect(
                        Collectors.toMap(
                                CompanyMembershipDTO::getUserId,
                                dto -> dto,
                                (existing, replacement) -> existing));
    }
}
