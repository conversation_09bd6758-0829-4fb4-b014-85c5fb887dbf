package com.arealytics.areadocs.service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.areadocs.domain.*;
import com.arealytics.areadocs.enumeration.*;
import com.arealytics.areadocs.exception.InsufficientBalanceException;
import com.arealytics.areadocs.exception.InvalidTransactionException;
import com.arealytics.areadocs.repository.*;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Financial transaction service implementing double-entry accounting principles. Handles all money
 * movements between accounts including purchases, transfers, and wallet recharges.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class TransactionService {

    private final AccountService accountService;
    private final AccountEntryRepository accountEntryRepository;
    private final AccountEntryDetailRepository accountEntryDetailRepository;
    private final PaymentTransactionRepository paymentTransactionRepository;
    private final TransactionRepository transactionRepository;

    private static final String TRANSACTION_ERROR_MESSAGE =
            "System error occurred. Please try again later.";
    private static final String UNEXPECTED_ERROR_MESSAGE =
            "An unexpected error occurred. Please contact support.";

    /**
     * Retrieves all transactions for a specific user.
     *
     * @param userId The ID of the user
     * @return List of transactions for the user
     */
    public List<Transaction> getTransactionsByUserId(Long userId) {
        return transactionRepository.findByUserId(userId);
    }
    public List<Transaction> getTransactionsByCompanyId(Long companyId) {
        return transactionRepository.findByAccountCompanyId(companyId);
    }

    /**
     * Processes a debit transaction implementing double-entry accounting. Debit source account and
     * credits destination account.
     *
     * @param sourceAccount The account to debit (take money from)
     * @param destinationAccount The account to credit (add money to)
     * @param amount The amount to transfer
     * @param entryType The type of entry (PAYMENT, FEE, etc.)
     * @param description A description of the transaction
     * @param user The user initiating the transaction
     * @param order The order associated with the transaction (can be null)
     * @return A TransactionResult containing the transaction details and status
     */
    @Transactional(
            propagation = Propagation.REQUIRED,
            isolation = Isolation.REPEATABLE_READ,
            rollbackFor = {Exception.class})
    public TransactionResult transferFunds(
            Account sourceAccount,
            Account destinationAccount,
            BigDecimal amount,
            EntryType entryType,
            String description,
            User user,
            Order order) {

        log.info(
                "Processing debit of {} from account ID: {} to account ID: {}",
                amount,
                sourceAccount.getId(),
                destinationAccount.getId());

        try {
            // Validate transaction parameters
            validateTransaction(sourceAccount, amount);

            // Generate unique reference number
            String referenceNumber = generateReferenceNumber(entryType);

            // Execute the double-entry transaction
            return executeDoubleEntryTransaction(
                    sourceAccount,
                    destinationAccount,
                    amount,
                    entryType,
                    description,
                    user,
                    order,
                    referenceNumber,
                    TransactionType.PURCHASE,
                    false); // No payment transaction for standard debit

        } catch (InsufficientBalanceException | InvalidTransactionException e) {
            log.warn("Debit processing failed: {}", e.getMessage());
            return TransactionResult.builder().success(false).message(e.getMessage()).build();
        } catch (DataAccessException e) {
            log.error("Database error during debit processing", e);
            return TransactionResult.builder()
                    .success(false)
                    .message(TRANSACTION_ERROR_MESSAGE)
                    .build();
        } catch (Exception e) {
            log.error("Unexpected error during debit processing", e);
            return TransactionResult.builder()
                    .success(false)
                    .message(UNEXPECTED_ERROR_MESSAGE)
                    .build();
        }
    }

    /**
     * Processes a wallet recharge transaction. Credits user/company account from the platform
     * account.
     *
     * @param accountToRecharge The account to recharge (credit)
     * @param amount The amount to recharge
     * @param currency The currency of the amount
     * @param user The user initiating the recharge
     * @return A TransactionResult containing the transaction details and status
     */
    @Transactional(
            propagation = Propagation.REQUIRED,
            isolation = Isolation.REPEATABLE_READ,
            rollbackFor = {Exception.class})
    public TransactionResult rechargeAccount(
            Account accountToRecharge, BigDecimal amount, String currency, User user) {

        log.info(
                "Processing wallet recharge of {} for account ID: {}",
                amount,
                accountToRecharge.getId());

        try {
            // Validate amount is positive
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new InvalidTransactionException("Amount must be greater than zero");
            }

            // Get a platform account to debit
            Account platformAccount = accountService.getPlatformAccount();

            // Generate human-readable description
            String description =
                    createTransactionDescription(
                            user, accountToRecharge, amount, currency, "Wallet recharge");

            // Generate unique reference number
            String referenceNumber = generateReferenceNumber(EntryType.WALLET_RECHARGE);

            // Execute the double-entry transaction
            return executeDoubleEntryTransaction(
                    platformAccount,
                    accountToRecharge,
                    amount,
                    EntryType.WALLET_RECHARGE,
                    description,
                    user,
                    null, // No order associated with recharge
                    referenceNumber,
                    TransactionType.RECHARGE,
                    true); // Create payment transaction for recharge

        } catch (InvalidTransactionException e) {
            log.warn("Wallet recharge validation failed: {}", e.getMessage());
            return TransactionResult.builder().success(false).message(e.getMessage()).build();
        } catch (DataAccessException e) {
            log.error("Database error during wallet recharge", e);
            return TransactionResult.builder()
                    .success(false)
                    .message(TRANSACTION_ERROR_MESSAGE)
                    .build();
        } catch (Exception e) {
            log.error("Unexpected error during wallet recharge", e);
            return TransactionResult.builder()
                    .success(false)
                    .message(UNEXPECTED_ERROR_MESSAGE)
                    .build();
        }
    }

    /**
     * Executes a double-entry accounting transaction between two accounts. Core implementation that
     * handles both debits and credits in the system.
     *
     * @param sourceAccount The account to debit
     * @param destinationAccount The account to credit
     * @param amount The transaction amount
     * @param entryType The type of accounting entry
     * @param description The transaction description
     * @param user The user initiating the transaction
     * @param order The order associated with the transaction (can be null)
     * @param referenceNumber The transaction reference number
     * @param transactionType The type of transaction (PURCHASE, RECHARGE, etc.)
     * @param createPaymentTransaction Whether to create a payment transaction record
     * @return The transaction result with all created records
     */
    private TransactionResult executeDoubleEntryTransaction(
            Account sourceAccount,
            Account destinationAccount,
            BigDecimal amount,
            EntryType entryType,
            String description,
            User user,
            Order order,
            String referenceNumber,
            TransactionType transactionType,
            boolean createPaymentTransaction) {

        // Create the main accounting entry
        AccountEntry accountEntry =
                createAccountEntry(referenceNumber, description, entryType, user);

        // Create debit entry detail (source account)
        AccountEntryDetail debitEntry =
                createAccountEntryDetail(
                        accountEntry, sourceAccount, AccountTransactionType.DEBITED, amount);

        // Create credit entry detail (destination account)
        AccountEntryDetail creditEntry =
                createAccountEntryDetail(
                        accountEntry, destinationAccount, AccountTransactionType.CREDITED, amount);

        // Create payment transaction record if needed
        PaymentTransaction paymentTransaction = null;
        if (createPaymentTransaction) {
            paymentTransaction =
                    createPaymentTransaction(referenceNumber, amount, user, destinationAccount);
        }

        // Create consolidated transaction record
        Transaction transaction =
                createTransactionRecord(
                        transactionType,
                        user,
                        sourceAccount,
                        amount,
                        referenceNumber,
                        accountEntry,
                        paymentTransaction,
                        order);

        // Update account balances atomically (thanks to @Transactional)
        sourceAccount = accountService.updateBalance(sourceAccount, amount.negate());
        destinationAccount = accountService.updateBalance(destinationAccount, amount);

        // Build and return the success result
        return TransactionResult.builder()
                .success(true)
                .message(generateSuccessMessage(entryType))
                .accountEntry(accountEntry)
                .transaction(transaction)
                .paymentTransaction(paymentTransaction)
                .sourceAccount(sourceAccount)
                .destinationAccount(destinationAccount)
                .amount(amount)
                .build();
    }

    /**
     * Generates an appropriate success message based on the entry type.
     *
     * @param entryType The type of entry
     * @return A user-friendly success message
     */
    private String generateSuccessMessage(EntryType entryType) {
        return switch (entryType) {
            case WALLET_RECHARGE -> "Wallet recharge processed successfully";
            case PURCHASE -> "Purchase processed successfully";
            case REFUND -> "Refund processed successfully";
            case FEE -> "Fee processed successfully";
            case TRANSFER -> "Transfer processed successfully";
            default -> "Transaction processed successfully";
        };
    }

    /**
     * Validates a debit transaction and throws an exception if invalid.
     *
     * @param account The account to debit
     * @param amount The amount to debit
     * @throws InvalidTransactionException If the amount is invalid
     * @throws InsufficientBalanceException If the account has insufficient balance
     */
    private void validateTransaction(Account account, BigDecimal amount) {
        // Check if amount is positive
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new InvalidTransactionException("Amount must be greater than zero");
        }

        // Check if an account has sufficient balance
        if (account.getBalance().compareTo(amount) < 0) {
            throw new InsufficientBalanceException("Insufficient balance");
        }
    }

    /**
     * Generates a unique reference number for transaction traceability.
     *
     * @param entryType The type of entry
     * @return A prefixed unique reference number
     */
    private String generateReferenceNumber(EntryType entryType) {
        String prefix =
                switch (entryType) {
                    case PURCHASE -> "PMT";
                    case WALLET_RECHARGE -> "RCHRG";
                    case REFUND -> "RFND";
                    case FEE -> "FEE";
                    case TRANSFER -> "TRNSFR";
                    default -> "TXN";
                };

        return prefix + "-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }

    /**
     * Creates an account entry record in the database.
     *
     * @param referenceNumber The reference number
     * @param description The description
     * @param entryType The entry type
     * @param user The user creating the entry
     * @return The persisted account entry
     */
    private AccountEntry createAccountEntry(
            String referenceNumber, String description, EntryType entryType, User user) {

        AccountEntry accountEntry =
                AccountEntry.builder()
                        .entryDate(LocalDate.now())
                        .referenceNumber(referenceNumber)
                        .description(description)
                        .entryType(entryType)
                        .status(EntryStatus.POSTED)
                        .createdBy(user)
                        .build();

        return accountEntryRepository.save(accountEntry);
    }

    /**
     * Creates an account entry detail record for one side of the double-entry.
     *
     * @param accountEntry The parent account entry
     * @param account The account being debited or credited
     * @param accountTransactionType The transaction type (DEBITED or CREDITED)
     * @param amount The amount
     * @return The persisted account entry detail
     */
    private AccountEntryDetail createAccountEntryDetail(
            AccountEntry accountEntry,
            Account account,
            AccountTransactionType accountTransactionType,
            BigDecimal amount) {

        AccountEntryDetail detail =
                AccountEntryDetail.builder()
                        .accountEntry(accountEntry)
                        .account(account)
                        .accountTransactionType(accountTransactionType)
                        .amount(amount)
                        .build();

        return accountEntryDetailRepository.save(detail);
    }

    /**
     * Creates a payment transaction record for wallet recharges and external payments.
     *
     * @param referenceNumber The reference number
     * @param amount The payment amount
     * @param user The user making the payment
     * @param account The account receiving the payment
     * @return The persisted payment transaction
     */
    private PaymentTransaction createPaymentTransaction(
            String referenceNumber, BigDecimal amount, User user, Account account) {

        // Generate unique identifiers for order and transaction
        String orderId = "ORDER-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
        String transactionId = "TXN-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();

        PaymentTransaction paymentTransaction =
                PaymentTransaction.builder()
                        .orderId(orderId)
                        .transactionId(transactionId)
                        .amount(amount)
                        .status(PaymentStatus.COMPLETED)
                        .transactionTimestamp(LocalDateTime.now())
                        .paymentMethod("Wallet")
                        .paymentGateway("Internal")
                        .gatewayReference(referenceNumber)
                        .gatewayStatus("SUCCESS")
                        .user(user)
                        .company(account.getCompany())
                        .build();

        return paymentTransactionRepository.save(paymentTransaction);
    }

    /**
     * Creates a consolidated transaction record linking all components.
     *
     * @param transactionType The transaction type
     * @param user The user
     * @param account The primary account involved
     * @param amount The transaction amount
     * @param referenceNumber The reference number
     * @param accountEntry The linked account entry
     * @param paymentTransaction The linked payment transaction (if any)
     * @param order The linked order (if any)
     * @return The persisted transaction record
     */
    private Transaction createTransactionRecord(
            TransactionType transactionType,
            User user,
            Account account,
            BigDecimal amount,
            String referenceNumber,
            AccountEntry accountEntry,
            PaymentTransaction paymentTransaction,
            Order order) {

        Transaction transaction =
                Transaction.builder()
                        .transactionType(transactionType)
                        .transactionDate(LocalDateTime.now())
                        .user(user)
                        .account(account)
                        .amount(amount)
                        .status(TransactionStatus.COMPLETED)
                        .referenceId(referenceNumber)
                        .accountEntry(accountEntry)
                        .paymentTransaction(paymentTransaction)
                        .order(order)
                        .build();

        return transactionRepository.save(transaction);
    }

    /**
     * Creates a detailed, human-readable transaction description.
     *
     * @param user The user performing the transaction
     * @param account The account being debited/credited
     * @param amount The transaction amount
     * @param currency The currency code
     * @param purpose The transaction purpose
     * @return A formatted description string
     */
    public String createTransactionDescription(
            User user, Account account, BigDecimal amount, String currency, String purpose) {

        String accountOwner;
        if (account.getCompany() != null) {
            // Company account
            accountOwner = "company " + account.getCompany().getName();
        } else {
            // Personal account
            accountOwner = "user " + user.getFirstName() + " " + user.getLastName();
        }

        return String.format(
                "%s of %s %s for %s on %s",
                purpose, amount, currency, accountOwner, LocalDate.now());
    }

    /**
     * Result container for financial transactions. Holds all information about a completed
     * transaction.
     */
    @lombok.Builder
    @lombok.Data
    public static class TransactionResult {
        private boolean success;
        private String message;
        private AccountEntry accountEntry;
        private Transaction transaction;
        private PaymentTransaction paymentTransaction;
        private Account sourceAccount;
        private Account destinationAccount;
        private BigDecimal amount;
    }

    @Transactional(readOnly = true)
    public Transaction getTransactionById(Long transactionId) {
        return transactionRepository
                .findById(transactionId)
                .orElseThrow(
                        () ->
                                new InvalidTransactionException(
                                        "Transaction not found with ID: " + transactionId));
    }
}
