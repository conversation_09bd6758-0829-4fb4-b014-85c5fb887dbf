package com.arealytics.areadocs.service;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.areadocs.domain.Company;
import com.arealytics.areadocs.domain.OrderItem;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.dto.requestDTO.serv.CreateOrderRequest;
import com.arealytics.areadocs.util.OrderRequestConstants;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class OrderRequestMapperService {

    /**
     * Converts a frontend order request to a full CreateOrderRequest.
     *
     * @param orderItems the request from the frontend
     * @param user the user placing the order
     * @return a complete CreateOrderRequest with all required fields
     */
    @Transactional(readOnly = true)
    public CreateOrderRequest buildServOrderRequestFromItems(
            List<OrderItem> orderItems, User user, Company company) {
        log.info("Converting frontend request to full request for user ID: {}", user.getId());
        // Replace problematic logging with safer versions
        log.debug("Order items count: {}", orderItems != null ? orderItems.size() : 0);
        log.debug("User ID: {}", user.getId());

        // check if company is null
        String userCompanyName = company != null ? company.getName() : "N/A";

        CreateOrderRequest fullRequest = new CreateOrderRequest();

        // Convert order items to products
        if (orderItems != null && !orderItems.isEmpty()) {
            List<CreateOrderRequest.Product> products =
                    orderItems.stream()
                            .map(
                                    orderItem ->
                                            mapOrderItemToProduct(orderItem, user, userCompanyName))
                            .collect(Collectors.toList());

            fullRequest.setProducts(products);
        }

        // Set request-level information
        setRequestLevelInformation(fullRequest, user);

        return fullRequest;
    }

    /**
     * Maps an OrderItem to a CreateOrderRequest.Product.
     *
     * @param orderItem the order item to map
     * @param user the user placing the order
     * @param userCompanyName the user's company name
     * @return mapped product for service request
     */
    private CreateOrderRequest.Product mapOrderItemToProduct(
            OrderItem orderItem, User user, String userCompanyName) {

        CreateOrderRequest.Product product = new CreateOrderRequest.Product();

        // Copy basic product details
        product.setProductCode(orderItem.getProductCode());
        product.setWarningAcknowledged(orderItem.getIsWarningAcknowledged());

        // Map product IDs if available
        if (orderItem.getVolumeFolio() != null) {
            CreateOrderRequest.ProductIds productIds = new CreateOrderRequest.ProductIds();
            productIds.setTitleId(orderItem.getVolumeFolio());
            product.setProductIds(productIds);
        }

        // Create extra data
        CreateOrderRequest.ExtraData extraData = new CreateOrderRequest.ExtraData();

        // Set standard dummy values
        extraData.setSettlementDate(OrderRequestConstants.DUMMY_DATE);
        extraData.setMeterReadDate(OrderRequestConstants.DUMMY_DATE);
        extraData.setWaterShareId(OrderRequestConstants.DUMMY_WATER_SHARE_ID);
        extraData.setRatesRequestDate(OrderRequestConstants.DUMMY_DATE);
        extraData.setSalePrice(OrderRequestConstants.DUMMY_ZERO);
        extraData.setWisNumber(OrderRequestConstants.DUMMY_ZERO);
        extraData.setDeliverySpeed("STANDARD");
        extraData.setParcel(OrderRequestConstants.DUMMY_PARCEL);

        // Set land use application reason if available
        extraData.setLandUseApplicationReason(
                String.valueOf(orderItem.getLandUseApplicationReason()));

        // Populate postal address, vendors, purchasers, and owners corp manager data
        extraData.setPostalAddress(createPostalAddress(user));
        extraData.setVendors(createVendorsList());
        extraData.setPurchasers(createPurchasersList(user, userCompanyName));
        extraData.setOwnersCorpManagerData(createOwnersCorpManagerData());

        product.setExtraData(extraData);

        // Create prerequisites if needed
        if (orderItem.getVolumeFolio() != null) {
            CreateOrderRequest.Prerequisite prerequisite = new CreateOrderRequest.Prerequisite();
            prerequisite.setProductCode(orderItem.getProductCode());

            CreateOrderRequest.ProductIds prerequisiteIds = new CreateOrderRequest.ProductIds();
            prerequisiteIds.setTitleId(orderItem.getVolumeFolio());
            prerequisite.setProductIds(prerequisiteIds);

            product.setPrerequisites(List.of(prerequisite));
        }

        return product;
    }

    /**
     * Creates postal address for the order.
     *
     * @param user the user placing the order
     * @return populated postal address
     */
    private CreateOrderRequest.PostalAddress createPostalAddress(User user) {
        CreateOrderRequest.PostalAddress postalAddress = new CreateOrderRequest.PostalAddress();
        postalAddress.setRecipientName(user.getFirstName() + " " + user.getLastName());
        postalAddress.setContactNumber(user.getContactNumber());
        postalAddress.setEmail(user.getEmail());
        postalAddress.setState("VIC"); // Default state
        postalAddress.setPostcode(OrderRequestConstants.DUMMY_ZERO);

        return postalAddress;
    }

    /**
     * Creates vendors list for the order.
     *
     * @return list of vendors
     */
    private List<CreateOrderRequest.Vendor> createVendorsList() {
        CreateOrderRequest.Vendor vendor = new CreateOrderRequest.Vendor();
        vendor.setVendorType("ORGANISATION");
        vendor.setFirstName(OrderRequestConstants.DUMMY_COMPANY_NAME);
        vendor.setMiddleName(OrderRequestConstants.DUMMY_COMPANY_NAME);
        vendor.setLastName(OrderRequestConstants.DUMMY_COMPANY_NAME);
        vendor.setCompanyName(OrderRequestConstants.DUMMY_COMPANY_NAME);

        return List.of(vendor);
    }

    /**
     * Creates purchasers list for the order.
     *
     * @param user the user placing the order
     * @param userCompanyName the user's company name
     * @return list of purchasers
     */
    private List<CreateOrderRequest.Purchaser> createPurchasersList(
            User user, String userCompanyName) {

        CreateOrderRequest.Purchaser purchaser = new CreateOrderRequest.Purchaser();
        purchaser.setPurchaserType(String.valueOf(user.getUserType()));
        purchaser.setFirstName(user.getFirstName());
        purchaser.setLastName(user.getLastName());
        purchaser.setCompanyName(userCompanyName);

        return List.of(purchaser);
    }

    /**
     * Creates owners corp manager data.
     *
     * @return owners corp manager data
     */
    private CreateOrderRequest.OwnersCorpManagerData createOwnersCorpManagerData() {
        CreateOrderRequest.OwnersCorpManagerData ownersCorpManagerData =
                new CreateOrderRequest.OwnersCorpManagerData();
        ownersCorpManagerData.setManagerId(OrderRequestConstants.DUMMY_ZERO);

        return ownersCorpManagerData;
    }

    /**
     * Sets request-level information on the full request.
     *
     * @param fullRequest the request to populate
     * @param user the user placing the order
     */
    private void setRequestLevelInformation(CreateOrderRequest fullRequest, User user) {
        fullRequest.setDeliveryPreference("NONE");
        fullRequest.setEmail(user.getEmail());
        fullRequest.setContactName(user.getFirstName() + " " + user.getLastName());
        fullRequest.setContactNumber(user.getContactNumber());
    }
}
