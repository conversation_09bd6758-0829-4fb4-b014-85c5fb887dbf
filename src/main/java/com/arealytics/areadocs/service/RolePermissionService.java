package com.arealytics.areadocs.service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.areadocs.domain.Permission;
import com.arealytics.areadocs.domain.Role;
import com.arealytics.areadocs.domain.RolePermissionMapping;
import com.arealytics.areadocs.dto.requestDTO.RolePermissionRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.RolePermissionDTO;
import com.arealytics.areadocs.exception.PermissionException;
import com.arealytics.areadocs.exception.RoleException;
import com.arealytics.areadocs.mapper.RolePermissionMapper;
import com.arealytics.areadocs.repository.PermissionRepository;
import com.arealytics.areadocs.repository.RolePermissionMappingRepository;
import com.arealytics.areadocs.repository.RoleRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/** Service for managing role permissions. */
@Service
@RequiredArgsConstructor
@Slf4j
public class RolePermissionService {

    private final RolePermissionMappingRepository rolePermissionMappingRepository;
    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository;
    private final RolePermissionMapper rolePermissionMapper;

    /**
     * Assign a permission to a role
     *
     * @param rolePermissionRequestDTO The role permission assignment request
     * @return The created role permission DTO
     */
    @Transactional
    public RolePermissionDTO assignPermissionToRole(
            RolePermissionRequestDTO rolePermissionRequestDTO) {
        log.info(
                "Assigning permission {} to role {}",
                rolePermissionRequestDTO.getPermissionId(),
                rolePermissionRequestDTO.getRoleId());

        // Check if role exists
        Role role =
                roleRepository
                        .findById(rolePermissionRequestDTO.getRoleId())
                        .orElseThrow(
                                () ->
                                        new RoleException.RoleNotFoundException(
                                                "Role not found with id: "
                                                        + rolePermissionRequestDTO.getRoleId()));

        // Check if permission exists
        Permission permission =
                permissionRepository
                        .findById(rolePermissionRequestDTO.getPermissionId())
                        .orElseThrow(
                                () ->
                                        new PermissionException.PermissionNotFoundException(
                                                "Permission not found with id: "
                                                        + rolePermissionRequestDTO
                                                                .getPermissionId()));

        // Check if the role already has this permission
        if (rolePermissionMappingRepository.existsByRoleIdAndPermissionId(
                role.getId(), permission.getId())) {
            throw new IllegalStateException("Role already has this permission assigned");
        }

        // Create and save the role permission mapping
        RolePermissionMapping rolePermissionMapping = new RolePermissionMapping();
        rolePermissionMapping.setRole(role);
        rolePermissionMapping.setPermission(permission);

        RolePermissionMapping savedMapping =
                rolePermissionMappingRepository.save(rolePermissionMapping);
        log.info(
                "Permission {} assigned to role {} successfully",
                permission.getPermissionCode(),
                role.getName());

        return rolePermissionMapper.toDto(savedMapping);
    }

    /**
     * Get all permissions assigned to a role
     *
     * @param roleId The role ID
     * @return List of role permission DTOs
     */
    @Transactional(readOnly = true)
    public List<RolePermissionDTO> getRolePermissions(Long roleId) {
        // Check if role exists
        if (!roleRepository.existsById(roleId)) {
            throw new RoleException.RoleNotFoundException("Role not found with id: " + roleId);
        }

        return rolePermissionMappingRepository.findByRoleId(roleId).stream()
                .map(rolePermissionMapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Get all roles assigned to a permission
     *
     * @param permissionId The permission ID
     * @return List of role permission DTOs
     */
    @Transactional(readOnly = true)
    public List<RolePermissionDTO> getPermissionRoles(Long permissionId) {
        // Check if permission exists
        if (!permissionRepository.existsById(permissionId)) {
            throw new PermissionException.PermissionNotFoundException(
                    "Permission not found with id: " + permissionId);
        }

        return rolePermissionMappingRepository.findByPermissionId(permissionId).stream()
                .map(rolePermissionMapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Get a specific role permission assignment
     *
     * @param id The role permission ID
     * @return The role permission DTO
     */
    @Transactional(readOnly = true)
    public RolePermissionDTO getRolePermissionById(Long id) {
        RolePermissionMapping mapping =
                rolePermissionMappingRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new IllegalArgumentException(
                                                "Role permission mapping not found with id: "
                                                        + id));

        return rolePermissionMapper.toDto(mapping);
    }

    /**
     * Update a role permission assignment
     *
     * @param id The role permission ID
     * @param rolePermissionRequestDTO The updated role permission data
     * @return The updated role permission DTO
     */
    @Transactional
    public RolePermissionDTO updateRolePermission(
            Long id, RolePermissionRequestDTO rolePermissionRequestDTO) {
        RolePermissionMapping existingMapping =
                rolePermissionMappingRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new IllegalArgumentException(
                                                "Role permission mapping not found with id: "
                                                        + id));

        // If role ID is changing, check if the new role exists
        if (rolePermissionRequestDTO.getRoleId() != null
                && !existingMapping
                        .getRole()
                        .getId()
                        .equals(rolePermissionRequestDTO.getRoleId())) {
            Role newRole =
                    roleRepository
                            .findById(rolePermissionRequestDTO.getRoleId())
                            .orElseThrow(
                                    () ->
                                            new RoleException.RoleNotFoundException(
                                                    "Role not found with id: "
                                                            + rolePermissionRequestDTO
                                                                    .getRoleId()));
            existingMapping.setRole(newRole);
        }

        // If permission ID is changing, check if the new permission exists
        if (rolePermissionRequestDTO.getPermissionId() != null
                && !existingMapping
                        .getPermission()
                        .getId()
                        .equals(rolePermissionRequestDTO.getPermissionId())) {
            Permission newPermission =
                    permissionRepository
                            .findById(rolePermissionRequestDTO.getPermissionId())
                            .orElseThrow(
                                    () ->
                                            new PermissionException.PermissionNotFoundException(
                                                    "Permission not found with id: "
                                                            + rolePermissionRequestDTO
                                                                    .getPermissionId()));
            existingMapping.setPermission(newPermission);
        }

        RolePermissionMapping updatedMapping =
                rolePermissionMappingRepository.save(existingMapping);
        log.info("Role permission mapping with ID {} updated successfully", id);

        return rolePermissionMapper.toDto(updatedMapping);
    }

    /**
     * Remove a permission from a role
     *
     * @param id The role permission ID
     */
    @Transactional
    public void removeRolePermission(Long id) {
        if (!rolePermissionMappingRepository.existsById(id)) {
            throw new IllegalArgumentException("Role permission mapping not found with id: " + id);
        }

        rolePermissionMappingRepository.deleteById(id);
        log.info("Role permission mapping with ID {} removed successfully", id);
    }

    /**
     * Remove a specific permission from a role
     *
     * @param roleId The role ID
     * @param permissionId The permission ID
     */
    @Transactional
    public void removePermissionFromRole(Long roleId, Long permissionId) {
        RolePermissionMapping mapping =
                rolePermissionMappingRepository
                        .findByRoleIdAndPermissionId(roleId, permissionId)
                        .orElseThrow(
                                () ->
                                        new IllegalArgumentException(
                                                "Role permission mapping not found for role ID: "
                                                        + roleId
                                                        + " and permission ID: "
                                                        + permissionId));

        rolePermissionMappingRepository.delete(mapping);
        log.info("Permission {} removed from role {} successfully", permissionId, roleId);
    }

    /**
     * Get all role permission assignments with pagination
     *
     * @param pageable The pagination information
     * @return Page of role permission DTOs
     */
    @Transactional(readOnly = true)
    public Page<RolePermissionDTO> getAllRolePermissions(Pageable pageable) {
        return rolePermissionMappingRepository.findAll(pageable).map(rolePermissionMapper::toDto);
    }

    /**
     * Assign multiple permissions to a role
     *
     * @param roleId The role ID
     * @param permissionIds List of permission IDs
     * @return List of created role permission DTOs
     */
    @Transactional
    public List<RolePermissionDTO> assignPermissionsToRole(Long roleId, List<Long> permissionIds) {
        log.info("Assigning {} permissions to role {}", permissionIds.size(), roleId);

        // Check if role exists
        Role role =
                roleRepository
                        .findById(roleId)
                        .orElseThrow(
                                () ->
                                        new RoleException.RoleNotFoundException(
                                                "Role not found with id: " + roleId));

        return permissionIds.stream()
                .filter(
                        permissionId ->
                                !rolePermissionMappingRepository.existsByRoleIdAndPermissionId(
                                        roleId, permissionId))
                .map(
                        permissionId -> {
                            Permission permission =
                                    permissionRepository
                                            .findById(permissionId)
                                            .orElseThrow(
                                                    () ->
                                                            new PermissionException
                                                                    .PermissionNotFoundException(
                                                                    "Permission not found with id: "
                                                                            + permissionId));

                            RolePermissionMapping mapping = new RolePermissionMapping();
                            mapping.setRole(role);
                            mapping.setPermission(permission);
                            mapping.setStatus("ACTIVE");

                            return rolePermissionMappingRepository.save(mapping);
                        })
                .map(rolePermissionMapper::toDto)
                .collect(Collectors.toList());
    }

    /**
     * Remove all permissions from a role
     *
     * @param roleId The role ID
     */
    @Transactional
    public void removeAllPermissionsFromRole(Long roleId) {
        // Check if role exists
        if (!roleRepository.existsById(roleId)) {
            throw new RoleException.RoleNotFoundException("Role not found with id: " + roleId);
        }

        rolePermissionMappingRepository.deleteByRoleId(roleId);
        log.info("All permissions removed from role {} successfully", roleId);
    }

    /**
     * Get role permissions by request parameters
     *
     * @param roleId The role ID (optional)
     * @param permissionId The permission ID (optional)
     * @return List of role permission DTOs
     */
    @Transactional(readOnly = true)
    public List<RolePermissionDTO> getRolePermissionsByParams(Long roleId, Long permissionId) {
        log.info(
                "Getting role permissions with roleId: {}, permissionId: {}", roleId, permissionId);

        // If both roleId and permissionId are provided
        if (roleId != null && permissionId != null) {
            return rolePermissionMappingRepository
                    .findByRoleIdAndPermissionId(roleId, permissionId)
                    .map(mapping -> Collections.singletonList(rolePermissionMapper.toDto(mapping)))
                    .orElse(Collections.emptyList());
        }

        // If only roleId is provided
        if (roleId != null) {
            return getRolePermissions(roleId);
        }

        // If only permissionId is provided
        if (permissionId != null) {
            return getPermissionRoles(permissionId);
        }

        // If no parameters are provided, return all role permissions
        return rolePermissionMappingRepository.findAll().stream()
                .map(rolePermissionMapper::toDto)
                .collect(Collectors.toList());
    }
}
