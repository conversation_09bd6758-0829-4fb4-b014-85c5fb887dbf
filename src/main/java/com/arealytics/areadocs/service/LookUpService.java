package com.arealytics.areadocs.service;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.areadocs.dto.lookUpDTO.StateDTO;
import com.arealytics.areadocs.dto.lookUpDTO.ZipCodeDTO;
import com.arealytics.areadocs.exception.ResourceNotFoundException;
import com.arealytics.areadocs.mapper.StateMapper;
import com.arealytics.areadocs.mapper.ZipCodeMapper;
import com.arealytics.areadocs.repository.StatesRepository;
import com.arealytics.areadocs.repository.ZipCodesRepository;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class LookUpService {

    private final StatesRepository statesRepository;
    private final ZipCodesRepository zipCodesRepository;
    private final StateMapper stateMapper;
    private final ZipCodeMapper zipCodeMapper;

    @Transactional
    public List<StateDTO> getAllStates() {
        log.info("Service: Fetching all states.");
        return statesRepository.findAll().stream()
                .map(stateMapper::toDto)
                .collect(Collectors.toList());
    }

    @Transactional
    public List<ZipCodeDTO> getZipCodesByStateIdOrAll(Long stateId)
            throws ResourceNotFoundException {
        if (stateId != null) {
            log.info("Service: Checking existence of state with ID: {}", stateId);
            statesRepository
                    .findById(stateId)
                    .orElseThrow(
                            () ->
                                    new ResourceNotFoundException(
                                            "State not found for ID: " + stateId));

            log.info("Service: Fetching zip codes for state ID: {}", stateId);
            return zipCodesRepository.findByStateId(stateId).stream()
                    .map(zipCodeMapper::toDto)
                    .collect(Collectors.toList());
        } else {
            log.info("Service: Fetching all zip codes.");
            return zipCodesRepository.findAll().stream()
                    .map(zipCodeMapper::toDto)
                    .collect(Collectors.toList());
        }
    }
}
