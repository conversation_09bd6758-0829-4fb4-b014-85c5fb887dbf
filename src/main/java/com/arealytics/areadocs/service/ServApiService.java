package com.arealytics.areadocs.service;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import com.arealytics.areadocs.dto.requestDTO.serv.DecryptTitleIdRequest;
import com.arealytics.areadocs.dto.requestDTO.serv.SubscriptionRequest;
import com.arealytics.areadocs.dto.requestDTO.serv.VerifyOwnershipRequest;
import com.arealytics.areadocs.dto.responseDTO.serv.*;

import lombok.extern.slf4j.Slf4j;

import static com.arealytics.areadocs.constants.SERVURLs.*;

@Service
@Slf4j
public class ServApiService {

    private final RestTemplate restTemplate;
    private final String baseUrl;
    private final String clientId;
    private final String clientSecret;
    private final String authToken;
    private final String alertBaseUrl;
    private final String verifyBaseUrl;
    private final String ordersBaseUrl;
    private final MockServApiService mockServApiService;
    private final DocumentPricingService documentPricingService;

    @Value("${serv.api.useMockResponses:false}")
    private boolean useMockResponses;

    public ServApiService(
            RestTemplate restTemplate,
            @Value("${serv.api.baseUrl}") String baseUrl,
            @Value("${serv.api.clientId}") String clientId,
            @Value("${serv.api.clientSecret}") String clientSecret,
            @Value("${serv.api.authToken}") String authToken,
            @Value("${serv.api.alertBaseUrl}") String alertBaseUrl,
            @Value("${serv.api.verifyBaseUrl}") String verifyBaseUrl,
            @Value("${serv.api.ordersBaseUrl}") String ordersBaseUrl,
            MockServApiService mockServApiService,
            DocumentPricingService documentPricingService) {
        this.restTemplate = restTemplate;
        this.baseUrl = baseUrl;
        this.clientId = clientId;
        this.clientSecret = clientSecret;
        this.authToken = authToken;
        this.alertBaseUrl = alertBaseUrl;
        this.verifyBaseUrl = verifyBaseUrl;
        this.ordersBaseUrl = ordersBaseUrl;
        this.mockServApiService = mockServApiService;
        this.documentPricingService = documentPricingService;
    }

    /**
     * Creates common HTTP headers used by all SERV API requests
     *
     * @return HttpHeaders with all required authentication headers
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.set("x-correlation-id", UUID.randomUUID().toString());
        headers.set("client_id", clientId);
        headers.set("client_secret", clientSecret);
        headers.set("Authorization", authToken);
        return headers;
    }

    /**
     * Get property information using various search parameters
     *
     * @param propertyParams the query parameters for property search
     * @param responseType the class type of the expected response
     * @return the property data response
     */
    public <T> ResponseEntity<T> getProperties(String propertyParams, Class<T> responseType) {
        UriComponentsBuilder builder =
                UriComponentsBuilder.fromHttpUrl(baseUrl + DISCOVERY_PROPERTIES_SEARCH_API_URL);
        if (propertyParams != null && !propertyParams.isEmpty()) {
            builder.query(propertyParams);
        }
        String url = builder.toUriString();

        HttpEntity<Void> requestEntity = new HttpEntity<>(createHeaders());
        return restTemplate.exchange(url, HttpMethod.GET, requestEntity, responseType);
    }

    /**
     * Get parcel information by Survey Parcel Identifier (SPI)
     *
     * @param parcelIdentifier the parcel Indentifier
     * @return the parcel data response
     */
    public <T> ResponseEntity<T> getParcel(String parcelIdentifier, Class<T> responseType) {
        String url =
                UriComponentsBuilder.fromHttpUrl(baseUrl + DISCOVERY_PARCELS_SEARCH_API_URL)
                        .queryParam("spi", parcelIdentifier)
                        .toUriString();

        HttpEntity<Void> requestEntity = new HttpEntity<>(createHeaders());
        return restTemplate.exchange(url, HttpMethod.GET, requestEntity, responseType);
    }

    /**
     * Get title information by Title ID
     *
     * @param titleId the title ID
     * @return the title data response
     */
    public <T> ResponseEntity<T> getTitleById(String titleId, Class<T> responseType) {
        // Todo: will be removed, once the SERV API is giving response.
        if (useMockResponses) {
            if (responseType == TitleResponse.class) {
                ResponseEntity<T> mockResponse =
                        (ResponseEntity<T>) mockServApiService.getMockTitleResponse();
                return mockResponse;
            } else {
                return ResponseEntity.ok().body(null); // Or throw an IllegalArgumentException
            }
        }
        String url =
                UriComponentsBuilder.fromHttpUrl(baseUrl + DISCOVERY_TITLES_SEARCH_API_URL)
                        .queryParam("titleId", titleId)
                        .toUriString();

        HttpEntity<Void> requestEntity = new HttpEntity<>(createHeaders());
        return restTemplate.exchange(url, HttpMethod.GET, requestEntity, responseType);
    }

    /**
     * Get township information
     *
     * @param townshipParams any query parameters needed to identify the township
     * @return the township data response
     */
    public <T> ResponseEntity<T> getTownships(String townshipParams, Class<T> responseType) {
        String url =
                UriComponentsBuilder.fromHttpUrl(baseUrl + DISCOVERY_TOWNSHIP_SEARCH_API_URL)
                        .toUriString();

        HttpEntity<Void> requestEntity = new HttpEntity<>(createHeaders());
        return restTemplate.exchange(url, HttpMethod.GET, requestEntity, responseType);
    }

    /**
     * Get parishes information
     *
     * @param parishesParams any query parameters needed to identify the parishes
     * @return the parishes data response
     */
    public <T> ResponseEntity<T> getParishes(String parishesParams, Class<T> responseType) {
        String url =
                UriComponentsBuilder.fromHttpUrl(baseUrl + DISCOVERY_PARISHES_SEARCH_API_URL)
                        .toUriString();

        HttpEntity<Void> requestEntity = new HttpEntity<>(createHeaders());
        return restTemplate.exchange(url, HttpMethod.GET, requestEntity, responseType);
    }

    /**
     * Subscribes to title alerts by sending a subscription request to the SERV API.
     *
     * @param request the subscription request containing volume/folio and date range details
     * @return the subscription response containing confirmation or details of the alert
     *     subscription
     */
    public ResponseEntity<SubscriptionResponse> subscriptionAlerts(SubscriptionRequest request) {
        String url =
                UriComponentsBuilder.fromHttpUrl(alertBaseUrl + ALERT_Title_Subscription_API_URL)
                        .toUriString();

        HttpHeaders headers = createHeaders();

        HttpEntity<SubscriptionRequest> requestEntity = new HttpEntity<>(request, headers);

        return restTemplate.exchange(
                url, HttpMethod.POST, requestEntity, SubscriptionResponse.class);
    }

    /**
     * Get proprietor information
     *
     * @param proprietorsParams any query parameters needed to identify the proprietors
     * @param responseType the class type of the expected response
     * @return the proprietor data response, or an empty object {} if JSON is invalid
     */
    public <T> ResponseEntity<T> getProprietors(String proprietorsParams, Class<T> responseType) {
        if (useMockResponses) {
            if (responseType == ProprietorsResponse.class) {
                ResponseEntity<T> mockResponse =
                        (ResponseEntity<T>) mockServApiService.getMockProprietorsResponse();

                return mockResponse;
            } else {
                return ResponseEntity.ok().body(null);
            }
        }
        UriComponentsBuilder builder =
                UriComponentsBuilder.fromHttpUrl(baseUrl + DISCOVERY_PROPRIETORS_SEARCH_API_URL);
        if (proprietorsParams != null && !proprietorsParams.isEmpty()) {
            builder.query(proprietorsParams);
        }
        String url = builder.toUriString();

        HttpEntity<Void> requestEntity = new HttpEntity<>(createHeaders());

        return restTemplate.exchange(url, HttpMethod.GET, requestEntity, responseType);
    }

    /**
     * Subscribes to title alerts by sending a subscription request to the SERV API.
     *
     * @param request the subscription request containing volume/folio and date range details
     * @return the subscription response containing confirmation or details of the alert
     *     subscription
     */
    public ResponseEntity<VerifyOwnershipResponse> verifyOwnership(VerifyOwnershipRequest request) {
        String url =
                UriComponentsBuilder.fromHttpUrl(verifyBaseUrl + VERIFY_Ownership_API_URL)
                        .toUriString();

        HttpHeaders headers = createHeaders();

        HttpEntity<VerifyOwnershipRequest> requestEntity = new HttpEntity<>(request, headers);

        return restTemplate.exchange(
                url, HttpMethod.POST, requestEntity, VerifyOwnershipResponse.class);
    }

    public <T> ResponseEntity<T> getProducts(String productsParams, Class<T> responseType) {

        // Todo: will be removed , once the SERV API is giving response.
        if (useMockResponses) {
            if (responseType == ProductsResponse.class) {
                Map<String, String> queryParams = extractQueryParams(productsParams);
                // Future purpose
                String titleId = queryParams.get("titleId");
                String propertyPfi = queryParams.get("propertyPfi");

                ResponseEntity<T> mockResponse =
                        (ResponseEntity<T>)
                                mockServApiService.getMockOrderProducts(titleId, propertyPfi);
                ProductsResponse products = (ProductsResponse) mockResponse.getBody();
                if (products != null) {
                    // Apply pricing logic
                    ProductsResponse updatedResponse =
                            documentPricingService.addPlatformPricesToProducts(products, null);
                    mockResponse = (ResponseEntity<T>) ResponseEntity.ok(updatedResponse);
                }
                return mockResponse;
            } else {
                return ResponseEntity.ok().body(null);
            }
        }

        UriComponentsBuilder builder =
                UriComponentsBuilder.fromHttpUrl(ordersBaseUrl + ORDERS_Products_API_URL);

        if (productsParams != null && !productsParams.isEmpty()) {
            builder.query(productsParams);
        }

        String url = builder.toUriString();

        HttpHeaders headers = createHeaders();
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

        ResponseEntity<T> response =
                restTemplate.exchange(url, HttpMethod.GET, requestEntity, responseType);
        if (responseType == ProductsResponse.class) {
            ProductsResponse products = (ProductsResponse) response.getBody();
            if (products != null) {
                // Apply pricing logic
                ProductsResponse productsWithPlatformPrices =
                        documentPricingService.addPlatformPricesToProducts(products, null);
                response = (ResponseEntity<T>) ResponseEntity.ok(productsWithPlatformPrices);
            }
        }
        return response;
    }

    public <T> ResponseEntity<T> getOwnersCorporationManagers(Class<T> responseType) {
        String url =
                UriComponentsBuilder.fromHttpUrl(
                                ordersBaseUrl + ORDERS_Owner_Corporation_Managers_API_URL)
                        .toUriString();

        HttpHeaders headers = createHeaders();
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

        return restTemplate.exchange(url, HttpMethod.GET, requestEntity, responseType);
    }

    public <T> ResponseEntity<T> getMunicipalities(Class<T> responseType) {
        String url =
                UriComponentsBuilder.fromHttpUrl(ordersBaseUrl + ORDERS_Municipalities_API_URL)
                        .toUriString();

        HttpHeaders headers = createHeaders();
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

        return restTemplate.exchange(url, HttpMethod.GET, requestEntity, responseType);
    }

    public <T> ResponseEntity<T> createOrder(Object request, Class<T> responseType) {
        String url = UriComponentsBuilder.fromHttpUrl(ordersBaseUrl).toUriString();

        HttpHeaders headers = createHeaders();
        HttpEntity<Object> requestEntity = new HttpEntity<>(request, headers);

        return restTemplate.exchange(url, HttpMethod.POST, requestEntity, responseType);
    }

    public <T> ResponseEntity<T> getOrders(String orderParams, Class<T> responseType) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(ordersBaseUrl);

        if (orderParams != null && !orderParams.isEmpty()) {
            builder.query(orderParams);
        }

        String url = builder.toUriString();

        HttpHeaders headers = createHeaders();
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

        return restTemplate.exchange(
                builder.toUriString(), HttpMethod.GET, requestEntity, responseType);
    }

    public <T> ResponseEntity<T> getOrderDetailsById(String orderId, Class<T> responseType) {
        String url = UriComponentsBuilder.fromHttpUrl(ordersBaseUrl + "/" + orderId).toUriString();

        HttpHeaders headers = createHeaders();
        HttpEntity<Void> requestEntity = new HttpEntity<>(headers);

        return restTemplate.exchange(url, HttpMethod.GET, requestEntity, responseType);
    }

    public <T> ResponseEntity<T> getOrderItemDetails(
            String orderId, int orderItemNumber, Class<T> responseType) {
        String url =
                UriComponentsBuilder.fromHttpUrl(
                                ordersBaseUrl + "/" + orderId + "/item/" + orderItemNumber)
                        .toUriString();
        HttpEntity<Void> requestEntity = new HttpEntity<>(createHeaders());

        return restTemplate.exchange(url, HttpMethod.GET, requestEntity, responseType);
    }

    public ResponseEntity<DecryptTitleIdResponse> decryptTitleIds(DecryptTitleIdRequest request) {
        String url =
                UriComponentsBuilder.fromHttpUrl(ordersBaseUrl + ORDERS_Decrypty_Titles_API_URL)
                        .toUriString();

        HttpHeaders headers = createHeaders();

        HttpEntity<DecryptTitleIdRequest> requestEntity = new HttpEntity<>(request, headers);

        return restTemplate.exchange(
                url, HttpMethod.POST, requestEntity, DecryptTitleIdResponse.class);
    }

    // Helper method to parse query parameters into a Map
    private Map<String, String> extractQueryParams(String query) {
        Map<String, String> queryParams = new HashMap<>();
        if (query != null && !query.isEmpty()) {
            String[] pairs = query.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                if (keyValue.length == 2) {
                    queryParams.put(keyValue[0], keyValue[1]);
                }
            }
        }
        return queryParams;
    }

    /**
     * Get a specific order item with documents url from the SERV API
     *
     * @param orderId external order id
     * @param orderItemNumber external order item number
     * @param responseType the class type of the expected response
     * @return the response entity
     */
    public <T> ResponseEntity<T> getServOrderItem(
            String orderId, String orderItemNumber, Class<T> responseType) {
        log.debug("getServOrderItem orderId: {}, orderItemNumber: {}", orderId, orderItemNumber);
        if (useMockResponses) {
            if (responseType == OrderItemResponse.class) {
                ResponseEntity<T> mockResponse =
                        (ResponseEntity<T>) mockServApiService.getMockServOrderItem();
                return mockResponse;
            } else {
                return ResponseEntity.ok().body(null);
            }
        }
        // Construct the actual URL
        String url =
                UriComponentsBuilder.fromHttpUrl(baseUrl)
                        .path(ORDERS_ITEM_API_URL)
                        .buildAndExpand(orderId, orderItemNumber)
                        .toUriString();
        HttpEntity<Void> requestEntity = new HttpEntity<>(createHeaders());

        RestTemplate restTemplate = new RestTemplate();
        return restTemplate.exchange(url, HttpMethod.GET, requestEntity, responseType);
    }
}
