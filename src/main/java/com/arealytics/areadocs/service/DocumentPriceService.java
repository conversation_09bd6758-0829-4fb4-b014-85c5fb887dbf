package com.arealytics.areadocs.service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.areadocs.domain.Company;
import com.arealytics.areadocs.domain.CompanySpecialPrice;
import com.arealytics.areadocs.domain.DocumentPrice;
import com.arealytics.areadocs.domain.DocumentType;
import com.arealytics.areadocs.dto.requestDTO.DocumentPriceRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.DocumentPriceResponseDTO;
import com.arealytics.areadocs.exception.CompanyException;
import com.arealytics.areadocs.mapper.DocumentPriceMapper;
import com.arealytics.areadocs.repository.CompanyRepository;
import com.arealytics.areadocs.repository.CompanySpecialPriceRepository;
import com.arealytics.areadocs.repository.DocumentPriceRepository;
import com.arealytics.areadocs.repository.DocumentTypeRepository;

import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class DocumentPriceService {

    private final DocumentPriceRepository documentPriceRepository;
    private final DocumentTypeRepository documentTypeRepository;
    private final DocumentPriceMapper documentPriceMapper;
    private final MockServApiService mockServApiService;
    private final CompanySpecialPriceRepository companySpecialPriceRepository;
    private final CompanyRepository companyRepository;

    @Transactional
    public DocumentPriceResponseDTO createDocumentPrice(
            DocumentPriceRequestDTO documentPriceRequestDTO) {
        if (documentPriceRequestDTO == null || documentPriceRequestDTO.getProductCode() == null) {
            throw new IllegalArgumentException("ProductCode details must be provided");
        }

        if (documentPriceRepository.existsByProductCode(documentPriceRequestDTO.getProductCode())) {
            throw new CompanyException(
                    "'Product code'"
                            + documentPriceRequestDTO.getProductCode()
                            + "'already exists'");
        }

        validateUniqueConstraint(
                null,
                documentPriceRequestDTO.getProductCode(),
                documentPriceRequestDTO.getEffectiveDate());

        BigDecimal priceForGst =
                documentPriceRequestDTO.getEffectiveBasePrice() != null
                        ? documentPriceRequestDTO.getEffectiveBasePrice()
                        : documentPriceRequestDTO.getBasePrice();
        if (priceForGst == null) {
            throw new IllegalArgumentException(
                    "Either effectiveBase price or base price should be provided");
        }

        BigDecimal gst =
                mockServApiService.calculateGst(
                        priceForGst, documentPriceRequestDTO.getEffectivePriceGst());
        documentPriceRequestDTO.setEffectivePriceGst(gst);

        DocumentPrice documentPrice = documentPriceMapper.toEntity(documentPriceRequestDTO);
        setRelationships(documentPrice, documentPriceRequestDTO);
        documentPrice.setIsActive(true);

        DocumentPrice savedDocumentPrice = documentPriceRepository.saveAndFlush(documentPrice);

        List<Company> companies = companyRepository.findAll();
        List<CompanySpecialPrice> companySpecialPrices = new ArrayList<>();
        for (Company company : companies) {
            if (!companySpecialPriceRepository.existsByCompanyIdAndDocumentPriceId(
                    company.getId(), savedDocumentPrice.getId())) {
                CompanySpecialPrice companySpecialPrice =
                        CompanySpecialPrice.builder()
                                .documentPrice(savedDocumentPrice)
                                .company(company)
                                .specialPrice(savedDocumentPrice.getBasePrice())
                                .specialPriceGst(savedDocumentPrice.getEffectivePriceGst())
                                .build();
                companySpecialPrices.add(companySpecialPrice);
            }
        }
        if (!companySpecialPrices.isEmpty()) {
            companySpecialPriceRepository.saveAll(companySpecialPrices);
        }
        return documentPriceMapper.toDto(savedDocumentPrice);
    }

    @Transactional
    public DocumentPriceResponseDTO updateDocumentPrice(
            Long id, DocumentPriceRequestDTO documentPriceRequestDTO) {
        DocumentPrice existingDocumentPrice =
                documentPriceRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "Document price not found with id: " + id));

        if (documentPriceRequestDTO == null || documentPriceRequestDTO.getProductCode() == null) {
            throw new IllegalArgumentException("ProductCode details must be provided");
        }

        if (!existingDocumentPrice
                .getProductCode()
                .equals(documentPriceRequestDTO.getProductCode())) {
            if (documentPriceRepository.existsByProductCode(
                    documentPriceRequestDTO.getProductCode())) {
                throw new CompanyException(
                        "'Product code '"
                                + documentPriceRequestDTO.getProductCode()
                                + "'already exists'");
            }
        }

        if (!existingDocumentPrice.getProductCode().equals(documentPriceRequestDTO.getProductCode())
                || !existingDocumentPrice
                        .getEffectiveDate()
                        .equals(documentPriceRequestDTO.getEffectiveDate())) {
            validateUniqueConstraint(
                    id,
                    documentPriceRequestDTO.getProductCode(),
                    documentPriceRequestDTO.getEffectiveDate());
        }

        BigDecimal priceForGst =
                documentPriceRequestDTO.getEffectiveBasePrice() != null
                        ? documentPriceRequestDTO.getEffectiveBasePrice()
                        : documentPriceRequestDTO.getBasePrice();
        if (priceForGst == null) {
            throw new IllegalArgumentException(
                    "Either effectiveBasePrice or basePrice must be provided");
        }
        BigDecimal gst =
                mockServApiService.calculateGst(
                        priceForGst, documentPriceRequestDTO.getEffectivePriceGst());
        documentPriceRequestDTO.setEffectivePriceGst(gst);

        documentPriceMapper.updateDocumentPriceFromDto(
                documentPriceRequestDTO, existingDocumentPrice);
        setRelationships(existingDocumentPrice, documentPriceRequestDTO);

        DocumentPrice savedDocumentPrice =
                documentPriceRepository.saveAndFlush(existingDocumentPrice);

        List<CompanySpecialPrice> companySpecialPrices =
                companySpecialPriceRepository.findAllByDocumentPriceId(savedDocumentPrice.getId());
        for (CompanySpecialPrice companySpecialPrice : companySpecialPrices) {
            if (companySpecialPrice.getSpecialPrice() == null) {
                companySpecialPrice.setSpecialPriceGst(savedDocumentPrice.getEffectivePriceGst());

                companySpecialPriceRepository.save(companySpecialPrice);
            }
        }
        return documentPriceMapper.toDto(savedDocumentPrice);
    }

    @Transactional(readOnly = true)
    public DocumentPriceResponseDTO getDocumentPriceById(Long id) {
        DocumentPrice documentPrice =
                documentPriceRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "Document price not found with id: " + id));
        return documentPriceMapper.toDto(documentPrice);
    }

    @Transactional(readOnly = true)
    public Optional<DocumentPriceResponseDTO> findDocumentPriceById(Long id) {
        return documentPriceRepository.findById(id).map(documentPriceMapper::toDto);
    }

    @Transactional(readOnly = true)
    public Page<DocumentPriceResponseDTO> getDocumentPrices(
            Long productCodeId, Long documentTypeId, Pageable pageable) {
        Specification<DocumentPrice> spec =
                (root, query, criteriaBuilder) -> {
                    List<Predicate> predicates = new ArrayList<>();

                    if (productCodeId != null) {
                        predicates.add(
                                criteriaBuilder.equal(
                                        root.get("productCode").get("id"), productCodeId));
                    }

                    if (documentTypeId != null) {
                        predicates.add(
                                criteriaBuilder.equal(
                                        root.get("documentType").get("id"), documentTypeId));
                    }

                    return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
                };

        return documentPriceRepository.findAll(spec, pageable).map(documentPriceMapper::toDto);
    }

    @Transactional
    public void deleteDocumentPrice(Long id) {
        DocumentPrice documentPrice =
                documentPriceRepository
                        .findById(id)
                        .orElseThrow(
                                () ->
                                        new EntityNotFoundException(
                                                "Document price not found with id: " + id));
        documentPrice.softDelete();
        documentPriceRepository.save(documentPrice);
    }

    private void validateUniqueConstraint(
            Long excludeDocumentPriceId, String productCode, LocalDateTime effectiveDate) {
        Specification<DocumentPrice> spec =
                (root, query, criteriaBuilder) -> {
                    List<Predicate> predicates = new ArrayList<>();
                    predicates.add(criteriaBuilder.equal(root.get("productCode"), productCode));
                    predicates.add(criteriaBuilder.equal(root.get("effectiveDate"), effectiveDate));
                    if (excludeDocumentPriceId != null) {
                        predicates.add(
                                criteriaBuilder.notEqual(root.get("id"), excludeDocumentPriceId));
                    }
                    return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
                };

        if (documentPriceRepository.exists(spec)) {
            throw new CompanyException(
                    "A document price already exists for product code ID "
                            + productCode
                            + " and effective date "
                            + effectiveDate);
        }
    }

    private void setRelationships(
            DocumentPrice documentPrice, DocumentPriceRequestDTO documentPriceRequestDTO) {
        if (documentPriceRequestDTO.getDocumentTypeId() != null) {
            DocumentType documentType =
                    documentTypeRepository
                            .findById(documentPriceRequestDTO.getDocumentTypeId())
                            .orElseThrow(
                                    () ->
                                            new EntityNotFoundException(
                                                    "Document type not found with id: "
                                                            + documentPriceRequestDTO
                                                                    .getDocumentTypeId()));
            documentPrice.setDocumentType(documentType);
        } else {
            documentPrice.setDocumentType(null);
        }
    }
}
