package com.arealytics.areadocs.service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;

import org.apache.commons.lang3.tuple.Pair;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.areadocs.domain.CompanySpecialPrice;
import com.arealytics.areadocs.domain.ProductCode;
import com.arealytics.areadocs.dto.requestDTO.serv.CreateOrderRequest;
import com.arealytics.areadocs.dto.responseDTO.serv.*;
import com.arealytics.areadocs.enumeration.SERV.*;
import com.arealytics.areadocs.repository.CompanyMembershipRepository;
import com.arealytics.areadocs.repository.CompanySpecialPriceRepository;
import com.arealytics.areadocs.repository.DocumentPriceRepository;
import com.arealytics.areadocs.repository.DocumentTypeRepository;
import com.arealytics.areadocs.repository.ProductCodeRepository;
import com.arealytics.areadocs.util.AuthUtils;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class MockServApiService {
    static Long counter = 0L;
    private final S3Service s3Service;
    private final UserService userService;
    private final CompanyMembershipRepository companyMembershipRepository;
    private final DocumentTypeRepository documentTypeRepository;
    private final DocumentPriceRepository documentPriceRepository;
    private final CompanySpecialPriceRepository companySpecialPriceRepository;
    private final ProductCodeRepository productCodeRepository;
    private final AuthUtils authUtils;

    // Explicit Constructor Injection
    public MockServApiService(
            S3Service s3Service,
            UserService userService,
            CompanyMembershipRepository companyMembershipRepository,
            DocumentTypeRepository documentTypeRepository,
            DocumentPriceRepository documentPriceRepository,
            CompanySpecialPriceRepository companySpecialPriceRepository,
            ProductCodeRepository productCodeRepository,
            AuthUtils authUtils) {
        this.s3Service = s3Service;
        this.userService = userService;
        this.companyMembershipRepository = companyMembershipRepository;
        this.documentTypeRepository = documentTypeRepository;
        this.documentPriceRepository = documentPriceRepository;
        this.companySpecialPriceRepository = companySpecialPriceRepository;
        this.productCodeRepository = productCodeRepository;
        this.authUtils = authUtils;
    }

    /**
     * Provides a mock response for the /products endpoint.
     *
     * @param titleId The title ID parameter (optional)
     * @param propertyPfi The property PFI parameter (optional)
     * @return A mock ProductsResponse
     */
    @Transactional(readOnly = true)
    public ResponseEntity<ProductsResponse> getMockOrderProducts(
            String titleId, String propertyPfi) {
        ProductsResponse response = new ProductsResponse();
        List<ProductsResponse.Product> products = new ArrayList<>();

        // Create a mock product
        ProductsResponse.Product product = new ProductsResponse.Product();
        product.setProductCode("RegisterSearchStatement");

        ProductsResponse.ProductIds productIds = new ProductsResponse.ProductIds();
        productIds.setTitleId(titleId != null ? titleId : "10940/843");
        product.setProductIds(productIds);

        product.setProductName("Register Search Statement (Copy of Title)");
        product.setProductDescription(
                "PDF version of the copy of the Title, lists the current registered proprietors,"
                    + " any encumbrances, caveats or notices that apply to the land, but does not"
                    + " include a sketch of the plan.");

        ProductsResponse.ProductInformation productInfo = new ProductsResponse.ProductInformation();
        productInfo.setDocumentType("MORTGAGE");
        product.setProductInformation(productInfo);

        product.setProductAvailability("AVAILABLE");
        product.setAuthorityName("Victorian Land Title Registry");
        product.setPrice(new BigDecimal("891"));
        product.setGst(new BigDecimal("89"));
        product.setTurnaroundTime(5);
        product.setTurnaroundTimeUnit("DAY");
        product.setCustomerWarning(
                "Please note that Commemorative Title Certificate is a decorative memento that"
                    + " celebrates property ownership and makes pride of ownership tangible. This"
                    + " cannot be used for any legal purpose.");

        // Add extra data
        List<ProductsResponse.ExtraData> extraData = new ArrayList<>();
        ProductsResponse.ExtraData extraData1 = new ProductsResponse.ExtraData();
        extraData1.setKey("vendors");
        ProductsResponse.ExtraData extraData2 = new ProductsResponse.ExtraData();
        extraData2.setKey("meterRead");
        extraData.add(extraData1);
        extraData.add(extraData2);
        product.setExtraData(extraData);

        // Add Prerequisites
        List<ProductsResponse.Prerequisite> prerequisites = new ArrayList<>();
        ProductsResponse.Prerequisite prerequisite = new ProductsResponse.Prerequisite();
        prerequisite.setProductCode("RegisterSearchStatement");

        ProductsResponse.ProductIds prerequisiteProductIds = new ProductsResponse.ProductIds();
        prerequisiteProductIds.setTitleId(titleId != null ? titleId : "10940/843");
        prerequisite.setProductIds(prerequisiteProductIds);

        prerequisite.setProductName("Register Search Statement (Copy of Title)");
        prerequisite.setProductDescription(
                "PDF version of the copy of the Title, lists the current registered proprietors,"
                    + " any encumbrances, caveats or notices that apply to the land, but does not"
                    + " include a sketch of the plan.");

        ProductsResponse.ProductInformation prerequisiteProductInfo =
                new ProductsResponse.ProductInformation();
        prerequisiteProductInfo.setDocumentType("MORTGAGE");
        prerequisite.setProductInformation(prerequisiteProductInfo);

        prerequisite.setProductAvailability("AVAILABLE");
        prerequisite.setAuthorityName("Victorian Land Title Registry");
        prerequisite.setGst(new BigDecimal("891"));
        prerequisite.setGst(new BigDecimal("89"));
        prerequisite.setTurnaroundTime(5);
        prerequisite.setTurnaroundTimeUnit("DAY");
        prerequisite.setCustomerWarning(
                "Please note that Commemorative Title Certificate is a decorative memento that"
                    + " celebrates property ownership and makes pride of ownership tangible. This"
                    + " cannot be used for any legal purpose.");

        // Add extra data to prerequisite
        List<ProductsResponse.ExtraData> prerequisiteExtraData = new ArrayList<>();
        ProductsResponse.ExtraData prerequisiteExtraData1 = new ProductsResponse.ExtraData();
        prerequisiteExtraData1.setKey("vendors");
        ProductsResponse.ExtraData prerequisiteExtraData2 = new ProductsResponse.ExtraData();
        prerequisiteExtraData2.setKey("meterRead");
        prerequisiteExtraData.add(prerequisiteExtraData1);
        prerequisiteExtraData.add(prerequisiteExtraData2);
        prerequisite.setExtraData(prerequisiteExtraData);

        // Add previous order to prerequisite
        ProductsResponse.PreviousOrder previousOrder = new ProductsResponse.PreviousOrder();
        previousOrder.setOrderId("");
        previousOrder.setOrderItemNumber(1);
        previousOrder.setOrderDate("2023-07-14T00:00:00.000000+11:00");
        prerequisite.setPreviousOrder(previousOrder);

        prerequisites.add(prerequisite);
        product.setPrerequisites(prerequisites);

        products.add(product);
        response.setProducts(products);

        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    public ResponseEntity<CreateOrderResponse> getMockCreateOrder(CreateOrderRequest request) {
        CreateOrderResponse response = new CreateOrderResponse();

        // Set order details
        response.setOrderId("10219435");
        response.setTotalPrice(891);
        response.setTotalGst(89);

        // Create fees
        List<CreateOrderResponse.Fee> fees = new ArrayList<>();
        CreateOrderResponse.Fee fee = new CreateOrderResponse.Fee();
        fee.setType("");
        fee.setAmount(891);
        fee.setGst(new BigDecimal("89"));
        fee.setFeeDetails(Collections.emptyMap());
        fees.add(fee);
        response.setFees(fees);

        // Create items ordered
        List<CreateOrderResponse.ItemOrdered> itemsOrdered = new ArrayList<>();
        CreateOrderResponse.ItemOrdered item = new CreateOrderResponse.ItemOrdered();
        item.setOrderItemNumber(1);
        item.setProductCode("RegisterSearchStatement");

        CreateOrderResponse.ProductIds productIds = new CreateOrderResponse.ProductIds();
        productIds.setTitleId("10940/843");
        item.setProductIds(productIds);

        item.setProductName("Register Search Statement (Copy of Title)");

        CreateOrderResponse.ProductInformation productInfo =
                new CreateOrderResponse.ProductInformation();
        productInfo.setDocumentType("MORTGAGE");
        item.setProductInformation(productInfo);

        item.setAuthorityName("Victorian Land Title Registry");
        item.setPrice(new BigDecimal("891"));
        item.setGst(new BigDecimal("89"));

        itemsOrdered.add(item);
        response.setItemsOrdered(itemsOrdered);

        // Generate and upload PDF
        generateAndUploadPdf(response);

        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    private void generateAndUploadPdf(CreateOrderResponse response) {
        PDDocument document = new PDDocument();
        PDPage page = new PDPage();
        document.addPage(page);

        try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
            contentStream.beginText();
            contentStream.setFont(PDType1Font.HELVETICA_BOLD, 12);
            contentStream.newLineAtOffset(100, 700);
            contentStream.showText("Order ID: " + response.getOrderId());
            contentStream.newLineAtOffset(0, -15);
            contentStream.showText("Total Price: " + response.getTotalPrice());
            contentStream.newLineAtOffset(0, -15);
            contentStream.showText("Total GST: " + response.getTotalGst());
            contentStream.endText();
        } catch (IOException e) {
            e.printStackTrace();
        }

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            document.save(byteArrayOutputStream);
            document.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

        byte[] pdfData = byteArrayOutputStream.toByteArray();

        try {
            String fileKey = "order-documents/" + response.getOrderId() + ".pdf";
            s3Service.uploadFile(pdfData, fileKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Pair<Integer, Integer> applyPricingLogic(
            ProductCode productCode,
            Long companyId,
            BigDecimal basePrice,
            LocalDateTime today,
            BigDecimal docEffectiveBasePrice,
            BigDecimal docGst) {
        if (companyId != null) {
            Optional<CompanySpecialPrice> specialPriceOpt =
                    companySpecialPriceRepository
                            .findFirstByCompanyIdAndProductCodeId(companyId, productCode.getId())
                            .filter(
                                    sp ->
                                            Boolean.TRUE.equals(sp.getIsActive())
                                                    && (sp.getEffectiveDate() == null
                                                            || !today.isBefore(
                                                                    sp.getEffectiveDate()))
                                                    && (sp.getExpiryDate() == null
                                                            || !today.isAfter(sp.getExpiryDate())));

            if (specialPriceOpt.isPresent()) {
                CompanySpecialPrice sp = specialPriceOpt.get();
                BigDecimal specialPrice = sp.getSpecialPrice();
                BigDecimal gstRate = sp.getGst();

                BigDecimal gstAmount = calculateGst(specialPrice, gstRate);

                return Pair.of(specialPrice.intValue(), gstAmount.intValue());
            }
        }
        if (docEffectiveBasePrice != null) {
            BigDecimal gst = calculateGst(docEffectiveBasePrice, docGst);
            return Pair.of(docEffectiveBasePrice.intValue(), gst.intValue());
        }

        return Pair.of(0, 0);
    }

    public BigDecimal calculateGst(BigDecimal price, BigDecimal gstRate) {
        if (gstRate == null) return BigDecimal.ZERO;
        return price.multiply(gstRate).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
    }

    /**
     * Provides a mock response for the /getTitle endpoint. This method directly populates the
     * TitleResponse object with sample data.
     *
     * @return A mocked TitleResponse object.
     */
    public ResponseEntity<TitleResponse> getMockTitleResponse() {
        TitleResponse response = new TitleResponse();
        System.out.println("Mock Title Response: " + response);
        TitleResponse.TitleDTO titleDTO = new TitleResponse.TitleDTO();
        titleDTO.setTitleId("qp0gjuieZ5VzbNKtT/eZvqPryobRkEYxMA==");
        titleDTO.setTitleStatus(TitleStatus.ACTIVE);
        titleDTO.setTitleType(TitleType.FREEHOLD);
        titleDTO.setCopyOfTitleType(CopyOfTitleType.ELECTRONIC);
        titleDTO.setTitleStreetAddress("UNIT 328 LEVEL 3 800 SWANSTON STREET CARLTON VIC 3053");
        titleDTO.setLandDescriptionText("Lot 3A2 on Plan of Subdivision 515587S.");

        TitleResponse.TitleReferenceDTO titleReferenceDTO = new TitleResponse.TitleReferenceDTO();
        titleReferenceDTO.setVolume(0);
        titleReferenceDTO.setFolio(0);
        titleReferenceDTO.setSuffix("");
        titleDTO.setTitleReference(titleReferenceDTO);

        TitleResponse.LandParcelDTO landParcelDTO = new TitleResponse.LandParcelDTO();
        landParcelDTO.setParcelIdentifier("3A2\\PS515587");
        landParcelDTO.setParcelType(ParcelType.LOT);
        landParcelDTO.setLotType(LotType.RESTRICTED);
        landParcelDTO.setParcelStatus(ParcelStatus.ACTIVE);
        titleDTO.setLandParcels(Arrays.asList(landParcelDTO));

        TitleResponse.TitlePropertyDTO titlePropertyDTO = new TitleResponse.TitlePropertyDTO();
        titlePropertyDTO.setPropertyPfi(208045633L);
        titlePropertyDTO.setIsMultiAssess(true);
        titlePropertyDTO.setPropertyStatus(PropertyStatus.ACTIVE);
        titleDTO.setProperties(Arrays.asList(titlePropertyDTO));

        response.setTitleSummaries(Arrays.asList(titleDTO));

        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    /**
     * Provides a mock response for the /getProprietors endpoint. This method directly populates the
     * ProprietorsResponse object with sample data.
     *
     * @return A mocked ProprietorsResponse object.
     */
    public ResponseEntity<ProprietorsResponse> getMockProprietorsResponse() {
        ProprietorsResponse response = new ProprietorsResponse();

        ProprietorsResponse.ProprietorSummary.TitleDetails titleDetails =
                new ProprietorsResponse.ProprietorSummary.TitleDetails();
        titleDetails.setTitleId("qp0gjuieZ5VzbNKtT/eZvqPryobRkEYxMA==");
        titleDetails.setTitleStatus(TitleStatus.ACTIVE);
        titleDetails.setTitleType(TitleType.FREEHOLD);
        titleDetails.setLandDescriptionText("Lot 3A2 on Plan of Subdivision 515587S.");
        titleDetails.setMunicipality("GREATER BENDIGO");

        ProprietorsResponse.ProprietorSummary proprietorSummary =
                new ProprietorsResponse.ProprietorSummary();
        proprietorSummary.setLastName("");
        proprietorSummary.setGivenName("");
        proprietorSummary.setCompanyName("");
        proprietorSummary.setTitleDetails(titleDetails);

        response.setProprietorSummaries(List.of(proprietorSummary));

        ProprietorsResponse.Pagination pagination = new ProprietorsResponse.Pagination();
        pagination.setPageSize(0);
        pagination.setNextPage(2);
        response.setPagination(pagination);

        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    public ResponseEntity<OrderItemResponse> getMockServOrderItem() {
        OrderItemResponse mock = new OrderItemResponse();
        mock.setOrderId("10219435");
        mock.setOrderItemNumber(1);
        mock.setProductName("Register Search Statement (Copy of Title)");
        mock.setProductCode("RegisterSearchStatement");
        mock.setCustomerReference("10219435");
        mock.setResourceText("Register Search Statement (Copy of Title)");
        mock.setResourceJson("Register Search Statement (Copy of Title)");
        mock.setResourceExpiry("2024-11-07T04:43:46.564302+11:00");
        mock.setResourceFileType("application/pdf");
        mock.setProductStatus(ProductStatus.COMPLETE.name());
        mock.setResourceLocation(
                "https://www.infotrack.com.au/wp-content/uploads/VIC-Title-Search-Sample.pdf?utm_source=chatgpt.com");
        mock.setResourceFileSize(1897);
        return new ResponseEntity<>(mock, HttpStatus.OK);
    }
}
