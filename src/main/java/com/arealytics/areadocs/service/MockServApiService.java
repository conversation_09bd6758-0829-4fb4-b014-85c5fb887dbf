package com.arealytics.areadocs.service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;

import org.apache.commons.lang3.tuple.Pair;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.areadocs.domain.CompanySpecialPrice;
import com.arealytics.areadocs.domain.DocumentPrice;
import com.arealytics.areadocs.dto.requestDTO.serv.CreateOrderRequest;
import com.arealytics.areadocs.dto.responseDTO.serv.*;
import com.arealytics.areadocs.enumeration.SERV.*;
import com.arealytics.areadocs.repository.CompanyMembershipRepository;
import com.arealytics.areadocs.repository.CompanySpecialPriceRepository;
import com.arealytics.areadocs.repository.DocumentPriceRepository;
import com.arealytics.areadocs.repository.DocumentTypeRepository;
import com.arealytics.areadocs.util.AuthUtils;
import com.arealytics.areadocs.util.DateUtils;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class MockServApiService {
    static Long counter = 0L;
    private final S3Service s3Service;
    private final UserService userService;
    private final CompanyMembershipRepository companyMembershipRepository;
    private final DocumentTypeRepository documentTypeRepository;
    private final DocumentPriceRepository documentPriceRepository;
    private final CompanySpecialPriceRepository companySpecialPriceRepository;
    private final AuthUtils authUtils;
    private ProductsResponse.Product createProduct(String code, String name, String description, String authority,
                                                   int price, int gst, int tat, String tatUnit,
                                                   String titleId, String documentId,
                                                   List<String> extraKeys, String warning) {
        return createProduct(code, name, description, authority, price, gst, tat, tatUnit, titleId, documentId, extraKeys, warning, null);
    }

    private ProductsResponse.Product createProduct(String code, String name, String description, String authority,
                                                   int price, int gst, int tat, String tatUnit,
                                                   String titleId, String documentId,
                                                   List<String> extraKeys, String warning,
                                                   String documentType) {
        ProductsResponse.Product product = new ProductsResponse.Product();
        product.setProductCode(code);
        product.setProductName(name);
        product.setProductDescription(description);
        product.setAuthorityName(authority);
        product.setPrice(BigDecimal.valueOf(price));
        product.setGst(BigDecimal.valueOf(gst));
        product.setTurnaroundTime(tat);
        product.setTurnaroundTimeUnit(tatUnit);
        product.setProductAvailability("AVAILABLE");

        // Product IDs
        ProductsResponse.ProductIds ids = new ProductsResponse.ProductIds();
        if (titleId != null) ids.setTitleId(titleId);
        if (documentId != null) ids.setDocumentId(documentId);
        product.setProductIds(ids);

        // Optional document type
        if (documentType != null) {
            ProductsResponse.ProductInformation info = new ProductsResponse.ProductInformation();
            info.setDocumentType(documentType);
            product.setProductInformation(info);
        }

        // Extra data
        if (extraKeys != null) {
            List<ProductsResponse.ExtraData> extraDataList = new ArrayList<>();
            for (String key : extraKeys) {
                ProductsResponse.ExtraData data = new ProductsResponse.ExtraData();
                data.setKey(key);
                extraDataList.add(data);
            }
            product.setExtraData(extraDataList);
        }

        if (warning != null) {
            product.setCustomerWarning(warning);
        }

        return product;
    }

    private ProductsResponse.Product createProductWithOC(String code, String name, String description, String authority,
                                                         int price, int gst, int tat, String tatUnit,
                                                         String documentId, String ocNumber, boolean isPremium) {
        ProductsResponse.Product product = createProduct(code, name, description, authority, price, gst, tat, tatUnit,
                null, documentId, null, null);

        ProductsResponse.ProductIds ids = product.getProductIds();
        ids.setOwnersCorporationNumber(ocNumber);
        product.setProductIds(ids);

        return product;
    }


    // Explicit Constructor Injection
    public MockServApiService(
            S3Service s3Service,
            UserService userService,
            CompanyMembershipRepository companyMembershipRepository,
            DocumentTypeRepository documentTypeRepository,
            DocumentPriceRepository documentPriceRepository,
            CompanySpecialPriceRepository companySpecialPriceRepository,
            AuthUtils authUtils) {
        this.s3Service = s3Service;
        this.userService = userService;
        this.companyMembershipRepository = companyMembershipRepository;
        this.documentTypeRepository = documentTypeRepository;
        this.documentPriceRepository = documentPriceRepository;
        this.companySpecialPriceRepository = companySpecialPriceRepository;
        this.authUtils = authUtils;
    }

    /**
     * Provides a mock response for the /products endpoint.
     *
     * @param titleId The title ID parameter (optional)
     * @param propertyPfi The property PFI parameter (optional)
     * @return A mock ProductsResponse
     */

    @Transactional(readOnly = true)
    public ResponseEntity<ProductsResponse> getMockOrderProducts(String titleId, String propertyPfi) {
        ProductsResponse response = new ProductsResponse();
        List<ProductsResponse.Product> products = new ArrayList<>();

        String defaultTitleId = titleId != null ? titleId : "10940/843";

        products.add(createProduct("RegisterSearchStatement", "Register Search Statement (Copy of Title)", "PDF version of the copy of the Title...", "Land Registry", 660, 0, 5, "MIN", defaultTitleId, null, null, null));
        products.add(createProduct("CommemorativeTitleCertificateAuthenticA3Unframed", "Commemorative Title Certificate - Authentic", "** Certificate Warning ** ...", "Land Registry", 8073, 734, 10, "DAY", defaultTitleId, null, List.of("postalAddress"), "Please note that Commemorative Title Certificate ..."));
        products.add(createProduct("InstrumentSearch", "Instrument Search", "An instrument is a document referred to on the Register...", "Land Registry", 450, 0, 5, "MIN", null, "AE427859T", null, null, "TRANSFER"));
        products.add(createProduct("InstrumentSearch", "Instrument Search", "An instrument is a document referred to on the Register...", "Land Registry", 450, 0, 5, "MIN", null, "AD597662U", null, null, "AGREEMENT"));
        products.add(createProduct("PropertyTransactionAlert3Months", "Property Transaction Alert (3 months)", "", "Land Registry", 581, 53, 5, "MIN", defaultTitleId, null, null, "Alert on Title orders are subject to additional Property Transaction Alert Service (PTAS) Terms and Conditions (https://uat.landata.online/terms-conditions-information-products/)..."));
        products.add(createProduct("PropertyTransactionAlert6Months", "Property Transaction Alert (6 months)", "", "Land Registry", 581, 53, 5, "MIN", defaultTitleId, null, null, "Alert on Title orders are subject to additional Property Transaction Alert Service (PTAS) Terms and Conditions (https://uat.landata.online/terms-conditions-information-products/)..."));
        products.add(createProduct("PropertyTransactionAlert12Months", "Property Transaction Alert (12 months)", "", "Land Registry", 1163, 106, 5, "MIN", defaultTitleId, null, null, "Alert on Title orders are subject to additional Property Transaction Alert Service (PTAS) Terms and Conditions (https://uat.landata.online/terms-conditions-information-products/)..."));
        products.add(createProduct("FinalSearch", "Dealings on Title", "Any unregistered or registered dealings...", "Land Registry", 320, 0, 5, "MIN", defaultTitleId, null, null, null));
        products.add(createProduct("HistorySearch", "Title History Search Statement", "A Title History Search Statement provides a history of the title, not the property...", "Land Registry", 1110, 0, 5, "MIN", defaultTitleId, null, null, null));
        products.add(createProduct("CopyOfPlan", "Copy of Plan", "Shows the subdivision of an original parcel of land...", "Land Registry", 660, 0, 5, "MIN", null, "PS515587S", null, null));
        products.add(createProduct("DigitalRegisterSearchStatement", "Digital Register Search Statement", "", "Land Registry", 1243, 53, 5, "MIN", defaultTitleId, null, null, "Please note that the Digital Register Search Statement is only a data population and not consider a Title certificate."));

        // Owners Corporation Reports
        for (String num : List.of("1", "2", "4")) {
            products.add(createProductWithOC("OwnersCorporationBasicReport", "Owners Corporation Basic Report", "The Basic Owners Corporation Report includes details...", "Land Registry", 450, 0, 5, "MIN", "PS515587S", num, false));
            products.add(createProductWithOC("OwnersCorporationPremiumReport", "Owners Corporation Premium Report", "The Basic Owners Corporation Report includes details...\n\nPremium", "Land Registry", 812, 74, 5, "MIN", "PS515587S", num, true));
        }

        response.setProducts(products);
        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    public ResponseEntity<CreateOrderResponse> getMockCreateOrder(CreateOrderRequest request) {
        CreateOrderResponse response = new CreateOrderResponse();

        // Set order details
        response.setOrderId("10219435");
        response.setTotalPrice(891);
        response.setTotalGst(89);

        // Create fees
        List<CreateOrderResponse.Fee> fees = new ArrayList<>();
        CreateOrderResponse.Fee fee = new CreateOrderResponse.Fee();
        fee.setType("");
        fee.setAmount(891);
        fee.setGst(new BigDecimal("89"));
        fee.setFeeDetails(Collections.emptyMap());
        fees.add(fee);
        response.setFees(fees);

        // Create items ordered
        List<CreateOrderResponse.ItemOrdered> itemsOrdered = new ArrayList<>();
        CreateOrderResponse.ItemOrdered item = new CreateOrderResponse.ItemOrdered();
        item.setOrderItemNumber(1);
        item.setProductCode("RegisterSearchStatement");

        CreateOrderResponse.ProductIds productIds = new CreateOrderResponse.ProductIds();
        productIds.setTitleId("10940/843");
        item.setProductIds(productIds);

        item.setProductName("Register Search Statement (Copy of Title)");

        CreateOrderResponse.ProductInformation productInfo =
                new CreateOrderResponse.ProductInformation();
        productInfo.setDocumentType("MORTGAGE");
        item.setProductInformation(productInfo);

        item.setAuthorityName("Victorian Land Title Registry");
        item.setPrice(new BigDecimal("891"));
        item.setGst(new BigDecimal("89"));

        itemsOrdered.add(item);
        response.setItemsOrdered(itemsOrdered);

        // Generate and upload PDF
        generateAndUploadPdf(response);

        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    private void generateAndUploadPdf(CreateOrderResponse response) {
        PDDocument document = new PDDocument();
        PDPage page = new PDPage();
        document.addPage(page);

        try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
            contentStream.beginText();
            contentStream.setFont(PDType1Font.HELVETICA_BOLD, 12);
            contentStream.newLineAtOffset(100, 700);
            contentStream.showText("Order ID: " + response.getOrderId());
            contentStream.newLineAtOffset(0, -15);
            contentStream.showText("Total Price: " + response.getTotalPrice());
            contentStream.newLineAtOffset(0, -15);
            contentStream.showText("Total GST: " + response.getTotalGst());
            contentStream.endText();
        } catch (IOException e) {
            e.printStackTrace();
        }

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try {
            document.save(byteArrayOutputStream);
            document.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

        byte[] pdfData = byteArrayOutputStream.toByteArray();

        try {
            String fileKey = "order-documents/" + response.getOrderId() + ".pdf";
            s3Service.uploadFile(pdfData, fileKey);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Pair<BigDecimal, BigDecimal> applyPricingLogic(
            String productCode,
            Long companyId,
            BigDecimal basePrice,
            LocalDateTime today,
            BigDecimal docEffectiveBasePrice,
            BigDecimal docEffectivePriceGst) {
        // Find the DocumentPrice for the productCode
        Optional<DocumentPrice> documentPriceOpt = documentPriceRepository
                .findFirstByProductCode(productCode)
                .filter(price -> Boolean.TRUE.equals(price.getIsActive())
                        && DateUtils.isWithinEffectivePeriod(today, price.getEffectiveDate(), price.getExpiryDate()));

        BigDecimal finalPrice;
        BigDecimal gstAmount;

        if (documentPriceOpt.isEmpty()) {
            finalPrice = docEffectiveBasePrice != null ? docEffectiveBasePrice : basePrice;
            gstAmount = docEffectivePriceGst != null ? docEffectivePriceGst : BigDecimal.ZERO;
            return Pair.of(finalPrice, gstAmount);
        }

        DocumentPrice documentPrice = documentPriceOpt.get();

        if (companyId != null) {
            Optional<CompanySpecialPrice> specialPriceOpt = companySpecialPriceRepository
                    .findFirstByCompanyIdAndDocumentPriceId(companyId, documentPrice.getId())
                    .filter(sp -> Boolean.TRUE.equals(sp.getIsActive())
                            && DateUtils.isWithinEffectivePeriod(today, documentPrice.getEffectiveDate(), documentPrice.getExpiryDate()));

            if (specialPriceOpt.isPresent()) {
                CompanySpecialPrice sp = specialPriceOpt.get();
                finalPrice = sp.getSpecialPrice();
                gstAmount = sp.getSpecialPriceGst();
                return Pair.of(finalPrice, gstAmount);
            }
        }

        finalPrice = docEffectiveBasePrice != null ? docEffectiveBasePrice : basePrice;
        gstAmount = documentPrice.getEffectivePriceGst();

        return Pair.of(finalPrice, gstAmount);
    }

    public BigDecimal calculateGst(BigDecimal price, BigDecimal previousGst) {
        if (price == null) {
            return previousGst != null ? previousGst : BigDecimal.ZERO;
        }
        // Assuming a GST rate of 10% as a fallback, since no gstRate field exists
        BigDecimal gstRate = new BigDecimal("10");
        return price.multiply(gstRate).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
    }

    /**
     * Provides a mock response for the /getTitle endpoint. This method directly populates the
     * TitleResponse object with sample data.
     *
     * @return A mocked TitleResponse object.
     */
    public ResponseEntity<TitleResponse> getMockTitleResponse() {
        TitleResponse response = new TitleResponse();
        System.out.println("Mock Title Response: " + response);
        TitleResponse.TitleDTO titleDTO = new TitleResponse.TitleDTO();
        titleDTO.setTitleId("qp0gjuieZ5VzbNKtT/eZvqPryobRkEYxMA==");
        titleDTO.setTitleStatus(TitleStatus.ACTIVE);
        titleDTO.setTitleType(TitleType.FREEHOLD);
        titleDTO.setCopyOfTitleType(CopyOfTitleType.ELECTRONIC);
        titleDTO.setTitleStreetAddress("UNIT 328 LEVEL 3 800 SWANSTON STREET CARLTON VIC 3053");
        titleDTO.setLandDescriptionText("Lot 3A2 on Plan of Subdivision 515587S.");

        TitleResponse.TitleReferenceDTO titleReferenceDTO = new TitleResponse.TitleReferenceDTO();
        titleReferenceDTO.setVolume(0);
        titleReferenceDTO.setFolio(0);
        titleReferenceDTO.setSuffix("");
        titleDTO.setTitleReference(titleReferenceDTO);

        TitleResponse.LandParcelDTO landParcelDTO = new TitleResponse.LandParcelDTO();
        landParcelDTO.setParcelIdentifier("3A2\\PS515587");
        landParcelDTO.setParcelType(ParcelType.LOT);
        landParcelDTO.setLotType(LotType.RESTRICTED);
        landParcelDTO.setParcelStatus(ParcelStatus.ACTIVE);
        titleDTO.setLandParcels(Arrays.asList(landParcelDTO));

        TitleResponse.TitlePropertyDTO titlePropertyDTO = new TitleResponse.TitlePropertyDTO();
        titlePropertyDTO.setPropertyPfi(208045633L);
        titlePropertyDTO.setIsMultiAssess(true);
        titlePropertyDTO.setPropertyStatus(PropertyStatus.ACTIVE);
        titleDTO.setProperties(Arrays.asList(titlePropertyDTO));

        response.setTitleSummaries(Arrays.asList(titleDTO));

        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    /**
     * Provides a mock response for the /getProprietors endpoint. This method directly populates the
     * ProprietorsResponse object with sample data.
     *
     * @return A mocked ProprietorsResponse object.
     */
    public ResponseEntity<ProprietorsResponse> getMockProprietorsResponse() {
        ProprietorsResponse response = new ProprietorsResponse();

        ProprietorsResponse.ProprietorSummary.TitleDetails titleDetails =
                new ProprietorsResponse.ProprietorSummary.TitleDetails();
        titleDetails.setTitleId("qp0gjuieZ5VzbNKtT/eZvqPryobRkEYxMA==");
        titleDetails.setTitleStatus(TitleStatus.ACTIVE);
        titleDetails.setTitleType(TitleType.FREEHOLD);
        titleDetails.setLandDescriptionText("Lot 3A2 on Plan of Subdivision 515587S.");
        titleDetails.setMunicipality("GREATER BENDIGO");

        ProprietorsResponse.ProprietorSummary proprietorSummary =
                new ProprietorsResponse.ProprietorSummary();
        proprietorSummary.setLastName("");
        proprietorSummary.setGivenName("");
        proprietorSummary.setCompanyName("");
        proprietorSummary.setTitleDetails(titleDetails);

        response.setProprietorSummaries(List.of(proprietorSummary));

        ProprietorsResponse.Pagination pagination = new ProprietorsResponse.Pagination();
        pagination.setPageSize(0);
        pagination.setNextPage(2);
        response.setPagination(pagination);

        return new ResponseEntity<>(response, HttpStatus.OK);
    }

    public ResponseEntity<OrderItemResponse> getMockServOrderItem() {
        OrderItemResponse mock = new OrderItemResponse();
        mock.setOrderId("10219435");
        mock.setOrderItemNumber(1);
        mock.setProductName("Register Search Statement (Copy of Title)");
        mock.setProductCode("RegisterSearchStatement");
        mock.setCustomerReference("10219435");
        mock.setResourceText("Register Search Statement (Copy of Title)");
        mock.setResourceJson("Register Search Statement (Copy of Title)");
        mock.setResourceExpiry("2024-11-07T04:43:46.564302+11:00");
        mock.setResourceFileType("application/pdf");
        mock.setProductStatus(ProductStatus.COMPLETE.name());
        mock.setResourceLocation(
                "https://www.infotrack.com.au/wp-content/uploads/VIC-Title-Search-Sample.pdf?utm_source=chatgpt.com");
        mock.setResourceFileSize(1897);
        return new ResponseEntity<>(mock, HttpStatus.OK);
    }
}
