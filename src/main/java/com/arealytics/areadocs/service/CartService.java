package com.arealytics.areadocs.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.arealytics.areadocs.domain.OrderItem;
import com.arealytics.areadocs.domain.User;
import com.arealytics.areadocs.dto.requestDTO.CartRequestDTO;
import com.arealytics.areadocs.dto.responseDTO.CartResponseDTO;
import com.arealytics.areadocs.dto.responseDTO.serv.ProductsResponse;
import com.arealytics.areadocs.enumeration.FolderType;
import com.arealytics.areadocs.enumeration.OrderItemStatus;
import com.arealytics.areadocs.enumeration.UserType;
import com.arealytics.areadocs.exception.ResourceNotFoundException;
import com.arealytics.areadocs.mapper.OrderItemMapper;
import com.arealytics.areadocs.repository.OrderItemRepository;
import com.arealytics.areadocs.util.AuthUtils;
import com.arealytics.areadocs.util.ServQueryBuilder;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/** Service for managing shopping cart operations */
@Service
@RequiredArgsConstructor
@Slf4j
public class CartService {
    private final OrderItemRepository orderItemRepository;
    private final ServApiService servApiService;
    private final AuthUtils authUtils;
    private final OrderItemMapper orderItemMapper;
    private final FolderService folderService;

    /**
     * Get current user's cart
     *
     * @return CartResponseDTO containing cart information
     */
    @Transactional(readOnly = true)
    public CartResponseDTO getCart() {
        log.debug("Fetching cart for current user");
        User currentUser = authUtils.getCurrentUser();

        if (currentUser == null) {
            log.error("Current user is null - authentication may have failed");
            throw new IllegalStateException("User not authenticated or not found in database");
        }

        List<OrderItem> cartItems =
                orderItemRepository.findByUserAndStatus(currentUser, OrderItemStatus.CART);
        return orderItemMapper.toCartResponseDTO(cartItems, currentUser.getId());
    }

    /**
     * Add items to cart
     *
     * @param cartItems List of cart items to add
     * @return CartResponseDTO with updated cart info
     */
    @Transactional
    public CartResponseDTO addToCart(List<CartRequestDTO> cartItems) {
        log.debug("Adding {} items to cart", cartItems.size());
        User currentUser = authUtils.getCurrentUser();
        List<OrderItem> cartItemsList = new ArrayList<>();

        for (CartRequestDTO item : cartItems) {
            if (!isValidCartItem(item)) {
                log.warn("Invalid cart request: {}", item);
                continue;
            }

            try {
                OrderItem orderItem = addToCart(item, currentUser);
                cartItemsList.add(orderItem);
            } catch (Exception e) {
                log.error(
                        "Error processing cart request {}: {}",
                        item.getProductCode(),
                        e.getMessage(),
                        e);
            }
        }

        // Updated return statement to properly return CartResponseDTO instead of CartItemDTO
        List<OrderItem> currentCartItems =
                orderItemRepository.findByUserAndStatus(currentUser, OrderItemStatus.CART);
        log.debug("Added {} items to cart successfully", cartItemsList.size());
        return orderItemMapper.toCartResponseDTO(currentCartItems, currentUser.getId());
    }

    /**
     * Remove item from cart
     *
     * @param orderItemId ID of the order item to remove
     * @return Updated cart information
     */
    @Transactional
    public CartResponseDTO removeFromCart(Long orderItemId) {
        log.debug("Removing item {} from cart", orderItemId);
        User currentUser = authUtils.getCurrentUser();

        OrderItem orderItem =
                orderItemRepository
                        .findByIdAndUserAndStatus(orderItemId, currentUser, OrderItemStatus.CART)
                        .orElseThrow(
                                () ->
                                        new ResourceNotFoundException(
                                                "Order item not found or not in cart"));

        orderItemRepository.delete(orderItem);

        List<OrderItem> remainingItems =
                orderItemRepository.findByUserAndStatus(currentUser, OrderItemStatus.CART);
        return orderItemMapper.toCartResponseDTO(remainingItems, currentUser.getId());
    }

    /**
     * Clear all items from user's cart
     *
     * @return Empty cart response
     */
    @Transactional
    public CartResponseDTO clearCart() {
        log.debug("Clearing cart for current user");
        User currentUser = authUtils.getCurrentUser();

        List<OrderItem> cartItems =
                orderItemRepository.findByUserAndStatus(currentUser, OrderItemStatus.CART);
        orderItemRepository.deleteAll(cartItems);

        return CartResponseDTO.builder()
                .userId(currentUser.getId())
                .totalAmount(BigDecimal.ZERO)
                .itemCount(cartItems.size())
                .items(List.of())
                .build();
    }

    /**
     * Validate cart request
     *
     * @param request Cart request to validate
     * @return true if valid, false otherwise
     */
    private boolean isValidCartItem(CartRequestDTO request) {
        if (request == null) {
            return false;
        }

        if (request.getProductCode() == null || request.getProductCode().isEmpty()) {
            log.warn("Missing product code in cart request");
            return false;
        }

        if (!Boolean.TRUE.equals(request.getIsWarningAcknowledged())) {
            log.warn("User did not acknowledge warning for product: {}", request.getProductCode());
            return false;
        }

        return true;
    }

    /**
     * Process a single cart cartItem
     *
     * @param cartItem Cart cartItem
     * @param currentUser Current user
     * @return Processed OrderItem or null if failed
     */
    private OrderItem addToCart(CartRequestDTO cartItem, User currentUser) {
        try {
            // Fetch product details
            ProductsResponse.Product productData =
                    fetchProductDetails(cartItem.getProductCode(), cartItem.getPropertyPfi());
            if (productData == null) {
                return null;
            }

            // Save the document in a folder
            setDefaultFolderIfSpecificFolderIsNotMentioned(cartItem, currentUser);

            // Check if product is already in cart
            Optional<OrderItem> existingCartItem =
                    orderItemRepository.findByUserAndProductCodeAndStatus(
                            currentUser, cartItem.getProductCode(), OrderItemStatus.CART);

            OrderItem orderItem =
                    existingCartItem.orElseGet(
                            () ->
                                    orderItemMapper.createOrderItemFromProduct(
                                            productData, currentUser, cartItem));
            return orderItemRepository.save(orderItem);
        } catch (Exception e) {
            log.error(
                    "Error processing cart cartItem {}: {}",
                    cartItem.getProductCode(),
                    e.getMessage(),
                    e);
            throw e;
        }
    }

    /**
     * Fetch product data from backend service
     *
     * @param productCode Product code
     * @param propertyPfi Property PFI
     * @return Product data or null if not found
     */
    private ProductsResponse.Product fetchProductDetails(String productCode, String propertyPfi) {
        try {
            ProductsResponse response =
                    servApiService
                            .getProducts(
                                    ServQueryBuilder.buildProductsSearchQuery(
                                            null, propertyPfi, null),
                                    ProductsResponse.class)
                            .getBody();

            if (response == null
                    || response.getProducts() == null
                    || response.getProducts().isEmpty()) {
                log.warn("Empty product response for product code: {}", productCode);
                return null;
            }

            return response.getProducts().getFirst();
        } catch (Exception e) {
            log.error("Error fetching price for product {}: {}", productCode, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Ensure folder ID is valid, use default if null
     *
     * @param request Cart request to validate
     */
    private void setDefaultFolderIfSpecificFolderIsNotMentioned(CartRequestDTO request, User user) {
        // TODO:- No matter what the folder ID is, we will download to the downloads folder
        if (user.getUserType().equals(UserType.COMPANY)) {
            // Try to get the company's Downloads folder
            folderService
                    .getFoldersByCompanyId(user.getCompanyMemberships().getCompany().getId())
                    .stream()
                    .filter(f -> f.getFolderType() == FolderType.ROOT)
                    .findFirst()
                    .ifPresentOrElse(
                            f -> {
                                request.setFolderId(f.getId());
                                log.debug("Using company Downloads folder: {}", f.getId());
                            },
                            () -> log.warn("Company has no Downloads folder"));
        } else {
            // Fallback to user's Downloads folder
            folderService.getFoldersByUserId(user.getId()).stream()
                    .filter(f -> f.getFolderType() == FolderType.ROOT)
                    .findFirst()
                    .ifPresentOrElse(
                            f -> {
                                request.setFolderId(f.getId());
                                log.debug("Using user Downloads folder: {}", f.getId());
                            },
                            () -> log.warn("User {} has no Downloads folder", user.getId()));
        }
    }
}
