package com.arealytics.areadocs.dto.requestDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * DTO for DocumentSource creation and update requests. Audit fields are not included as they are
 * managed by JPA auditing.
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class DocumentSourceRequestDTO extends BaseRequestDTO {
    private String name;
    private String baseUrl;
    private String status;
}
