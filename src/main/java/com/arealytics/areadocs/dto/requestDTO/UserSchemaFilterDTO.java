package com.arealytics.areadocs.dto.requestDTO;

import com.arealytics.areadocs.enumeration.UserType;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserSchemaFilterDTO {
    private String email;
    private String firstName;
    private String lastName;
    private String contactNumber;
    private String city;
    private String country;
    private UserType userType;
}
