package com.arealytics.areadocs.dto.requestDTO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CompanyFilterDTO {
    private String name;
    private String ABN;
    private String ACN;
    private String industry;
    private Integer minEmployeeCount;
    private Integer maxEmployeeCount;
    private String accountsContactName;
    private String accountsContactNumber;
    private String billingEmail;
}
