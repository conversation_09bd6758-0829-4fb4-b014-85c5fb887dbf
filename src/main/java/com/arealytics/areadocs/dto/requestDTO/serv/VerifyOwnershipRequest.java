package com.arealytics.areadocs.dto.requestDTO.serv;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class VerifyOwnershipRequest {

    @JsonProperty("identifier")
    private Identifier identifier;

    @JsonProperty("searchNames")
    private List<SearchName> searchNames;

    @Data
    @NoArgsConstructor
    public static class Identifier {

        @JsonProperty("propertyAddress")
        private PropertyAddress propertyAddress;
    }

    @Data
    @NoArgsConstructor
    public static class PropertyAddress {

        @JsonProperty("unitFlatNumber")
        private String unitFlatNumber;

        @JsonProperty("streetNumber")
        private String streetNumber;

        @JsonProperty("streetName")
        private String streetName;

        @JsonProperty("streetType")
        private String streetType;

        @JsonProperty("streetSuffix")
        private String streetSuffix;

        @JsonProperty("suburbTownLocality")
        private String suburbTownLocality;

        @JsonProperty("postcode")
        private int postcode;
    }

    @Data
    @NoArgsConstructor
    public static class SearchName {

        @JsonProperty("searchNameId")
        private int searchNameId;

        @JsonProperty("individual")
        private Individual individual;
    }

    @Data
    @NoArgsConstructor
    public static class Individual {

        @JsonProperty("surname")
        private String surname;

        @JsonProperty("givenNames")
        private String givenNames;
    }
}
