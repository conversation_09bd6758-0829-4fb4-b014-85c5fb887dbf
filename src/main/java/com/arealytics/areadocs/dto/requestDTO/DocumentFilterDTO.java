package com.arealytics.areadocs.dto.requestDTO;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DocumentFilterDTO {
    private Long documentId;
    private Long folderId;
    private Long sourceId;
    private Long categoryId;
    private Long externalId;
    private String title;
    private String description;
    private String documentType;
    private BigDecimal minBasePrice;
    private BigDecimal maxBasePrice;
    private BigDecimal minFinalPrice;
    private BigDecimal maxFinalPrice;
    private Integer minAccessCount;
    private Integer maxAccessCount;
    private Boolean createdInLastDay;

}
