package com.arealytics.areadocs.dto.requestDTO;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class OrderRequestDTO {

    @NotNull(message = "The product list cannot be null.") @Size(min = 1, message = "At least one Item must be provided.") @Valid @JsonProperty("orderItemIds")
    private List<@Valid Long> orderItemIds;
}
