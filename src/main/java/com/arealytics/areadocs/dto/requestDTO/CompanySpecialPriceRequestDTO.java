package com.arealytics.areadocs.dto.requestDTO;

import java.math.BigDecimal;

import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CompanySpecialPriceRequestDTO {

    @NotNull(message = "Company ID is required") @Positive(message = "Company ID must be positive") private Long companyId;

    @NotNull(message = "Special price is required") @DecimalMin(value = "0.00", inclusive = false, message = "Special price must be greater than 0") @Digits(
            integer = 8,
            fraction = 2,
            message = "Special price must have up to 8 integer digits and 2 decimal places")
    private BigDecimal specialPrice;
}
