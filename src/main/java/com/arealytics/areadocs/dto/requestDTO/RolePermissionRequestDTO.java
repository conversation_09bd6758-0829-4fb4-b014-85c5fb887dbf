package com.arealytics.areadocs.dto.requestDTO;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RolePermissionRequestDTO extends BaseRequestDTO {
    @NotNull(message = "Role ID is required") private Long roleId;

    @NotNull(message = "Permission ID is required") private Long permissionId;
}
