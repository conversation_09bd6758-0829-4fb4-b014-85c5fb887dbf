package com.arealytics.areadocs.dto.requestDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * DTO for CompanyMembership creation and update requests. Audit fields are not included as they are
 * managed by JPA auditing.
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CompanyMembershipRequestDTO extends BaseRequestDTO {
    private Long userId;
    private Long companyId;
    private Long roleId;
}
