package com.arealytics.areadocs.dto.requestDTO.serv;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CreateOrderRequest {

    @JsonProperty("products")
    private List<Product> products;

    @JsonProperty("deliveryPreference")
    private String deliveryPreference;

    @JsonProperty("email")
    private String email;

    @JsonProperty("contactName")
    private String contactName;

    @JsonProperty("contactNumber")
    private String contactNumber;

    @JsonProperty("pdfData")
    private byte[] pdfData;

    @JsonProperty("orderId")
    private String orderId;

    @Data
    @NoArgsConstructor
    public static class Product {

        @JsonProperty("productCode")
        private String productCode;

        @JsonProperty("productIds")
        private ProductIds productIds;

        @JsonProperty("isWarningAcknowledged")
        private boolean isWarningAcknowledged;

        @JsonProperty("extraData")
        private ExtraData extraData;

        @JsonProperty("prerequisites")
        private List<Prerequisite> prerequisites;
    }

    @Data
    @NoArgsConstructor
    public static class ProductIds {
        @JsonProperty("titleId")
        private String titleId;
    }

    @Data
    @NoArgsConstructor
    public static class ExtraData {

        @JsonProperty("settlementDate")
        private String settlementDate;

        @JsonProperty("meterReadDate")
        private String meterReadDate;

        @JsonProperty("waterShareId")
        private String waterShareId;

        @JsonProperty("landUseApplicationReason")
        private String landUseApplicationReason;

        @JsonProperty("postalAddress")
        private PostalAddress postalAddress;

        @JsonProperty("vendors")
        private List<Vendor> vendors;

        @JsonProperty("purchasers")
        private List<Purchaser> purchasers;

        @JsonProperty("ratesRequestDate")
        private String ratesRequestDate;

        @JsonProperty("salePrice")
        private int salePrice;

        @JsonProperty("ownersCorpManagerData")
        private OwnersCorpManagerData ownersCorpManagerData;

        @JsonProperty("wisNumber")
        private int wisNumber;

        @JsonProperty("deliverySpeed")
        private String deliverySpeed;

        @JsonProperty("parcel")
        private String parcel;
    }

    @Data
    @NoArgsConstructor
    public static class PostalAddress {
        @JsonProperty("recipientName")
        private String recipientName;

        @JsonProperty("companyName")
        private String companyName;

        @JsonProperty("contactNumber")
        private String contactNumber;

        @JsonProperty("email")
        private String email;

        @JsonProperty("streetAddress")
        private String streetAddress;

        @JsonProperty("suburb")
        private String suburb;

        @JsonProperty("state")
        private String state;

        @JsonProperty("postcode")
        private int postcode;
    }

    @Data
    @NoArgsConstructor
    public static class Vendor {
        @JsonProperty("vendorType")
        private String vendorType;

        @JsonProperty("firstName")
        private String firstName;

        @JsonProperty("middleName")
        private String middleName;

        @JsonProperty("lastName")
        private String lastName;

        @JsonProperty("companyName")
        private String companyName;
    }

    @Data
    @NoArgsConstructor
    public static class Purchaser {
        @JsonProperty("purchaserType")
        private String purchaserType;

        @JsonProperty("firstName")
        private String firstName;

        @JsonProperty("middleName")
        private String middleName;

        @JsonProperty("lastName")
        private String lastName;

        @JsonProperty("companyName")
        private String companyName;
    }

    @Data
    @NoArgsConstructor
    public static class OwnersCorpManagerData {
        @JsonProperty("managerId")
        private int managerId;

        @JsonProperty("managerName")
        private String managerName;

        @JsonProperty("address")
        private String address;

        @JsonProperty("phone")
        private String phone;
    }

    @Data
    @NoArgsConstructor
    public static class Prerequisite {
        @JsonProperty("productCode")
        private String productCode;

        @JsonProperty("productIds")
        private ProductIds productIds;
    }

    public String getOrderId() {
        return this.orderId;
    }
}
