package com.arealytics.areadocs.dto.requestDTO;

import java.util.List;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * DTO for Role creation and update requests. Audit fields are not included as they are managed by
 * JPA auditing.
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RoleRequestDTO extends BaseRequestDTO {

    @NotBlank(message = "Role name is required") @Size(max = 100, message = "Role name cannot exceed 100 characters") private String name;

    private String description;

    private List<Long> permissionIds;

    private String displayText;

    public String setName(String name) {
        return this.name = name.trim();
    }
}
