package com.arealytics.areadocs.dto.requestDTO;

import com.arealytics.areadocs.util.StringMatchingUtil;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * DTO for Company creation and update requests. Audit fields are not included as they are managed
 * by JPA auditing.
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CompanyRequestDTO extends BaseRequestDTO {
    @NotBlank(message = "Company name is required") @Size(max = 255, message = "Company name cannot exceed 255 characters") private String name;

    private String ABN;

    private String ACN;

    private String industry;

    private String description;

    private String website;

    private Integer employeeCount;

    private Boolean isBillingPrimary;

    @NotBlank(message = "Billing Email is required") @Email(message = "Email should be valid") private String billingEmail;

    private String accountsContactNumber;

    private String accountsContactName;

    @NotNull(message = "Primary address is required") @Valid private AddressRequestDTO primaryAddress;

    @NotNull(message = "Billing address is required") @Valid private AddressRequestDTO billingAddress;

    // check fo validation null and make it normalize
    public void setBillingEmail(String billingEmail) {
        if (billingEmail != null) {
            this.billingEmail = StringMatchingUtil.normalizeString(billingEmail);
        } else {
            this.billingEmail = null;
        }
    }

    public void setName(String name) {
        this.name = StringMatchingUtil.normalizeString(name);
    }

    public void setAccountsContactName(String accountsContactName) {
        if (accountsContactName != null) {
            this.accountsContactName = StringMatchingUtil.normalizeString(accountsContactName);
        } else {
            this.accountsContactName = null;
        }
    }
}
