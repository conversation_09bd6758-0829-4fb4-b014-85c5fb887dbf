package com.arealytics.areadocs.dto.requestDTO;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class UserRoleRequestDTO extends BaseRequestDTO {
    @NotNull(message = "User ID is required") private Long userId;

    @NotNull(message = "Role ID is required") private Long roleId;
}
