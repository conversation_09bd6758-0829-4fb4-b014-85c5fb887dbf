package com.arealytics.areadocs.dto.requestDTO;

import com.arealytics.areadocs.enumeration.FolderType;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * DTO for DocumentFolder creation and update requests. Audit fields are not included as they are
 * managed by JPA auditing.
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class FolderRequestDTO extends BaseRequestDTO {
    @NotBlank(message = "Folder name is required") @Size(max = 255, message = "Folder name must not exceed 255 characters") @Pattern(regexp = "^[^/\\\\:*?\"<>|]*$", message = "Folder name contains invalid characters") private String name;

    private Long parentFolderId;

    private FolderType folderType = FolderType.SHARED;

    public String setName(String name) {
        return this.name = name.trim();
    }
}
