package com.arealytics.areadocs.dto.requestDTO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DocumentPriceRequestDTO {

    @Positive(message = "Document type ID must be positive") private Long documentTypeId;

    @NotBlank(message = "Product code is required") @Size(max = 50, message = "Product code must not exceed 50 characters") private String productCode;

    @NotBlank(message = "Name is required") @Size(max = 100, message = "Name must not exceed 100 characters") private String name;

    @Size(max = 255, message = "Description must not exceed 255 characters") private String description;

    @NotNull(message = "Base price is required") @DecimalMin(value = "0.00", inclusive = false, message = "Base price must be greater than 0") @Digits(
            integer = 8,
            fraction = 2,
            message = "Base price must have up to 8 integer digits and 2 decimal places")
    private BigDecimal basePrice;

    @DecimalMin(value = "0.00", message = "Effective base price must be non-negative") @Digits(
            integer = 3,
            fraction = 2,
            message = "Effective base price must have up to 3 integer digits and 2 decimal places")
    private BigDecimal effectiveBasePrice;

    @NotNull(message = "Effective date is required") @PastOrPresent(message = "Effective date must be in the past or present") private LocalDateTime effectiveDate;

    @FutureOrPresent(message = "Expiry date must be in the present or future") private LocalDateTime expiryDate;

    private BigDecimal effectivePriceGst;
}
