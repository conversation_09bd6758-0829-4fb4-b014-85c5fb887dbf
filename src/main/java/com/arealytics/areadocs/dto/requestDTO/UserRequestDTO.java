package com.arealytics.areadocs.dto.requestDTO;

import com.arealytics.areadocs.enumeration.UserType;

import jakarta.annotation.Nullable;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * DTO for User creation and update requests. Audit fields are not included as they are managed by
 * JPA auditing.
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class UserRequestDTO extends BaseRequestDTO {
    @NotBlank(message = "Email is required") @Email(message = "Email should be valid") private String email;

    private String password;

    @NotBlank(message = "First name is required") private String firstName;

    private String middleName;

    @NotBlank(message = "Last name is required") private String lastName;

    private Boolean isActive;

    @NotNull(message = "User type is required") private UserType userType;

    private Boolean isBillingPrimary;

    // The following fields are not used for user creation but kept for backward compatibility
    private String contactNumber;

    private String bio;

    @NotNull(message = "Role ID is required") private Long roleId;

    // Company related fields
    private Long companyId;

    @NotNull(message = "Primary address is required") @Valid private AddressRequestDTO primaryAddress;

    @NotNull(message = "Billing address is required") @Valid private AddressRequestDTO billingAddress;

    private Boolean removeProfilePicture;

    @Nullable private String profilePictureUrl;
}
