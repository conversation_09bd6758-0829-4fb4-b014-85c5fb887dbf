package com.arealytics.areadocs.dto.requestDTO;

import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * DTO for Document creation and update requests. Audit fields are not included as they are managed
 * by JPA auditing.
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class DocumentUpdateDTO extends BaseRequestDTO {
    @Size(max = 255, message = "Title cannot exceed 255 characters") private String title;

    private String description;

    private Long folderId;
}
