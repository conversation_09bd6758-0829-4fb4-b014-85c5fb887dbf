package com.arealytics.areadocs.dto.requestDTO;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class AddressRequestDTO extends BaseRequestDTO {

    @NotNull(message = "Address Line 1 is required") private String addressLine1;

    private String addressLine2;

    private String suburb;

    @NotNull(message = "State ID is required") private Long stateId;

    @NotNull(message = "Zip code ID is required") private Long zipCodeId;
}
