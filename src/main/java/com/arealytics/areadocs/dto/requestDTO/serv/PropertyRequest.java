package com.arealytics.areadocs.dto.requestDTO.serv;

import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class PropertyRequest {

    @Pattern(regexp = "^[0-9]{1,20}$", message = "propertyPfi must be 1 to 20 digits") private String propertyPfi;

    @Size(min = 1, max = 20, message = "propertyNumber must be between 1 and 20 characters") @Pattern(regexp = "^[0-9]+$", message = "propertyNumber must contain digits only") private String propertyNumber;

    private String municipalityName;

    @Size(max = 255, message = "eziAddress must not exceed 255 characters") private String eziAddress;
}
