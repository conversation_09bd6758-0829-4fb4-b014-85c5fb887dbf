package com.arealytics.areadocs.dto.requestDTO;

import java.math.BigDecimal;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** DTO for wallet recharge request. */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WalletRechargeRequestDTO {

    /** The amount to recharge. */
    @NotNull(message = "Amount is required") @DecimalMin(value = "0.01", message = "Amount must be greater than 0") private BigDecimal amount;

    /** The currency of the amount. */
    private String currency = "AUD";

    // TODO: Add payment method details for Stripe integration
}
