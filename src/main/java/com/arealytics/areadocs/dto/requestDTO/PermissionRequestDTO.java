package com.arealytics.areadocs.dto.requestDTO;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * DTO for Permission creation and update requests. Audit fields are not included as they are
 * managed by JPA auditing.
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class PermissionRequestDTO extends BaseRequestDTO {

    @NotBlank(message = "Permission code is required") @Size(max = 50, message = "Permission code cannot exceed 50 characters") private String permissionCode;

    @NotBlank(message = "Permission name is required") @Size(max = 100, message = "Permission name cannot exceed 100 characters") private String name;

    @Size(max = 255, message = "Permission description cannot exceed 255 characters") private String description;

    private String module;
    private String action;

    // Alias for backward compatibility
    public void setPermissionName(String name) {
        this.name = name.trim();
    }
}
