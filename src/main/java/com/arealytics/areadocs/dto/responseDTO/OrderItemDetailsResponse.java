package com.arealytics.areadocs.dto.responseDTO;

import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderItemDetailsResponse {
    private Long id;
    private String productCode;
    private String productName;
    private String documentType;
    private String titleId;
    private BigDecimal price;
    private BigDecimal tax;
    private BigDecimal finalPrice;
    private String status;
    private Long orderId;
    private String orderReference;
}
