package com.arealytics.areadocs.dto.responseDTO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.arealytics.areadocs.enumeration.EntryStatus;
import com.arealytics.areadocs.enumeration.PaymentStatus;
import com.arealytics.areadocs.enumeration.TransactionStatus;
import com.arealytics.areadocs.enumeration.TransactionType;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** DTO for wallet recharge response. */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WalletRechargeResponseDTO {

    /** The ID of the account entry created for the recharge. */
    private Long accountEntryId;

    /** The reference number of the account entry. */
    private String referenceNumber;

    /** The date of the entry. */
    private LocalDate entryDate;

    /** The status of the entry. */
    private EntryStatus status;

    /** The amount recharged. */
    private BigDecimal amount;

    /** The currency of the amount. */
    private String currency;

    /** The new balance of the account. */
    private BigDecimal newBalance;

    /** The description of the recharge. */
    private String description;

    /** The ID of the transaction created for the recharge. */
    private Long transactionId;

    /** The type of the transaction. */
    private TransactionType transactionType;

    /** The date of the transaction. */
    private LocalDateTime transactionDate;

    /** The status of the transaction. */
    private TransactionStatus transactionStatus;

    /** The ID of the payment transaction created for the recharge. */
    private Long paymentTransactionId;

    /** The order ID of the payment transaction. */
    private String orderId;

    /** The transaction ID of the payment transaction. */
    private String paymentTxnId;

    /** The status of the payment transaction. */
    private PaymentStatus paymentStatus;

    /** The timestamp of the payment transaction. */
    private LocalDateTime paymentTimestamp;

    /** The payment method used for the recharge. */
    private String paymentMethod;

    /** The payment gateway used for the recharge. */
    private String paymentGateway;
}
