package com.arealytics.areadocs.dto.responseDTO.serv;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class CreateOrderResponse {

    @JsonProperty("orderId")
    private String orderId;

    @JsonProperty("totalPrice")
    private int totalPrice;

    @JsonProperty("totalGst")
    private int totalGst;

    @JsonProperty("fees")
    private List<Fee> fees;

    @JsonProperty("itemsOrdered")
    private List<ItemOrdered> itemsOrdered;

    // Constructor to accept orderId
    public CreateOrderResponse(String orderId) {
        this.orderId = orderId;
    }

    @Data
    @NoArgsConstructor
    public static class Fee {

        @JsonProperty("type")
        private String type;

        @JsonProperty("amount")
        private int amount;

        @JsonProperty("gst")
        private BigDecimal gst;

        @JsonProperty("feeDetails")
        private Map<String, Object> feeDetails; // Flexible - empty object or key-values
    }

    @Data
    @NoArgsConstructor
    public static class ItemOrdered {

        @JsonProperty("orderItemNumber")
        private int orderItemNumber;

        @JsonProperty("productCode")
        private String productCode;

        @JsonProperty("productIds")
        private ProductIds productIds;

        @JsonProperty("productName")
        private String productName;

        @JsonProperty("productInformation")
        private ProductInformation productInformation;

        @JsonProperty("authorityName")
        private String authorityName;

        @JsonProperty("price")
        private BigDecimal price;

        @JsonProperty("gst")
        private BigDecimal gst;
    }

    @Data
    @NoArgsConstructor
    public static class ProductIds {
        @JsonProperty("titleId")
        private String titleId;
    }

    @Data
    @NoArgsConstructor
    public static class ProductInformation {
        @JsonProperty("documentType")
        private String documentType;
    }
}
