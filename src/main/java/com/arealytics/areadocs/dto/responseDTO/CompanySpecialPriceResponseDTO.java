package com.arealytics.areadocs.dto.responseDTO;

import java.math.BigDecimal;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CompanySpecialPriceResponseDTO extends BaseDTO {

    private Long companyId;

    private DocumentPriceResponseDTO documentPrice;

    private BigDecimal specialPrice;

    private BigDecimal specialPriceGst;
}
