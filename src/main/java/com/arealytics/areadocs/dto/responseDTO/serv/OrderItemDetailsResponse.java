package com.arealytics.areadocs.dto.responseDTO.serv;

import java.time.OffsetDateTime;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class OrderItemDetailsResponse {

    @JsonProperty("orderId")
    private String orderId;

    @JsonProperty("orderItemNumber")
    private int orderItemNumber;

    @JsonProperty("productName")
    private String productName;

    @JsonProperty("productCode")
    private String productCode;

    @JsonProperty("productStatus")
    private String productStatus;

    @JsonProperty("customerReference")
    private String customerReference;

    @JsonProperty("resourceText")
    private String resourceText;

    @JsonProperty("resourceJson")
    private String resourceJson;

    @JsonProperty("resourceLocation")
    private String resourceLocation;

    @JsonProperty("resourceExpiry")
    private OffsetDateTime resourceExpiry;

    @JsonProperty("resourceFileType")
    private String resourceFileType;

    @JsonProperty("resourceFileSize")
    private long resourceFileSize;
}
