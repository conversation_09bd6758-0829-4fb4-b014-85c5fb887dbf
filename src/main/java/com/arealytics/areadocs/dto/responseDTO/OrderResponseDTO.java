package com.arealytics.areadocs.dto.responseDTO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.arealytics.areadocs.enumeration.OrderStatus;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for returning order information to the frontend. This provides a clean, user-friendly
 * response with only the necessary information.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderResponseDTO {

    private Long id;
    private String externalOrderId;
    private LocalDateTime orderDate;
    private BigDecimal totalAmount;
    private BigDecimal totalTax;
    private OrderStatus status;
    private String paymentMethod;
    private List<OrderItemDTO> items;
    private TransactionDTO transaction;

    /** DTO for order item information. */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class OrderItemDTO {
        private Long id;
        private String productCode;
        private String productName;
        private String documentType;
        private String titleId;
        private BigDecimal price;
        private BigDecimal tax;
        private BigDecimal finalPrice;
        private String status;
        private DocumentDTO document;
    }
}
