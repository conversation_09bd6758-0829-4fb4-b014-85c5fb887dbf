package com.arealytics.areadocs.dto.responseDTO.serv;



import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TownshipResponse {
    @JsonProperty("townshipSummaries")
    private List<TownshipSummary> townshipSummaries;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TownshipSummary {
        @JsonProperty("name")
        private String name;

        @JsonProperty("code")
        private String code;
    }
}