package com.arealytics.areadocs.dto.responseDTO.serv;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class GetOrderResponse {

    @JsonProperty("pagination")
    private Pagination pagination;

    @JsonProperty("orders")
    private List<Order> orders;

    @Data
    @NoArgsConstructor
    public static class Pagination {
        @JsonProperty("totalRecords")
        private int totalRecords;

        @JsonProperty("nextPage")
        private int nextPage;
    }

    @Data
    @NoArgsConstructor
    public static class Order {
        @JsonProperty("orderId")
        private String orderId;

        @JsonProperty("orderDate")
        private String orderDate;

        @JsonProperty("totalPrice")
        private int totalPrice;

        @JsonProperty("totalGst")
        private int totalGst;

        @JsonProperty("fees")
        private List<Fee> fees;

        @JsonProperty("deliveryPreference")
        private String deliveryPreference;

        @JsonProperty("email")
        private String email;

        @JsonProperty("contactName")
        private String contactName;

        @JsonProperty("contactNumber")
        private String contactNumber;

        @JsonProperty("customerReference")
        private String customerReference;

        @JsonProperty("products")
        private List<Product> products;
    }

    @Data
    @NoArgsConstructor
    public static class Fee {
        @JsonProperty("type")
        private String type;

        @JsonProperty("amount")
        private int amount;

        @JsonProperty("gst")
        private BigDecimal gst;

        @JsonProperty("feeDetails")
        private Map<String, Object> feeDetails;
    }

    @Data
    @NoArgsConstructor
    public static class Product {
        @JsonProperty("orderItemNumber")
        private int orderItemNumber;

        @JsonProperty("productCode")
        private String productCode;

        @JsonProperty("productIds")
        private ProductIds productIds;

        @JsonProperty("productName")
        private String productName;

        @JsonProperty("productInformation")
        private ProductInformation productInformation;

        @JsonProperty("authorityName")
        private String authorityName;

        @JsonProperty("productStatus")
        private String productStatus;

        @JsonProperty("price")
        private BigDecimal price;

        @JsonProperty("gst")
        private BigDecimal gst;

        @JsonProperty("extraData")
        private Map<String, Object> extraData; // Empty object -> Map

        @JsonProperty("prerequisites")
        private List<Prerequisite> prerequisites;
    }

    @Data
    @NoArgsConstructor
    public static class ProductIds {
        @JsonProperty("titleId")
        private String titleId;
    }

    @Data
    @NoArgsConstructor
    public static class ProductInformation {
        @JsonProperty("titleId")
        private String titleId;
    }

    @Data
    @NoArgsConstructor
    public static class Prerequisite {
        @JsonProperty("productCode")
        private String productCode;

        @JsonProperty("productIds")
        private ProductIds productIds;
    }
}
