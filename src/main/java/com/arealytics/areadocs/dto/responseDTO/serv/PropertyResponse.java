package com.arealytics.areadocs.dto.responseDTO.serv;

import java.util.List;

import com.arealytics.areadocs.enumeration.SERV.LotType;
import com.arealytics.areadocs.enumeration.SERV.ParcelStatus;
import com.arealytics.areadocs.enumeration.SERV.ParcelType;
import com.arealytics.areadocs.enumeration.SERV.PropertyStatus;
import com.arealytics.areadocs.enumeration.SERV.TitleStatus;
import com.arealytics.areadocs.enumeration.SERV.TitleType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PropertyResponse {

    @JsonProperty("propertySummaries")
    private List<PropertyDTO> propertySummaries;

    @Data
    public static class PropertyDTO {

        @JsonProperty("propertyPfi")
        private Long propertyPfi;

        @JsonProperty("propertyStatus")
        private PropertyStatus propertyStatus;

        @JsonProperty("isCouncilPropertyRegistered")
        private Boolean isCouncilPropertyRegistered;

        @JsonProperty("councilPropertyDetails")
        private CouncilPropertyDetailsDTO councilPropertyDetails;

        @JsonProperty("isMultiAssess")
        private Boolean isMultiAssess;

        @JsonProperty("propertyCreatedDate")
        private String propertyCreatedDate;

        @JsonProperty("primaryAddress")
        private AddressDTO primaryAddress;

        @JsonProperty("aliasAddresses")
        private List<AddressDTO> aliasAddresses;

        @JsonProperty("landParcels")
        private List<LandParcelDTO> landParcels;

        @JsonProperty("titles")
        private List<TitleDTO> titles;
    }

    @Data
    public static class CouncilPropertyDetailsDTO {

        @JsonProperty("propertyNumber")
        private Long propertyNumber;

        @JsonProperty("municipalityName")
        private String municipalityName;
    }

    @Data
    public static class AddressDTO {

        @JsonProperty("eziAddress")
        private String eziAddress;

        @JsonProperty("addressDetails")
        private AddressDetailsDTO addressDetails;
    }

    @Data
    public static class AddressDetailsDTO {

        @JsonProperty("buildingName")
        private String buildingName;

        @JsonProperty("unitType")
        private String unitType;

        @JsonProperty("unitNumber")
        private String unitNumber;

        @JsonProperty("levelType")
        private String levelType;

        @JsonProperty("levelNumber")
        private String levelNumber;

        @JsonProperty("streetNumber")
        private Integer streetNumber;

        @JsonProperty("streetName")
        private String streetName;

        @JsonProperty("streetType")
        private String streetType;

        @JsonProperty("streetSuffix")
        private String streetSuffix;

        @JsonProperty("suburbTownLocality")
        private String suburbTownLocality;

        @JsonProperty("postcode")
        private Integer postcode;
    }

    @Data
    public static class LandParcelDTO {

        @JsonProperty("spi")
        private String parcelIdentifier;

        @JsonProperty("parcelType")
        private ParcelType parcelType;

        @JsonProperty("lotType")
        private LotType lotType;

        @JsonProperty("parcelStatus")
        private ParcelStatus parcelStatus;
    }

    @Data
    public static class TitleDTO {

        @JsonProperty("titleId")
        private String titleId;

        @JsonProperty("titleStatus")
        private TitleStatus titleStatus;

        @JsonProperty("titleType")
        private TitleType titleType;
    }
}
