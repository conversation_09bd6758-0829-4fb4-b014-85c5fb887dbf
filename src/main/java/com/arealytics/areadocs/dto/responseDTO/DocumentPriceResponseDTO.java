package com.arealytics.areadocs.dto.responseDTO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class DocumentPriceResponseDTO extends BaseDTO {

    private Long documentTypeId;

    private String name;

    private String description;

    private String productCode;

    private BigDecimal basePrice;

    private BigDecimal effectiveBasePrice;

    private LocalDateTime effectiveDate;

    private LocalDateTime expiryDate;

    private BigDecimal effectivePriceGst;
}
