package com.arealytics.areadocs.dto.responseDTO;

import com.arealytics.areadocs.enumeration.AddressType;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class AddressResponseDTO {
    private String addressLine1;
    private String addressLine2;
    private String suburb;
    private Long stateId;
    private String stateName;
    private String stateAbbr;
    private Long zipCodeId;
    private String zipCode;
    private AddressType addressType; // PRIMARY or BILLING
}
