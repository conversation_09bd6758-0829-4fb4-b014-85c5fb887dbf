package com.arealytics.areadocs.dto.responseDTO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.arealytics.areadocs.enumeration.TransactionStatus;
import com.arealytics.areadocs.enumeration.TransactionType;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TransactionDTO {

    private Long transactionId;
    private TransactionType transactionType;
    private LocalDateTime transactionDate;
    private BigDecimal amount;
    private TransactionStatus status;
    private String referenceId;
    private String paymentMethod;
}
