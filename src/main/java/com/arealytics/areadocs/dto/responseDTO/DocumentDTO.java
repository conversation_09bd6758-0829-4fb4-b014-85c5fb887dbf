package com.arealytics.areadocs.dto.responseDTO;

import java.time.LocalDateTime;
import java.util.Map;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class DocumentDTO extends BaseDTO {
    private Long folderId;
    private String folderName;
    private String title;
    private String description;
    private Long categoryId;
    private String categoryName;
    private String filePath;
    private Map<String, Object> metadata;
    private LocalDateTime lastAccessed;
    private Long accessedCount;
    private LocalDateTime expiryDate;
}
