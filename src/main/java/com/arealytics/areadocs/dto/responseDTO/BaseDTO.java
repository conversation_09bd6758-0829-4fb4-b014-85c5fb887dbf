package com.arealytics.areadocs.dto.responseDTO;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class BaseDTO {
    private Long id;

    // These fields will be included in responses but ignored in requests
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private LocalDateTime createdAt;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private LocalDateTime modifiedAt;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private Boolean isActive;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private Long version;

    // This field is only for soft delete functionality and should never be set manually
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private LocalDateTime deleteAt;
}
