package com.arealytics.areadocs.dto.responseDTO.serv;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class OwnersCorporationManagersResponse {

    @JsonProperty("managers")
    private List<ManagerDTO> managers;

    @Data
    public static class ManagerDTO {

        @JsonProperty("supportedDeliverySpeeds")
        private List<DeliverySpeedDTO> supportedDeliverySpeeds;
    }

    @Data
    public static class DeliverySpeedDTO {

        @JsonProperty("price")
        private Integer price;

        @JsonProperty("gst")
        private Integer gst;

        @JsonProperty("turnaroundTime")
        private Integer turnaroundTime;

        @JsonProperty("extraData")
        private List<ExtraDataDTO> extraData;
    }

    @Data
    public static class ExtraDataDTO {

        @JsonProperty("key")
        private String key;
    }
}
