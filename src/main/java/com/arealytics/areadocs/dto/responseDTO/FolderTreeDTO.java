package com.arealytics.areadocs.dto.responseDTO;

import java.util.ArrayList;
import java.util.List;

import com.arealytics.areadocs.enumeration.FolderType;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class FolderTreeDTO extends BaseDTO {
    private Long id;
    private String name;
    private FolderType folderType;
    private int childCount;
    private List<FolderTreeDTO> children = new ArrayList<>();
}
