package com.arealytics.areadocs.dto.responseDTO.serv;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class VerifyOwnershipResponse {

    @JsonProperty("applicantsMatchResults")
    private List<ApplicantMatchResult> applicantsMatchResults;

    @JsonProperty("proprietorCompleteness")
    private String proprietorCompleteness;

    @JsonProperty("moreThanOneTitle")
    private String moreThanOneTitle;

    @Data
    @NoArgsConstructor
    public static class ApplicantMatchResult {

        @JsonProperty("searchNameId")
        private int searchNameId;

        @JsonProperty("individual")
        private Individual individual;

        @JsonProperty("organisation")
        private Organisation organisation;
    }

    @Data
    @NoArgsConstructor
    public static class Individual {

        @JsonProperty("surname")
        private String surname;

        @JsonProperty("givenNames")
        private String givenNames;

        @JsonProperty("individualMatchType")
        private String individualMatchType;

        @JsonProperty("individualMatchResult")
        private String individualMatchResult;
    }

    @Data
    @NoArgsConstructor
    public static class Organisation {

        @JsonProperty("orgName")
        private String orgName;

        @JsonProperty("organisationMatchResult")
        private String organisationMatchResult;
    }
}
