package com.arealytics.areadocs.dto.responseDTO;

import java.math.BigDecimal;

import com.arealytics.areadocs.enumeration.AccountType;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** DTO for Account entity. */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AccountDTO {

    private Long id;

    private AccountType accountType;

    private Long userId;

    private Long companyId;

    private BigDecimal balance;

    private String currency;

    private Boolean isActive;
}
