package com.arealytics.areadocs.dto.responseDTO;

import java.math.BigDecimal;
import java.util.List;

import com.arealytics.areadocs.enumeration.OrderItemStatus;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/** DTO for returning cart information to the frontend. */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CartResponseDTO {
    private Long userId;
    private BigDecimal totalAmount;
    private int itemCount;
    private List<CartItemDTO> items;

    /** DTO for cart item information. */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class CartItemDTO {
        private Long id;
        private String productCode;
        private String productName;
        private Long folderId;
        private String propertyPfi;
        private String volumeFolio;
        private Boolean isWarningAcknowledged;
        private BigDecimal price;
        private BigDecimal tax;
        private BigDecimal finalPrice;
        private OrderItemStatus status;
    }
}
