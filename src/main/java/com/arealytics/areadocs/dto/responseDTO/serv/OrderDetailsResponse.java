package com.arealytics.areadocs.dto.responseDTO.serv;

import java.util.List;
import java.util.Map;

import lombok.Data;

@Data
public class OrderDetailsResponse {

    private String orderId;
    private String orderDate;
    private Integer totalPrice;
    private Integer totalGst;
    private List<Fee> fees;
    private String deliveryPreference;
    private String email;
    private String contactName;
    private String contactNumber;
    private String customerReference;
    private List<Product> products;

    @Data
    public static class Fee {
        private String type;
        private Integer amount;
        private Integer gst;
        private Map<String, Object> feeDetails;
    }

    @Data
    public static class Product {
        private Integer orderItemNumber;
        private String productCode;
        private ProductIds productIds;
        private String productName;
        private ProductInformation productInformation;
        private String authorityName;
        private String productStatus;
        private Integer price;
        private Integer gst;
        private Map<String, Object> extraData;
        private List<Prerequisite> prerequisites;
    }

    @Data
    public static class ProductIds {
        private String titleId;
    }

    @Data
    public static class ProductInformation {
        private String titleId;
    }

    @Data
    public static class Prerequisite {
        private String productCode;
        private ProductIds productIds;
    }
}
