package com.arealytics.areadocs.dto.responseDTO;

import java.math.BigDecimal;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CompanyDTO extends BaseDTO {
    private String name;
    private String ABN;
    private String ACN;
    private String industry;
    private String description;
    private boolean isActive;
    private Long activeUserCount;
    private String website;
    private Integer employeeCount;
    private String accountsContactNumber;
    private String accountsContactName;
    private String billingEmail;
    private AddressResponseDTO primaryAddress;
    private AddressResponseDTO billingAddress;
    private Long totalDocumentsOrdered;
    private BigDecimal totalDocumentPrice;
}
