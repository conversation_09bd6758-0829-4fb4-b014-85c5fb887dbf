package com.arealytics.areadocs.dto.responseDTO;

import java.time.LocalDateTime;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class CompanyMembershipDTO extends BaseDTO {
    private Long userId;
    private String userEmail;
    private String userFirstName;
    private String userLastName;

    private Long companyId;
    private String companyName;
    private String companyABN;
    private String companyACN;

    private Long roleId;
    private String roleName;
    private String roleDisplayText;
    private LocalDateTime modifiedAt;
    private LocalDateTime deleteAt;
}
