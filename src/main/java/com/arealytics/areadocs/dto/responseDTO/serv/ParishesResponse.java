package com.arealytics.areadocs.dto.responseDTO.serv;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class ParishesResponse {

    @JsonProperty("parishSummaries")
    private List<ParishSummary> parishSummaries;

    @Data
    public static class ParishSummary {

        @JsonProperty("name")
        private String name;

        @JsonProperty("code")
        private String code;
    }
}
