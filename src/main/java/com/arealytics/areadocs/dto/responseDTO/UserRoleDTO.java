package com.arealytics.areadocs.dto.responseDTO;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class UserRoleDTO extends BaseDTO {
    private Long userId;
    private String userEmail;
    private String userFirstName;
    private String userLastName;
    private Long roleId;
    private String roleName;
    private String roleDisplayText;
}
