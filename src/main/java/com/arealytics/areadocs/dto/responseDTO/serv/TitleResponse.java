package com.arealytics.areadocs.dto.responseDTO.serv;

import java.util.List;

import com.arealytics.areadocs.enumeration.SERV.CopyOfTitleType;
import com.arealytics.areadocs.enumeration.SERV.LotType;
import com.arealytics.areadocs.enumeration.SERV.ParcelStatus;
import com.arealytics.areadocs.enumeration.SERV.ParcelType;
import com.arealytics.areadocs.enumeration.SERV.PropertyStatus;
import com.arealytics.areadocs.enumeration.SERV.TitleStatus;
import com.arealytics.areadocs.enumeration.SERV.TitleType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TitleResponse {

    @JsonProperty("titleSummaries")
    private List<TitleDTO> titleSummaries;

    @Data
    public static class TitleDTO {

        @JsonProperty("titleId")
        private String titleId;

        @JsonProperty("titleStatus")
        private TitleStatus titleStatus;

        @JsonProperty("titleType")
        private TitleType titleType;

        @JsonProperty("titleReference")
        private TitleReferenceDTO titleReference;

        @JsonProperty("copyOfTitleType")
        private CopyOfTitleType copyOfTitleType;

        @JsonProperty("titleStreetAddress")
        private String titleStreetAddress;

        @JsonProperty("landDescriptionText")
        private String landDescriptionText;

        @JsonProperty("landParcels")
        private List<LandParcelDTO> landParcels;

        @JsonProperty("properties")
        private List<TitlePropertyDTO> properties;
    }

    @Data
    public static class TitleReferenceDTO {

        @JsonProperty("volume")
        private Integer volume;

        @JsonProperty("folio")
        private Integer folio;

        @JsonProperty("suffix")
        private String suffix;
    }

    @Data
    public static class LandParcelDTO {

        @JsonProperty("spi")
        private String parcelIdentifier;

        @JsonProperty("parcelType")
        private ParcelType parcelType;

        @JsonProperty("lotType")
        private LotType lotType;

        @JsonProperty("parcelStatus")
        private ParcelStatus parcelStatus;
    }

    @Data
    public static class TitlePropertyDTO {

        @JsonProperty("propertyPfi")
        private Long propertyPfi;

        @JsonProperty("isMultiAssess")
        private Boolean isMultiAssess;

        @JsonProperty("propertyStatus")
        private PropertyStatus propertyStatus;
    }
}
