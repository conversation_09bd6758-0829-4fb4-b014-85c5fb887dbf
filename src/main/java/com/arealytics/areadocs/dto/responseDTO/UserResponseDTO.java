package com.arealytics.areadocs.dto.responseDTO;

import java.time.LocalDateTime;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class UserResponseDTO extends BaseDTO {
    private String email;
    private String firstName;
    private String middleName;
    private String lastName;
    private String contactNumber;
    private String userType;
    private LocalDateTime lastLogin;
    private String bio;
    private String profilePictureUrl;
    private Long companyId;
    private Long roleId;
    private String roleName;
    private String roleDisplayText;
    private String ABN;
    private String ACN;
    private String keycloakId;
    private AddressResponseDTO primaryAddress;
    private AddressResponseDTO billingAddress;
}
