package com.arealytics.areadocs.dto.responseDTO.serv;

import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class ProductsResponse {

    @JsonProperty("products")
    private List<Product> products;

    @Data
    public static class Product {

        @JsonProperty("productCode")
        private String productCode;

        @JsonProperty("productIds")
        private ProductIds productIds;

        @JsonProperty("productName")
        private String productName;

        @JsonProperty("productDescription")
        private String productDescription;

        @JsonProperty("productInformation")
        private ProductInformation productInformation;

        @JsonProperty("productAvailability")
        private String productAvailability;

        @JsonProperty("authorityName")
        private String authorityName;

        @JsonProperty("price")
        private BigDecimal price;

        @JsonProperty("gst")
        private BigDecimal gst;

        @JsonProperty("turnaroundTime")
        private Integer turnaroundTime;

        @JsonProperty("turnaroundTimeUnit")
        private String turnaroundTimeUnit;

        @JsonProperty("customerWarning")
        private String customerWarning;

        @JsonProperty("extraData")
        private List<ExtraData> extraData;

        @JsonProperty("prerequisites")
        private List<Prerequisite> prerequisites;
    }

    @Data
    public static class ProductIds {
        @JsonProperty("titleId")
        private String titleId;
        @JsonProperty("ownersCorporationNumber")
        private String ownersCorporationNumber;
        @JsonProperty("documentId")
        private String documentId;
    }

    @Data
    public static class ProductInformation {
        @JsonProperty("documentType")
        private String documentType;
    }

    @Data
    public static class ExtraData {
        @JsonProperty("key")
        private String key;
    }

    @Data
    public static class Prerequisite {

        @JsonProperty("productCode")
        private String productCode;

        @JsonProperty("productIds")
        private ProductIds productIds;

        @JsonProperty("productName")
        private String productName;

        @JsonProperty("productDescription")
        private String productDescription;

        @JsonProperty("productInformation")
        private ProductInformation productInformation;

        @JsonProperty("productAvailability")
        private String productAvailability;

        @JsonProperty("authorityName")
        private String authorityName;

        @JsonProperty("price")
        private BigDecimal price;

        @JsonProperty("gst")
        private BigDecimal gst;

        @JsonProperty("turnaroundTime")
        private int turnaroundTime;

        @JsonProperty("turnaroundTimeUnit")
        private String turnaroundTimeUnit;

        @JsonProperty("customerWarning")
        private String customerWarning;

        @JsonProperty("extraData")
        private List<ExtraData> extraData;

        @JsonProperty("previousOrder")
        private PreviousOrder previousOrder;
    }

    @Data
    public static class PreviousOrder {
        @JsonProperty("orderId")
        private String orderId;

        @JsonProperty("orderItemNumber")
        private int orderItemNumber;

        @JsonProperty("orderDate")
        private String orderDate;
    }
}
