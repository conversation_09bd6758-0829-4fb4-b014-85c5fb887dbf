package com.arealytics.areadocs.dto.responseDTO.serv;

import java.util.List;

import com.arealytics.areadocs.enumeration.SERV.TitleStatus;
import com.arealytics.areadocs.enumeration.SERV.TitleType;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class ProprietorsResponse {

    @JsonProperty("proprietorSummaries")
    private List<ProprietorSummary> proprietorSummaries;

    @JsonProperty("pagination")
    private Pagination pagination;

    @Data
    public static class ProprietorSummary {

        @JsonProperty("lastName")
        private String lastName;

        @JsonProperty("givenName")
        private String givenName;

        @JsonProperty("companyName")
        private String companyName;

        @JsonProperty("titleDetails")
        private TitleDetails titleDetails;

        @Data
        public static class TitleDetails {

            @JsonProperty("titleId")
            private String titleId;

            @JsonProperty("titleStatus")
            private TitleStatus titleStatus;

            @JsonProperty("titleType")
            private TitleType titleType;

            @JsonProperty("landDescriptionText")
            private String landDescriptionText;

            @JsonProperty("municipality")
            private String municipality;
        }
    }

    @Data
    public static class Pagination {

        @JsonProperty("pageSize")
        private int pageSize;

        @JsonProperty("nextPage")
        private int nextPage;
    }
}
