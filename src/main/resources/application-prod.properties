
spring.application.name=areadocs
spring.main.allow-bean-definition-overriding=true


# Server Configuration
server.port=8082
server.compression.enabled=true
server.compression.mime-types=application/json,application/xml,text/html,text/plain
server.compression.min-response-size=1024

# File Upload Settings
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Upload Directory
app.upload.dir=/tmp/areadocs/uploads

# JSON Serialization
spring.jackson.serialization.FAIL_ON_EMPTY_BEANS=false
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=UTC


# API Configuration
api.base.url=https://anypoint.mulesoft.com/mocking/api/v1/sources/exchange/assets/81b365a6-d760-419b-9615-4642b83ca28e/serv-alert-eapi/1.0.9/m
api.client-id=abcdef123456abcdef123456abcdef12
api.client-secret=abcdef123456abcdef123456abcdef12
api.timeout=5000

# Database Configuration
spring.datasource.url=*************************************************
spring.datasource.username=prod_user
#spring.datasource.password=prod_password
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.max-lifetime=1200000

# JPA Configuration
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
spring.jpa.open-in-view=false

# Logging Configuration
logging.level.root=WARN
logging.level.com.arealytics.areadocs=INFO
logging.file.name=/var/log/areadocs/application-prod.log
logging.file.max-size=10MB
logging.file.max-history=10


# Spring Security OAuth2 (Production Keycloak)
keycloak.base-url=https://auth.arealytics.com.au
spring.security.oauth2.client.registration.keycloak.client-id=areadocs-be
spring.security.oauth2.client.registration.keycloak.client-secret=prod-secret
spring.security.oauth2.client.registration.keycloak.authorization-grant-type=authorization_code
spring.security.oauth2.client.registration.keycloak.scope=openid,profile,email
spring.security.oauth2.client.provider.keycloak.issuer-uri=${keycloak.base-url}/realms/areadocs
spring.security.oauth2.client.registration.keycloak.redirect-uri=${keycloak.base-url}/login/oauth2/code/keycloak
spring.security.oauth2.resourceserver.jwt.issuer-uri=${keycloak.base-url}/realms/areadocs
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=${keycloak.base-url}/realms/areadocs/protocol/openid-connect/certs

#swagger details
swagger.server.url=https://api-areadocs.arealytics.com.au
swagger.server.description=Production Server
# Content negotiation
spring.mvc.contentnegotiation.favor-path-extension=false
spring.mvc.contentnegotiation.favor-parameter=false
spring.mvc.contentnegotiation.media-types.json=application/json
spring.mvc.default-content-type=application/json

# Springdoc OpenAPI config
springdoc.default-produces-media-type=application/json
springdoc.default-consumes-media-type=application/json

# Platform Admin Configuration
platform.admin.email=<EMAIL>
platform.admin.password=Admin@123
platform.admin.role=PLATFORM_ADMIN

# Flyway configuration
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true
spring.flyway.baseline-version=1
spring.flyway.url=${spring.datasource.url}
spring.flyway.user=${spring.datasource.username}
spring.flyway.password=${spring.datasource.password}

# AWS S3 Configuration
aws.access.key.id=********************
aws.secret.access.key=jaB1KhbI5MWVGYAiZVvTjvSPE2m+EcD9s8jksGUJ
aws.s3.bucket.name=areadocs-test
aws.region=us-east-1
s3.paths.document-uploads=document-files
