-- Insert Company and Users, followed by Accounts for INDIVIDUAL and COMPANY

-- Insert Permissions
INSERT INTO permissions (permission_code, permission_name, permission_description, created_at)
VALUES
    ('CREATE_USER', 'Create User', 'Permission to create user', NOW()),
    ('DELETE_USER', 'Delete User', 'Permission to delete user', NOW()),
    ('CREATE_COMPANY', 'Create Company', 'Permission to create company', NOW()),
    ('MAP_USER_TO_COMPANY', 'Map User To Company', 'Permission to map user to company', NOW()),
    ('CREATE_ROLE', 'Create Role', 'Permission to create role', NOW()),
    ('ASSIGN_ROLE', 'Assign Role', 'Permission to assign role', NOW()),
    ('RECHARGE', 'Recharge', 'Permission to recharge', NOW()),
    ('MANAGE_COMPANY_USERS', 'Manage Company Users', 'Permission to add/remove company users', NOW()),
    ('VIEW_COMPANY_DATA', 'View Company Data', 'Permission to view company information', NOW()),
    ('MANAGE_COMPANY_SETTINGS', 'Manage Company Settings', 'Permission to modify company settings', NOW()),
    ('VIEW_INDIVIDUAL_DATA', 'View Individual Data', 'Permission to view individual account data', NOW()),
    ('MANAGE_INDIVIDUAL_SETTINGS', 'Manage Individual Settings', 'Permission to modify individual settings', NOW());

INSERT INTO roles (is_active, name, description, created_at, modified_at, version, display_text)
VALUES (true, 'PLATFORM_ADMIN', 'Platform administrator with full system access', NOW(), NOW(), 0, 'Platform Admin'),
       (true, 'INDIVIDUAL_USER', 'Individual user with admin privileges', NOW(), NOW(), 0, 'User'),
       (true, 'COMPANY_ADMIN', 'Company administrator with company management privileges', NOW(), NOW(), 0, 'Admin'),
       (true, 'COMPANY_MEMBER', 'Company member with limited access', NOW(), NOW(), 0, 'Member');
-- Insert role_permission_mappings
INSERT INTO role_permission_mappings (is_active, created_at, modified_at, permission_id, role_id, version, status)
SELECT
    true, NOW(), NOW(), p.id, r.id, 0, 'ACTIVE'
FROM permissions p
         JOIN roles r ON r.name = 'PLATFORM_ADMIN'
WHERE p.permission_code IN (
                            'CREATE_USER',
                            'DELETE_USER',
                            'CREATE_COMPANY',
                            'MAP_USER_TO_COMPANY',
                            'CREATE_ROLE',
                            'ASSIGN_ROLE',
                            'RECHARGE'
    );

-- Assign permissions to INDIVIDUAL_USER role
INSERT INTO role_permission_mappings (is_active, created_at, modified_at, permission_id, role_id, version, status)
SELECT true, NOW(), NOW(), p.id, r.id, 0, 'ACTIVE'
FROM permissions p
         JOIN roles r ON r.name = 'INDIVIDUAL_USER'
WHERE p.permission_code IN ('VIEW_INDIVIDUAL_DATA', 'MANAGE_INDIVIDUAL_SETTINGS');

-- Assign permissions to COMPANY_ADMIN role
INSERT INTO role_permission_mappings (is_active, created_at, modified_at, permission_id, role_id, version, status)
SELECT true, NOW(), NOW(), p.id, r.id, 0, 'ACTIVE'
FROM permissions p
         JOIN roles r ON r.name = 'COMPANY_ADMIN'
WHERE p.permission_code IN ('MANAGE_COMPANY_USERS', 'VIEW_COMPANY_DATA', 'MANAGE_COMPANY_SETTINGS');

-- Assign permissions to COMPANY_MEMBER role
INSERT INTO role_permission_mappings (is_active, created_at, modified_at, permission_id, role_id, version, status)
SELECT true, NOW(), NOW(), p.id, r.id, 0, 'ACTIVE'
FROM permissions p
         JOIN roles r ON r.name = 'COMPANY_MEMBER'
WHERE p.permission_code IN ('VIEW_COMPANY_DATA');

-- -----------------------------------------------------------------------------
-- Insert Addresses
-- -----------------------------------------------------------------------------

INSERT INTO states (
    state_name, state_abbr, is_active, created_at, modified_at, version, deleted_at
)
VALUES
('New South Wales', 'NSW', true, NOW(), NOW(), 0, NULL),
('Queensland', 'QLD', true, NOW(), NOW(), 0, NULL),
('Victoria', 'VIC', true, NOW(), NOW(), 0, NULL),
('Western Australia', 'WA', true, NOW(), NOW(), 0, NULL),
('South Australia', 'SA', true, NOW(), NOW(), 0, NULL);

INSERT INTO zip_codes(
    zip_code, state_id, is_active, created_at, modified_at, version, deleted_at
)
VALUES
(1215,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),
(1220,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1225,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1230,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1235,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1240,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1300,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1335,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1340,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1350,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1355,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1360,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1363,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1435,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1445,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1450,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1455,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1460,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1465,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1466,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1470,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1475,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1480,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1481,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1485,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1490,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1495,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1499,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1515,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1560,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1565,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1570,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1585,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1590,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1595,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1630,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1635,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1639,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1640,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1655,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1660,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1670,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1675,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1680,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1685,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1700,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1701,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1710,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1715,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1730,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1750,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1755,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1765,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1790,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1800,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1805,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1811,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1825,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1835,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1851,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1860,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1871,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1875,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1885,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1890,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
1891,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2000,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2001,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2004,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2006,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2007,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2008,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2009,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2010,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2011,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2012,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2015,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2016,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2017,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2018,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2019,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2020,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2021,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2022,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2023,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2024,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2025,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2026,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2027,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2028,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2029,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2030,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2031,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2032,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2033,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2034,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2035,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2036,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2037,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2038,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2039,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2040,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2041,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2042,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2043,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2044,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2045,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2046,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2047,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2048,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2049,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2050,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2052,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2057,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2059,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2060,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2061,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2062,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2063,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2064,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2065,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2066,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2067,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2068,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2069,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2070,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2071,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2072,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2073,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2074,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2075,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2076,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2077,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2079,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2080,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2081,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2082,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2083,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2084,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2085,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2086,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2087,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2088,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2089,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2090,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2092,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2093,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2094,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2095,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2096,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2097,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2099,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2100,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2101,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2102,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2103,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2104,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2105,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2106,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2107,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2108,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2109,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2110,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2111,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2112,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2113,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2114,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2115,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2116,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2117,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2118,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2119,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2120,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2121,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2122,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2123,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2124,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2125,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2126,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2127,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2128,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2129,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2130,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2131,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2132,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2133,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2134,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2135,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2136,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2137,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2138,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2139,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2140,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2141,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2142,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2143,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2144,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2145,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2146,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2147,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2148,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2150,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2151,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2152,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2153,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2154,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2155,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2156,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2157,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2158,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2159,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2160,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2161,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2162,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2163,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2164,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2165,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2166,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2167,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2168,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2170,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2171,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2172,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2173,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2174,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2175,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2176,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2177,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2178,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2179,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2190,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2191,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2192,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2193,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2194,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2195,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2196,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2197,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2198,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2199,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2200,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2203,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2204,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2205,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2206,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2207,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2208,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2209,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2210,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2211,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2212,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2213,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2214,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2216,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2217,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2218,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2219,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2220,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2221,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2222,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2223,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2224,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2225,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2226,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2227,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2228,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2229,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2230,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2231,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2232,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2233,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2234,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2250,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2251,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2256,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2257,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2258,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2259,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2260,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2261,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2262,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2263,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2264,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2265,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2267,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2278,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2280,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2281,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2282,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2283,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2284,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2285,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2286,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2287,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2289,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2290,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2291,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2292,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2293,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2294,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2295,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2296,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2297,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2298,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2299,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2300,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2302,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2303,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2304,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2305,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2306,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2307,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2308,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2309,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2310,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2311,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2312,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2314,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2315,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2316,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2317,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2318,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2319,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2320,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2321,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2322,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2323,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2324,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2325,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2326,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2327,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2328,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2329,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2330,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2331,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2333,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2334,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2335,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2336,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2337,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2338,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2339,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2340,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2341,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2342,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2343,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2344,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2345,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2346,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2347,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2348,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2350,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2351,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2352,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2353,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2354,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2355,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2356,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2357,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2358,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2359,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2360,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2361,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2365,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2369,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2370,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2371,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2372,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2379,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2380,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2381,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2382,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2386,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2387,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2388,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2390,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2395,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2396,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2397,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2398,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2399,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2400,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2401,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2402,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2403,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2404,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2405,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2406,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2408,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2409,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2410,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2411,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2415,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2420,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2421,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2422,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2423,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2424,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2425,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2426,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2427,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2428,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2429,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2430,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2431,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2439,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2440,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2441,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2442,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2443,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2444,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2445,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2446,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2447,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2448,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2449,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2450,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2452,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2453,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2454,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2455,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2456,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2460,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2462,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2463,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2464,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2465,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2466,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2469,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2470,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2471,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2472,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2473,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2474,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2475,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2476,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2477,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2478,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2479,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2480,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2481,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2482,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2483,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2484,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2485,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2486,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2487,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2488,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2489,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2490,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2500,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2502,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2505,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2506,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2508,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2515,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2516,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2517,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2518,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2519,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2520,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2521,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2522,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2525,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2526,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2527,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2528,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2529,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2530,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2533,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2534,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2535,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2536,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2537,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2538,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2539,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2541,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2545,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2546,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2548,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2549,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2550,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2551,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2555,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2556,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2557,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2558,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2559,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2560,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2563,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2564,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2565,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2566,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2567,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2568,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2569,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2570,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2571,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2572,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2573,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2574,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2575,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2576,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2577,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2578,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2579,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2580,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2581,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2582,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2583,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2584,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2585,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2586,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2587,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2588,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2590,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2594,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2619,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2621,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2622,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2623,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2624,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2625,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2626,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2627,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2628,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2629,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2630,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2631,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2632,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2633,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2640,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2641,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2642,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2643,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2644,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2645,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2646,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2647,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2648,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2649,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2650,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2651,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2652,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2653,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2655,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2656,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2658,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2659,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2660,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2661,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2663,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2665,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2666,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2668,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2669,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2671,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2672,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2675,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2678,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2680,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2681,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2700,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2701,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2702,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2703,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2705,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2706,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2707,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2708,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2710,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2711,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2712,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2713,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2714,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2715,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2716,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2717,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2720,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2721,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2722,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2725,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2726,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2727,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2729,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2730,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2731,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2732,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2733,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2734,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2735,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2736,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2737,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2738,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2739,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2745,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2747,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2748,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2749,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2750,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2751,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2752,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2753,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2754,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2755,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2756,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2757,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2758,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2759,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2760,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2761,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2762,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2763,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2765,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2766,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2767,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2768,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2770,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2773,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2774,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2775,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2776,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2777,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2778,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2779,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2780,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2782,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2783,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2784,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2785,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2786,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2787,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2790,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2791,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2792,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2793,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2794,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2795,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2797,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2798,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2799,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2800,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2803,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2804,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2805,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2806,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2807,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2809,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2810,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2820,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2821,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2823,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2824,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2825,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2827,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2828,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2829,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2830,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2831,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2832,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2833,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2834,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2835,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2836,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2839,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2840,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2842,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2843,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2844,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2845,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2846,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2847,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2848,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2849,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2850,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2852,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2864,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2865,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2866,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2867,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2868,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2869,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2870,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2871,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2873,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2874,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2875,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2876,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2877,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2878,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2879,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2880,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2890,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2898,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2899,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
3500,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
3644,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3707,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
3000,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3006,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3004,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3008,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3005,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3002,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3003,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3121,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3206,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3207,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3016,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3015,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3013,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3011,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3031,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3032,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3050,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3051,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3039,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3055,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3056,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3057,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3054,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3052,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3068,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3010,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3053,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3065,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3066,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3067,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3019,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3012,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3025,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3018,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3028,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3027,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3029,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3030,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3020,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3022,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3023,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3021,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3038,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3037,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3059,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3049,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3045,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3043,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3042,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3041,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3040,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3033,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3034,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3064,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3062,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3048,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3061,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3047,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3074,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3046,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3044,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3060,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3058,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3073,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3072,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3070,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3078,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3079,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3081,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3084,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3085,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3086,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3087,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3088,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3094,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3093,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3095,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3083,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3182,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3181,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3141,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3142,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3184,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3183,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3143,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3144,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3122,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3123,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3101,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3102,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3105,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3104,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3103,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3126,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3124,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3146,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3145,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3161,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3185,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3162,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3163,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3186,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3187,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3204,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3188,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3191,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3107,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3106,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3108,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3109,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3129,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3130,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3127,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3128,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3125,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3147,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3151,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3111,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3131,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3132,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3133,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3193,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3113,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3114,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3134,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3135,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3152,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3179,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3178,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3115,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3136,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3153,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3155,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3180,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3156,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3116,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3140,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3138,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3137,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3765,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3158,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3160,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3750,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3076,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3075,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3754,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3753,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3082,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3090,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3091,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3089,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3096,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3149,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3148,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3166,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3165,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3189,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3190,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3192,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3194,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3195,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3168,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3800,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3167,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3169,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3202,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3150,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3170,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3171,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3172,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3174,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3173,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3175,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3177,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3803,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3196,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3197,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3298,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3201,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3200,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3199,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3910,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3975,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3976,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3805,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3977,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3806,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3807,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3809,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3808,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3810,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3802,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3804,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3220,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3219,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3224,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3216,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3218,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3215,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3214,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
2769,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
3024,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3026,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3036,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3063,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3071,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3097,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3139,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3154,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3159,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3198,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3205,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3217,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3226,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3228,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3230,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3232,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3270,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3280,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3300,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3335,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3337,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3338,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3350,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3396,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3400,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3412,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3427,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3428,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3429,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3446,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3483,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3485,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3550,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3690,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3723,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3752,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3781,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3818,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3824,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3850,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3912,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3913,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3915,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3926,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3930,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3931,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3936,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3937,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3939,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3943,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3950,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3978,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3995,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
4120,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4035,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4301,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4817,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4359,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4152,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4385,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4078,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4812,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4000,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4520,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4305,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4703,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4053,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4311,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4272,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4054,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4218,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4217,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4502,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4746,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4102,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4216,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4420,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4655,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4880,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4101,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4223,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4503,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4500,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4210,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4163,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4207,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4300,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4556,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4165,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4740,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4285,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4020,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4818,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4551,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4415,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4014,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4309,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4007,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4170,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4721,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4169,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4670,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4870,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4804,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4350,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4220,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4557,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4171,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4558,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4159,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4227,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4560,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4113,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4133,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4706,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4017,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4119,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4566,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4807,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4701,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4869,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4226,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4030,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4860,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4825,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4115,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4352,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4215,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4680,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4051,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4514,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4123,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4744,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4510,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4011,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4211,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4508,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4077,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4745,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4507,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4068,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4275,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4110,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4174,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4019,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4012,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4702,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4400,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4132,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4010,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4304,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4380,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4306,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4313,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4209,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4037,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4814,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4314,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4552,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4103,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4877,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4850,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4821,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4737,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4212,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4075,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4564,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4722,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4573,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4013,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4034,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4021,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4570,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4810,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4553,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4461,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4873,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4154,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4066,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4883,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4178,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4506,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4512,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4879,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4343,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4131,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4802,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4756,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4854,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4572,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4160,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4125,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4511,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4567,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4184,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4109,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4022,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4157,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4340,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4179,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4031,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4455,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4116,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4127,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4711,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4070,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4121,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4555,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4122,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4114,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4221,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4067,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4876,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4310,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4852,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4363,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4112,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4490,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4888,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4735,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4390,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4805,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4105,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4006,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4878,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4868,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4061,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4104,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4509,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4504,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4373,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4871,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4811,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4650,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4872,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4753,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4009,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4815,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4885,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4344,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4720,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4354,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4521,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4107,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4060,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4064,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4494,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4515,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4074,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4710,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4069,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4356,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4151,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4700,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4575,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4370,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4065,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4214,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4005,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4465,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4118,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4128,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4798,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4472,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4213,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4571,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4562,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4008,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4406,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4820,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4505,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4610,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4032,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4413,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4561,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4809,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4224,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4059,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4055,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4401,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4036,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4108,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4421,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4124,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4671,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4819,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4164,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4715,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4806,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4565,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4428,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4405,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4554,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4280,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4408,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4890,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4404,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4816,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4228,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4717,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4738,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4550,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4709,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4018,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4225,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4800,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4492,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4519,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4158,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4891,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4824,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4660,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4892,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4865,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4724,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4117,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4073,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4516,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4501,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4718,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4341,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4568,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4161,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4129,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4858,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4270,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4739,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4754,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4130,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4605,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4581,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4480,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4725,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4518,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4106,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4799,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4677,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4694,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4355,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4741,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4183,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4342,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4208,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4627,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4861,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4361,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4403,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4875,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4346,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4559,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4312,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4716,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4630,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4808,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4829,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4422,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4470,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4751,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4462,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4156,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4358,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4750,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4454,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4743,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4625,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4357,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4884,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4580,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4569,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4172,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4621,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4822,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4730,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4347,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4614,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4486,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4383,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4205,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4695,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4714,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4674,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4626,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4173,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4887,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4025,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4830,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4882,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4895,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4673,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4563,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4303,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4497,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4600,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4407,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4726,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4410,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4615,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4360,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4886,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4712,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4601,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4856,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4381,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4659,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4855,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4388,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4757,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4382,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4612,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4874,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4362,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4153,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4727,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4723,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4478,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4676,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4705,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4699,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4487,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4412,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4423,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4620,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4608,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4881,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4613,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4704,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4416,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4387,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4424,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4707,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4849,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4678,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4606,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4477,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4374,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4719,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4427,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4482,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4076,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4419,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4371,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4517,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4823,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4697,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4426,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4742,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4474,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4287,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4574,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4307,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4111,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4728,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4378,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4411,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4611,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4155,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4377,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4713,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4417,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4662,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4384,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4365,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4376,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4828,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4402,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4375,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4859,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4481,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4364,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4493,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4731,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4488,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4372,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4425,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4467,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4498,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4496,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4418,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4353,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4489,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4468,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4732,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4736,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4491,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4479,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4733,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
6215,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6072,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6024,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6164,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6054,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6225,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6169,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6019,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6210,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6530,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6569,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6110,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6505,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6065,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6333,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6155,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6395,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6104,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6036,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6208,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6101,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6025,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6076,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6517,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6008,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6031,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6000,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6147,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6726,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6005,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6003,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6060,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6754,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6100,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6357,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6722,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6059,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6230,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6163,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6056,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6030,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6450,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6105,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6050,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6642,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6111,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6010,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6460,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6714,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6055,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6725,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6007,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6330,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6285,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6043,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6167,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6308,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6152,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6180,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6021,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6271,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6220,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6219,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6172,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6211,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6335,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6009,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6728,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6151,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6107,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6160,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6011,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6707,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6102,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6239,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6004,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6233,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6148,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6069,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6536,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6302,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6018,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6255,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6280,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6061,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6125,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6170,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6014,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6156,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6535,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6153,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6171,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6436,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6027,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6108,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6350,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6630,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6562,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6317,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6084,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6390,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6053,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6218,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6051,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6159,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6052,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6150,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6017,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6237,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6154,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6214,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6306,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6432,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6640,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6109,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6479,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6701,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6058,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6566,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6236,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6328,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6106,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6070,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6166,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6122,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6035,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6078,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6020,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6430,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6426,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6525,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6168,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6281,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6023,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6174,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6324,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6519,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6006,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6391,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6026,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6606,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6224,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6112,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6532,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6721,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6029,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6162,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6401,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6083,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6012,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6258,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6062,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6312,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6367,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6028,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6157,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6066,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6514,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6071,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6288,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6149,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6437,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6503,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6501,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6438,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6227,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6064,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6338,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6073,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6034,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6346,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6063,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6284,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6753,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6564,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6392,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6760,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6443,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6103,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6057,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6081,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6232,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6383,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6516,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6124,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6522,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6507,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6571,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6348,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6290,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6743,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6502,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6639,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6337,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6612,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6490,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6041,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6365,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6015,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6022,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6515,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6407,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6315,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6765,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6467,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6352,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6625,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6415,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6475,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6037,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6158,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6477,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6556,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6418,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6316,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6473,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6244,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6282,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6044,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6207,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6090,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6016,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6620,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6423,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6209,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6123,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6229,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6635,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6568,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6254,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6359,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6537,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6353,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6175,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6720,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6504,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6373,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6603,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6260,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6213,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6560,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6082,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6410,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6710,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6608,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6321,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6512,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6609,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6252,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6572,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6243,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6173,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6751,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6485,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6431,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6370,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6309,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6253,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6320,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6313,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6369,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6510,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6074,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6176,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6442,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6226,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6461,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6375,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6341,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6558,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6472,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6770,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6343,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6121,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6358,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6251,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6304,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6513,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6429,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6511,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6396,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6623,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6038,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6528,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6033,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6740,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6126,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6440,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6605,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6336,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6361,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6567,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6262,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6311,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6518,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6355,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6077,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6318,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6165,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6032,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6718,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6161,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6646,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6223,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6419,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6323,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6409,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6521,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6462,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6506,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6286,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6393,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6405,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6275,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6638,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6398,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6470,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6240,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6394,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6327,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6468,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6627,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6631,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6422,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6428,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6067,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6484,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6403,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6384,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6421,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6705,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6447,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6372,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6489,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6326,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6488,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6445,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6385,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6476,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6363,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6509,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6420,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6480,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6575,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6368,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6411,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6446,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6042,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6425,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6424,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6221,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6412,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6181,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6448,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6574,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6713,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6434,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6228,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6256,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6356,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6613,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6322,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6758,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6413,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6397,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6427,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6414,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6351,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6632,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6614,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6616,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6466,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6386,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6487,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6465,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6079,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6068,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6463,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6628,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6716,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6182,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
6452,(SELECT id FROM states WHERE state_abbr = 'WA'),true,NOW(),NOW(),0,NULL),(
2600,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2601,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2602,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2603,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2604,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2605,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2606,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2607,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2609,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2611,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2612,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2614,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2615,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2617,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2618,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2620,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2900,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2902,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2903,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2904,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2905,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2906,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2911,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2912,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2913,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2914,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
3099,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3212,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3336,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3430,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3751,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3755,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3757,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3759,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3760,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3761,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3766,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3767,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3770,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3782,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3785,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3786,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3787,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3788,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3789,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3791,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3792,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3793,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3795,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3796,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3911,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3916,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3918,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3919,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3920,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3927,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3928,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3929,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3933,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3934,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3938,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3940,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3941,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3942,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3944,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
2740,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
2540,(SELECT id FROM states WHERE state_abbr = 'NSW'),true,NOW(),NOW(),0,NULL),(
3844,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3213,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3717,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3221,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3981,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3356,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3840,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3555,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3691,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3775,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3222,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3223,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3250,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3444,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3352,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3630,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3677,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3351,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3442,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3875,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3756,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3340,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3922,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3465,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3355,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
4072,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
4219,(SELECT id FROM states WHERE state_abbr = 'QLD'),true,NOW(),NOW(),0,NULL),(
3820,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3953,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3722,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3730,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3377,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3549,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3585,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3564,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3380,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3579,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3305,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3450,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3225,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3551,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3556,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3842,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3608,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3764,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3616,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3629,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3231,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3269,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3847,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3862,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3870,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3871,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3777,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3825,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3331,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3797,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3357,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3211,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3437,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3498,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3363,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3816,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3631,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3925,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3438,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3464,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
3496,(SELECT id FROM states WHERE state_abbr = 'VIC'),true,NOW(),NOW(),0,NULL),(
5068,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5094,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5110,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5082,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5064,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5063,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5015,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5007,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5163,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5000,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5012,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5013,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5011,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5290,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5087,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5009,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5152,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5006,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5277,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5061,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5037,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5067,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5065,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5253,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5023,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5034,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5113,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5010,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5072,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5109,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5096,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5051,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5653,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5160,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5291,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5044,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5020,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5112,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5038,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5165,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5031,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5039,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5341,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5025,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5041,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5107,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5043,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5081,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5045,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5046,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5021,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5118,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5343,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5095,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5251,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5352,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5086,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5019,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5211,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5070,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5159,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5171,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5075,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5606,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5255,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5172,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5162,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5014,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5700,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5554,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5108,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5062,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5280,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5238,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5355,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5066,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5033,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5050,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5092,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5084,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5111,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5168,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5008,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5114,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5125,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5098,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5042,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5121,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5169,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5032,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5106,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5018,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5158,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5117,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5035,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5017,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5074,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5173,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5024,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5083,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5161,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5166,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5120,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5097,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5085,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5090,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5116,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5049,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5069,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5091,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5047,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5016,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5052,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5164,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5088,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5073,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5022,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5048,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5167,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5174,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5126,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5076,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5093,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5115,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5157,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5089,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5127,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5040,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5131,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5132,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5150,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5170,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL),(
5950,(SELECT id FROM states WHERE state_abbr = 'SA'),true,NOW(),NOW(),0,NULL);

-- Insert Addresses
INSERT INTO Address (address_line_1, address_line_2, suburb, state_id, zip_code_id, address_type, created_at, modified_at, version, is_active)
VALUES
    -- Original addresses for existing users (PRIMARY and BILLING)
    ('789 Admin Ave', NULL, 'Carsville', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'BILLING', NOW(), NOW(), 0, true),
    ('123 Main St', NULL, 'Sydney CBD', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('456 Corporate Blvd', NULL, 'Docklands', (SELECT id FROM States WHERE state_abbr = 'VIC' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '3000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('567 Corporate Blvd', NULL, 'Docklands', (SELECT id FROM States WHERE state_abbr = 'VIC' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '3000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('678 Corporate Blvd', NULL, 'Docklands', (SELECT id FROM States WHERE state_abbr = 'VIC' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '3000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('789 Corporate Blvd', NULL, 'Docklands', (SELECT id FROM States WHERE state_abbr = 'VIC' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '3000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('891 Corporate Blvd', NULL, 'Docklands', (SELECT id FROM States WHERE state_abbr = 'VIC' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '3000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('912 Corporate Blvd', NULL, 'Docklands', (SELECT id FROM States WHERE state_abbr = 'VIC' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '3000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('123 Corporate Blvd', NULL, 'Docklands', (SELECT id FROM States WHERE state_abbr = 'VIC' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '3000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('234 Corporate Blvd', NULL, 'Docklands', (SELECT id FROM States WHERE state_abbr = 'VIC' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '3000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('345 Corporate Blvd', NULL, 'Docklands', (SELECT id FROM States WHERE state_abbr = 'VIC' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '3000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('123 Main St', 'Arealytics User', 'Docklands', (SELECT id FROM States WHERE state_abbr = 'VIC' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '3000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('456 Main St', 'Arealytics User', 'Docklands', (SELECT id FROM States WHERE state_abbr = 'VIC' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '3000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('234 Main St', 'Arealytics User', 'Docklands', (SELECT id FROM States WHERE state_abbr = 'VIC' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '3000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('567 Main St', 'Arealytics User', 'Docklands', (SELECT id FROM States WHERE state_abbr = 'VIC' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '3000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    -- Company Addresses
    ('1 Farrer Place', 'Company Address', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true), -- Savills
    ('357-363 George St', 'Levels 19 - 21', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true), -- CBRE Sydney
    ('1 O''connell St', 'Level 22', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true), -- Cushman & Wakefield
    ('200 George St', 'Company Address', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true), -- Mirvac
    ('133 Castlereagh St', 'Levels 2, 22 - 29', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true), -- Stockland
    ('18 Ross St', '201', 'Parramatta', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2150' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true), -- Bawdens
    ('10 Spring St', 'Company Address', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true), -- CI Australia
    ('5 Emerald Hills Blvd', 'Shop 1a', 'Leppington', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2179' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true), -- Colliers Sydney South West
    ('8 Churchill Ave', 'Company Address', 'Strathfield', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2135' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true); -- Strathfield Partners

-- Insert Company
INSERT INTO Company (employee_count, is_active, created_at, deleted_at, modified_at, version, description, industry, name, ABN, ACN, website, primary_address_id, billing_address_id, billing_email, accounts_contact_name, accounts_contact_number)
VALUES (
    100, true, NOW(), NULL, NOW(), 0, 'Global real estate services', 'Real estate', 'Arealytics', '**********', '**********', 'arealytics.com.au',
    (SELECT id FROM Address WHERE address_line_1 = '456 Corporate Blvd' AND address_line_2 IS NULL AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1),
    (SELECT id FROM Address WHERE address_line_1 = '456 Corporate Blvd' AND address_line_2 IS NULL AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1),
    '<EMAIL>', 'John Wick', '**********'
);

-- Insert Users
INSERT INTO app_user (
    is_active, created_at, deleted_at, last_login, modified_at, version, contact_number, user_type,
    first_name, last_name, bio, email, keycloak_id, profile_picture_url,
    primary_address_id, billing_address_id
)
VALUES
    (
        true, NOW(), NULL, NULL, NOW(), 0, NULL, 'INDIVIDUAL',
        'Platform', 'Platform', NULL,
        '<EMAIL>', '8b3846a6-c363-46ea-89e3-9c2f20bd390e', NULL,
        (SELECT id FROM Address WHERE address_line_1 = '789 Admin Ave' AND address_line_2 IS NULL AND suburb = 'Carsville' AND address_type = 'BILLING' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '789 Admin Ave' AND address_line_2 IS NULL AND suburb = 'Carsville' AND address_type = 'BILLING' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '1122334455', 'INDIVIDUAL',
        'Matthew', 'Salisbury', 'Real estate investor and entrepreneur.',
        '<EMAIL>', '0bdec6d9-a59c-4cb0-a88a-d020002caee8',
        'https://example.com/profiles/matthew.jpg',
        (SELECT id FROM Address WHERE address_line_1 = '123 Main St' AND address_line_2 IS NULL AND suburb = 'Sydney CBD' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '123 Main St' AND address_line_2 IS NULL AND suburb = 'Sydney CBD' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '2233445566', 'COMPANY',
        'Sid', 'Chintapally', 'Founder and CEO at tech firm.',
        '<EMAIL>', '3146f139-c0f6-466b-9717-092d6640cf01',
        'https://example.com/profiles/sid.jpg',
        (SELECT id FROM Address WHERE address_line_1 = '456 Corporate Blvd' AND address_line_2 IS NULL AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '456 Corporate Blvd' AND address_line_2 IS NULL AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '1133224455', 'INDIVIDUAL',
        'Saraswathi', 'Pasumarthi', 'Software Employee at Zessta Technologies.',
        '<EMAIL>', 'baf6783e-9b94-4c6e-85bd-2ce74bd712f0',
        'https://example.com/profiles/saraswathi.jpg',
        (SELECT id FROM Address WHERE address_line_1 = '567 Corporate Blvd' AND address_line_2 IS NULL AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '567 Corporate Blvd' AND address_line_2 IS NULL AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '1133442255', 'INDIVIDUAL',
        'Iswar Chandra', 'Rana', 'Software Employee at Zessta Technologies.',
        '<EMAIL>', '15fe5bb1-caa5-4574-9143-e3639ff75980',
        'https://example.com/profiles/iswar.jpg',
        (SELECT id FROM Address WHERE address_line_1 = '678 Corporate Blvd' AND address_line_2 IS NULL AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '678 Corporate Blvd' AND address_line_2 IS NULL AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '1122443355', 'INDIVIDUAL',
        'Gayani', 'Polireddy', 'Software Employee at Zessta Technologies.',
        '<EMAIL>', 'ab737c3c-75b9-4c68-8b57-530c6df177a7',
        'https://example.com/profiles/gayani.jpg',
        (SELECT id FROM Address WHERE address_line_1 = '789 Corporate Blvd' AND address_line_2 IS NULL AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '789 Corporate Blvd' AND address_line_2 IS NULL AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '1133224455', 'INDIVIDUAL',
        'Satyajit', 'Prusty', 'Software Employee at Zessta Technologies.',
        '<EMAIL>', 'dececa03-2f57-4e74-a056-7c3ea6888c11',
        'https://example.com/profiles/satyajit.jpg',
        (SELECT id FROM Address WHERE address_line_1 = '891 Corporate Blvd' AND address_line_2 IS NULL AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '891 Corporate Blvd' AND address_line_2 IS NULL AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '2211334455', 'INDIVIDUAL',
        'Vivek', 'Priyadarshee', 'Software Employee at Zessta Technologies.',
        '<EMAIL>', '0dc6db52-081e-4105-97a6-ce87b7a47024',
        'https://example.com/profiles/vivek.jpg',
        (SELECT id FROM Address WHERE address_line_1 = '912 Corporate Blvd' AND address_line_2 IS NULL AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '912 Corporate Blvd' AND address_line_2 IS NULL AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '1122335544', 'INDIVIDUAL',
        'Chandrashekar', 'Bemagone', 'Engineer at Zessta Technologies.',
        '<EMAIL>', '00e00e09-d27a-46dc-a919-889c3497cea4',
        'https://example.com/profiles/chandrashekar.jpg',
        (SELECT id FROM Address WHERE address_line_1 = '123 Corporate Blvd' AND address_line_2 IS NULL AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '123 Corporate Blvd' AND address_line_2 IS NULL AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '1144223355', 'INDIVIDUAL',
        'Addivita', 'Agarwal', 'Product Manager at Zessta Technologies.',
        '<EMAIL>', '055d8d54-b871-45af-a505-d011cc0c8c08',
        'https://example.com/profiles/addivita.jpg',
        (SELECT id FROM Address WHERE address_line_1 = '234 Corporate Blvd' AND address_line_2 IS NULL AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '234 Corporate Blvd' AND address_line_2 IS NULL AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '1122554433', 'INDIVIDUAL',
        'Anuradha', 'Mahapatra', 'UI/UX Designer at Zessta Technologies.',
        '<EMAIL>', 'd0b8c20e-a8fd-454c-a7e5-faaac664ff72',
        'https://example.com/profiles/anuradha.jpg',
        (SELECT id FROM Address WHERE address_line_1 = '345 Corporate Blvd' AND address_line_2 IS NULL AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '345 Corporate Blvd' AND address_line_2 IS NULL AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '1122334455', 'INDIVIDUAL',
        'Savannah', 'Curry', 'CTO at Arealytics.',
        '<EMAIL>', '3a064032-759c-4b09-af37-fc7d6e655c56',
        'https://example.com/profiles/savannah.jpg',
        (SELECT id FROM Address WHERE address_line_1 = '123 Main St' AND address_line_2 = 'Arealytics User' AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '123 Main St' AND address_line_2 = 'Arealytics User' AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '1122334455', 'INDIVIDUAL',
        'Doug', 'Curry', 'CEO at Arealytics.',
        '<EMAIL>', '8001101a-93dc-492f-a61d-6573fd05b22b',
        'https://example.com/profiles/dj.jpg',
        (SELECT id FROM Address WHERE address_line_1 = '234 Main St' AND address_line_2 = 'Arealytics User' AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '234 Main St' AND address_line_2 = 'Arealytics User' AND suburb = 'Docklands' AND address_type = 'PRIMARY' LIMIT 1)
    );

-- Insert Admin User Roles
INSERT INTO user_roles (is_active, created_at, modified_at, role_id, user_id, version)
SELECT true, NOW(), NOW(), r.id, u.id, 0
FROM roles r
         JOIN app_user u ON u.email = '<EMAIL>'
WHERE r.name = 'PLATFORM_ADMIN';

-- Assign 'INDIVIDUAL_USER' role to all individual users
INSERT INTO user_roles (is_active, created_at, modified_at, role_id, user_id, version)
SELECT true, NOW(), NOW(), r.id, u.id, 0
FROM roles r
         JOIN app_user u ON u.user_type = 'INDIVIDUAL'
WHERE r.name = 'INDIVIDUAL_USER' AND u.email != '<EMAIL>';

-- Assign 'COMPANY_ADMIN' role to Sid
INSERT INTO user_roles (is_active, created_at, modified_at, role_id, user_id, version)
SELECT true, NOW(), NOW(), r.id, u.id, 0
FROM roles r
         JOIN app_user u ON u.email = '<EMAIL>'
WHERE r.name = 'COMPANY_ADMIN';

-- Insert Accounts for Users
INSERT INTO account (balance, is_active, company_id, created_at, deleted_at, modified_at, user_id, version, currency, account_type)
SELECT 0.00, true, NULL, NOW(), NULL, NOW(), u.id, 0, 'AUD', 'PLATFORM'
FROM app_user u
WHERE u.email = '<EMAIL>';

INSERT INTO account (balance, is_active, company_id, created_at, deleted_at, modified_at, user_id, version, currency, account_type)
SELECT 0.00, true, NULL, NOW(), NULL, NOW(), u.id, 0, 'AUD', 'INDIVIDUAL'
FROM app_user u
WHERE u.email = '<EMAIL>';

INSERT INTO account (balance, is_active, company_id, created_at, deleted_at, modified_at, user_id, version, currency, account_type)
SELECT 0.00, true, NULL, NOW(), NULL, NOW(), u.id, 0, 'AUD', 'INDIVIDUAL'
FROM app_user u
WHERE u.user_type = 'INDIVIDUAL' AND u.email NOT IN ('<EMAIL>', '<EMAIL>');

-- Insert Companies
INSERT INTO Company (employee_count, is_active, created_at, deleted_at, modified_at, version, description, industry, name, ABN, ACN, website, primary_address_id, billing_address_id, billing_email, accounts_contact_name, accounts_contact_number)
VALUES
    (245, true, NOW(), NULL, NOW(), 0, 'Global real estate brokerage services', 'Real Estate', 'Savills', '1**********', '**********2', 'www.savills.com.au',
     (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'Company Address' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
     (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'Company Address' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
     '<EMAIL>', 'Demo Contact', '***********'),
    (133, true, NOW(), NULL, NOW(), 0, 'Leading real estate services firm', 'Real Estate', 'CBRE Sydney', '**********2', '**********3', 'www.cbre.com.au',
     (SELECT id FROM Address WHERE address_line_1 = '357-363 George St' AND address_line_2 = 'Levels 19 - 21' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
     (SELECT id FROM Address WHERE address_line_1 = '357-363 George St' AND address_line_2 = 'Levels 19 - 21' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
     '<EMAIL>', 'Demo Contact', '61293333333'),
    (124, true, NOW(), NULL, NOW(), 0, 'Global real estate advisory', 'Real Estate', 'Cushman & Wakefield', '**********3', '**********4', 'www.cushmanwakefield.com.au',
     (SELECT id FROM Address WHERE address_line_1 = '1 O''connell St' AND address_line_2 = 'Level 22' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
     (SELECT id FROM Address WHERE address_line_1 = '1 O''connell St' AND address_line_2 = 'Level 22' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
     '<EMAIL>', 'Demo Contact', '61282439999'),
    (110, true, NOW(), NULL, NOW(), 0, 'Property development and investment', 'Real Estate', 'Mirvac Limited', '**********4', '**********5', 'www.mirvac.com',
     (SELECT id FROM Address WHERE address_line_1 = '200 George St' AND address_line_2 = 'Company Address' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
     (SELECT id FROM Address WHERE address_line_1 = '200 George St' AND address_line_2 = 'Company Address' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
     '<EMAIL>', 'Demo Contact', '61290808000'),
    (80, true, NOW(), NULL, NOW(), 0, 'Property investment and management', 'Real Estate', 'Stockland', '**********5', '**********6', 'www.stockland.com.au',
     (SELECT id FROM Address WHERE address_line_1 = '133 Castlereagh St' AND address_line_2 = 'Levels 2, 22 - 29' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
     (SELECT id FROM Address WHERE address_line_1 = '133 Castlereagh St' AND address_line_2 = 'Levels 2, 22 - 29' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
     '<EMAIL>', 'Demo Contact', '61290352000'),
    (73, true, NOW(), NULL, NOW(), 0, 'Commercial real estate services', 'Real Estate', 'Bawdens', '**********6', '**********7', 'www.bawdens.com.au',
     (SELECT id FROM Address WHERE address_line_1 = '18 Ross St' AND address_line_2 = '201' AND suburb = 'Parramatta' AND address_type = 'PRIMARY' LIMIT 1),
     (SELECT id FROM Address WHERE address_line_1 = '18 Ross St' AND address_line_2 = '201' AND suburb = 'Parramatta' AND address_type = 'PRIMARY' LIMIT 1),
     '<EMAIL>', 'Demo Contact', '***********'),
    (65, true, NOW(), NULL, NOW(), 0, 'Real estate brokerage services', 'Real Estate', 'CI Australia', '**********8', '**********9', 'www.ciaustralia.com.au',
     (SELECT id FROM Address WHERE address_line_1 = '10 Spring St' AND address_line_2 = 'Company Address' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
     (SELECT id FROM Address WHERE address_line_1 = '10 Spring St' AND address_line_2 = 'Company Address' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
     '<EMAIL>', 'Demo Contact', '***********'),
    (62, true, NOW(), NULL, NOW(), 0, 'Real estate services in Sydney South West', 'Real Estate', 'Colliers International Sydney South West', '**********9', '***********', 'www.colliers.com.au',
     (SELECT id FROM Address WHERE address_line_1 = '5 Emerald Hills Blvd' AND address_line_2 = 'Shop 1a' AND suburb = 'Leppington' AND address_type = 'PRIMARY' LIMIT 1),
     (SELECT id FROM Address WHERE address_line_1 = '5 Emerald Hills Blvd' AND address_line_2 = 'Shop 1a' AND suburb = 'Leppington' AND address_type = 'PRIMARY' LIMIT 1),
     '<EMAIL>', 'Demo Contact', '***********'),
    (61, true, NOW(), NULL, NOW(), 0, 'Real estate services in Strathfield', 'Real Estate', 'Strathfield Partners', '***********', '***********', 'www.strathfieldpartners.com.au',
     (SELECT id FROM Address WHERE address_line_1 = '8 Churchill Ave' AND address_line_2 = 'Company Address' AND suburb = 'Strathfield' AND address_type = 'PRIMARY' LIMIT 1),
     (SELECT id FROM Address WHERE address_line_1 = '8 Churchill Ave' AND address_line_2 = 'Company Address' AND suburb = 'Strathfield' AND address_type = 'PRIMARY' LIMIT 1),
     '<EMAIL>', 'Demo Contact', '***********');

-- Insert Account for Arealytics Company
INSERT INTO account (balance, is_active, company_id, created_at, deleted_at, modified_at, user_id, version, currency, account_type)
SELECT 0.00, true, c.id, NOW(), NULL, NOW(), NULL, 0, 'AUD', 'COMPANY'
FROM company c
WHERE c.name = 'Arealytics';

-- -----------------------------------------------------------------------------
-- Insert Document Types, Product Code, Prices and Special Prices
-- -----------------------------------------------------------------------------
INSERT INTO document_type(created_at, deleted_at, is_active, modified_at, version, description, name, type_code)
VALUES (NOW(),NULL,true,NOW(),0,'Legal agreement for property purchase','Sale Agreement','MORTGAGE' );

INSERT INTO product_code (created_at, deleted_at, is_active, modified_at, version, product_code, name, description)
VALUES 
    (NOW(), NULL, true, NOW(), 0, 'CancelledTitleSearch', 'Cancelled title search', 'Search for cancelled property titles'),
    (NOW(), NULL, true, NOW(), 0, 'CatchmentLandProtectionCertificate', 'Catchment & Land Protection Cert', 'Certificate for catchment and land protection'),
    (NOW(), NULL, true, NOW(), 0, 'CrownCancelledTitleSearch', 'Crown Cancelled Title search', 'Search for cancelled crown titles'),
    (NOW(), NULL, true, NOW(), 0, 'CrownDealingSearch', 'Crown Dealing search', 'Search for crown dealing records'),
    (NOW(), NULL, true, NOW(), 0, 'CrownTitleSearch', 'Crown Title search', 'Search for crown property titles'),
    (NOW(), NULL, true, NOW(), 0, 'CrownTitleHistorySearch', 'Crown Title History search', 'Historical search for crown titles'),
    (NOW(), NULL, true, NOW(), 0, 'CrownInstrumentSearch', 'Crown Instrument search', 'Search for crown instrument records'),
    (NOW(), NULL, true, NOW(), 0, 'DealingActivityOnTitle', 'Dealing activity on title', 'Activity details for title dealings'),
    (NOW(), NULL, true, NOW(), 0, 'DealingEnquiry', 'Dealing enquiry', 'Enquiry for dealing information'),
    (NOW(), NULL, true, NOW(), 0, 'ElectronicLodgementFileDealing', 'Electronic Lodgement File dealing number search', 'Search for electronic lodgement dealings'),
    (NOW(), NULL, true, NOW(), 0, 'FieldNotesSearch', 'Field notes search', 'Search for field notes'),
    (NOW(), NULL, true, NOW(), 0, 'FinalSearch', 'Final search', 'Final title search'),
    (NOW(), NULL, true, NOW(), 0, 'InstrumentSearch', 'Instrument search', 'Search for instrument records'),
    (NOW(), NULL, true, NOW(), 0, 'IssueSearch', 'Issue search', 'Search for title issues'),
    (NOW(), NULL, true, NOW(), 0, 'LandIndexSearch', 'Land Index search', 'Search for land index records'),
    (NOW(), NULL, true, NOW(), 0, 'LandIndexBrowsing', 'Land Index browse', 'Browse land index records'),
    (NOW(), NULL, true, NOW(), 0, 'OwnersCorporationBasicReport', 'Owners Corporation Basic Report', 'Basic report for owners corporation'),
    (NOW(), NULL, true, NOW(), 0, 'OwnersCorporationPremiumReport', 'Owners Corporation Premium Report', 'Premium report for ownership records'),
    (NOW(), NULL, true, NOW(), 0, 'PlanSearchOwnersCorporation', 'Plan search (title diagram or plan) and an Owners Corporation basic report', 'Plan and owners corporation report'),
    (NOW(), NULL, true, NOW(), 0, 'PreLodgementSearch', 'Pre Lodgement Search', 'Check before lodgement'),
    (NOW(), NULL, true, NOW(), 0, 'PropertyTransactionAlertTitle3To6', 'Property Transaction Alert Service on Title (3-6 month subscription)', '3-6 month title transaction alert'),
    (NOW(), NULL, true, NOW(), 0, 'PropertyTransactionAlertTitle12Months', 'Property Transaction Alert Service on Title (12 month subscription)', '12 month title transaction alert'),
    (NOW(), NULL, true, NOW(), 0, 'PropertyTransactionAlertPlan3To6Months', 'Property Transaction Alert Service on Plan (3-6 month subscription)', '3-6 month plan transaction alert'),
    (NOW(), NULL, true, NOW(), 0, 'PropertyTransactionAlertPlan12Months', 'Property Transaction Alert Service on Plan (12 month subscription)', '12 month plan transaction alert'),
    (NOW(), NULL, true, NOW(), 0, 'SalesHistoryReport', 'Sales History Report (non wholesale)', 'Non-wholesale sales history report'),
    (NOW(), NULL, true, NOW(), 0, 'TitleSearchDiagram', 'Title search and diagram', 'Title search with diagram'),
    (NOW(), NULL, true, NOW(), 0, 'RegisterSearchStatement', 'Title search (Registered Search Statement)', 'Registered search statement'),
    (NOW(), NULL, true, NOW(), 0, 'DigitalRegisteredSearchStatement', 'Digital Registered Search Statement (DRSS)', 'Digital registered search statement'),
    (NOW(), NULL, true, NOW(), 0, 'TitleSearchDigitalRegisteredStatement', 'Title Search & Digital Registered Search Statement (DRSS)', 'Title search and DRSS'),
    (NOW(), NULL, true, NOW(), 0, 'CrownDigitalTitleSearch', 'Crown Digital Title Search', 'Digital search for crown titles'),
    (NOW(), NULL, true, NOW(), 0, 'TitleHistorySearch', 'Title history search', 'Historical title search'),
    (NOW(), NULL, true, NOW(), 0, 'TitlesAffectedByInstruments', 'Titles affected by Instruments', 'Search for titles affected by instruments'),
    (NOW(), NULL, true, NOW(), 0, 'VerifyTitleSearch', 'Verify title search', 'Verification of title search'),
    (NOW(), NULL, true, NOW(), 0, 'VendorStatementManual', 'Vendor Statement certificates Manual processing', 'Manually processed vendor statement'),
    (NOW(), NULL, true, NOW(), 0, 'VendorStatementElectronic', 'Vendor Statement certificates Electronic Processing (A2A)', 'Electronically processed vendor statement'),
    (NOW(), NULL, true, NOW(), 0, 'PlanningCertificateOnline', 'Planning Certificate (Sec 198) issued by the licensor (online)', 'Online planning certificate'),
    (NOW(), NULL, true, NOW(), 0, 'PlanningCertificateManual', 'Planning Certificate (Sec 198) issued by the licensor (manual)', 'Manually issued planning certificate'),
    (NOW(), NULL, true, NOW(), 0, 'WaterShareRecord', 'Water Share Record (non-wholesale)', 'Non-wholesale water share record'),
    (NOW(), NULL, true, NOW(), 0, 'HistoricalAerialPhotography', 'Historical Aerial Photography', 'Historical aerial photography records'),
    (NOW(), NULL, true, NOW(), 0, 'DigitalRegisteredStatementDeliveryFee', 'DRSS delivery fee', 'Delivery fee for DRSS'),
    (NOW(), NULL, true, NOW(), 0, 'CommemorativeTitleAuthentic', 'Commemorative title Authentic', 'Authentic commemorative title'),
    (NOW(), NULL, true, NOW(), 0, 'CommemorativeTitlePostage', 'Commemorative title Authentic Standard postage', 'Postage for authentic commemorative title');

-- Insert into document_price with corrected expiry_date select statement
INSERT INTO document_price (created_at, deleted_at, is_active, modified_at, version, base_price, effective_base_price, gst, effective_date, expiry_date, document_type_id, product_code_id)
VALUES
    (NOW(), NULL, true, NOW(), 0, 4.09, 6.09, 2.00, '2025-06-11 17:56:00', NULL, NULL, (SELECT id FROM product_code WHERE product_code = 'CrownDealingSearch')),
    (NOW(), NULL, true, NOW(), 0, 6.18, 8.18, 2.00, '2025-06-11 17:56:00', NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'CrownTitleSearch')),
    (NOW(), NULL, true, NOW(), 0, 10.40, 12.40, 2.00, '2025-06-11 17:56:00', NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'CrownTitleHistorySearch')),
    (NOW(), NULL, true, NOW(), 0, 4.21, 6.21, 2.00, '2025-06-11 17:56:00',NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'CrownInstrumentSearch')),
    (NOW(), NULL, true, NOW(), 0, 1.03, 3.03, 2.00, '2025-06-11 17:56:00',NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'DealingActivityOnTitle')),
    (NOW(), NULL, true, NOW(), 0, 4.09, 6.09, 2.00, '2025-06-11 17:56:00',NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'DealingEnquiry')),
    (NOW(), NULL, true, NOW(), 0, 4.09, 6.09, 2.00, '2025-06-11 17:56:00',NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'ElectronicLodgementFileDealing')),
    (NOW(), NULL, true, NOW(), 0, 0.00, 2.00, 2.00, '2025-06-11 17:56:00',NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'FieldNotesSearch')),
    (NOW(), NULL, true, NOW(), 0, 3.10, 5.10, 2.00, '2025-06-11 17:56:00',NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'FinalSearch')),
    (NOW(), NULL, true, NOW(), 0, 4.40, 6.40, 2.00, '2025-06-11 17:56:00',NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'InstrumentSearch')),
    (NOW(), NULL, true, NOW(), 0, 4.09, 6.09, 2.00, '2025-06-11 17:56:00',NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'IssueSearch')),
    (NOW(), NULL, true, NOW(), 0, 7.22, 9.22, 2.00, '2025-06-11 17:56:00',NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'LandIndexSearch')),
    (NOW(), NULL, true, NOW(), 0, 0.00, 2.00, 2.00, '2025-06-11 17:56:00',NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'LandIndexBrowsing')),
    (NOW(), NULL, true, NOW(), 0, 4.40, 6.40, 2.00, '2025-06-11 17:56:00',NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'OwnersCorporationBasicReport')),
    (NOW(), NULL, true, NOW(), 0, 7.22, 9.22, 2.00, '2025-06-11 17:56:00',NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'OwnersCorporationPremiumReport')),
    (NOW(), NULL, true, NOW(), 0, 6.40, 8.40, 2.00, '2025-06-11 17:56:00',NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'PlanSearchOwnersCorporation')),
    (NOW(), NULL, true, NOW(), 0, 4.09, 6.09, 2.00, '2025-06-11 17:56:00',NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'PreLodgementSearch')),
    (NOW(), NULL, true, NOW(), 0, 5.16, 7.16, 2.00, '2025-06-11 17:56:00',NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'PropertyTransactionAlertTitle3To6')),
    (NOW(), NULL, true, NOW(), 0, 10.33, 12.33, 2.00, '2025-06-11 17:56:00',NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'PropertyTransactionAlertTitle12Months')),
    (NOW(), NULL, true, NOW(), 0, 4.09, 6.09, 2.00, '2025-06-11 17:56:00',NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'PropertyTransactionAlertPlan3To6Months')),
    (NOW(), NULL, true, NOW(), 0, 8.20, 10.20, 2.00, '2025-06-11 17:56:00',NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'PropertyTransactionAlertPlan12Months')),
    (NOW(), NULL, true, NOW(), 0, 2.40, 4.40, 2.00, '2025-06-11 17:56:00',NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'SalesHistoryReport')),
    (NOW(), NULL, true, NOW(), 0, 12.80, 14.80, 2.00, '2025-06-11 17:56:00',NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'TitleSearchDiagram')),
    (NOW(), NULL, true, NOW(), 0, 6.40, 8.40, 2.00, '2025-06-11 17:56:00',NULL,  NULL, (SELECT id FROM product_code WHERE product_code = 'RegisterSearchStatement'));

-- Insert into company_special_price with corrected expiry_date
INSERT INTO company_special_price (created_at, deleted_at, is_active, modified_at, version, effective_date, expiry_date, gst, special_price, company_id, document_type_id, product_code_id)
VALUES
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 13.27, 3.09, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'CrownDealingSearch')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 11.97, 5.18, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'CrownTitleSearch')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 11.06, 9.40, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'CrownTitleHistorySearch')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 13.08, 3.21, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'CrownInstrumentSearch')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 100.00, 0.03, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'DealingActivityOnTitle')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 13.27, 3.09, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'DealingEnquiry')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 13.27, 3.09, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'ElectronicLodgementFileDealing')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 0.00, 0.00, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'FieldNotesSearch')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 0.00, 2.10, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'FinalSearch')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 0.00, 3.40, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'InstrumentSearch')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 13.27, 3.09, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'IssueSearch')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 11.58, 6.22, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'LandIndexSearch')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 0.00, 0.00, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'LandIndexBrowsing')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 0.00, 3.40, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'OwnersCorporationBasicReport')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 11.58, 6.22, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'OwnersCorporationPremiumReport')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 0.00, 5.40, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'PlanSearchOwnersCorporation')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 13.27, 3.09, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'PreLodgementSearch')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 12.50, 4.16, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'PropertyTransactionAlertTitle3To6')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 11.04, 9.33, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'PropertyTransactionAlertTitle12Months')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 13.27, 3.09, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'PropertyTransactionAlertPlan3To6Months')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 11.39, 7.20, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'PropertyTransactionAlertPlan12Months')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 17.14, 1.40, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'SalesHistoryReport')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 0.00, 11.80, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'TitleSearchDiagram')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 0.00, 5.40, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'RegisterSearchStatement')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 0.00, 5.40, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'DigitalRegisteredSearchStatement')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 0.00, 5.40, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'TitleSearchDigitalRegisteredStatement')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 11.97, 5.18, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'CrownDigitalTitleSearch')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 0.00, 9.80, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'TitleHistorySearch')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 13.27, 3.09, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'TitlesAffectedByInstruments')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 13.27, 3.09, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'VerifyTitleSearch')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 11.98, 5.26, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'VendorStatementManual')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 35.90, 0.39, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE name = 'Vendor Statement certificates Electronic Processing (A2A)')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 0.00, 6.82, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'PlanningCertificateOnline')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 0.00, 23.50, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'PlanningCertificateManual')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 0.00, 15.30, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'WaterShareRecord')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 11.49, 6.96, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'HistoricalAerialPhotography')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 12.44, 4.18, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'DigitalRegisteredStatementDeliveryFee')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 10.19, 55.68, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'CommemorativeTitleAuthentic')),
    (NOW(), NULL, true, NOW(), 0, '2025-06-11 17:56:00', NULL, 10.69, 14.40, (SELECT id FROM company WHERE name = 'Arealytics'), NULL, (SELECT id FROM product_code WHERE product_code = 'CommemorativeTitlePostage'));

-- Link Sid to Arealytics Company
INSERT INTO company_membership (created_at, deleted_at, is_active, modified_at, version, role_id, user_id, company_id, status)
SELECT NOW(), NULL, true, NOW(), 0, r.id, u.id, c.id, 'ACTIVE'
FROM roles r
         JOIN app_user u ON u.email = '<EMAIL>'
         JOIN company c ON c.name = 'Arealytics'
WHERE r.name = 'COMPANY_ADMIN';

-- Insert Accounts for New Companies
INSERT INTO account (balance, is_active, company_id, created_at, deleted_at, modified_at, user_id, version, currency, account_type)
SELECT 0.00, true, c.id, NOW(), NULL, NOW(), NULL, 0, 'AUD', 'COMPANY'
FROM company c
WHERE c.name IN (
                 'Savills', 'CBRE Sydney', 'Cushman & Wakefield', 'Mirvac Limited', 'Stockland', 'Bawdens',
                 'CI Australia', 'Colliers International Sydney South West', 'Strathfield Partners'
    );

-- Create Default Root and Downloads Folders for Users
INSERT INTO folder (created_at, deleted_at, is_active, modified_at, version, name, user_id, company_id, folder_type, parent_folder_id)
SELECT NOW(), NULL, true, NOW(), 0, 'Root', u.id, NULL, 'ROOT', NULL
FROM app_user u
WHERE u.user_type = 'INDIVIDUAL' AND u.email NOT IN ('<EMAIL>', '<EMAIL>');

INSERT INTO folder (created_at, deleted_at, is_active, modified_at, version, name, user_id, company_id, folder_type, parent_folder_id)
SELECT NOW(), NULL, true, NOW(), 0, 'Downloads', u.id, NULL, 'DOWNLOADS', f.id
FROM app_user u
         JOIN folder f ON f.user_id = u.id AND f.folder_type = 'ROOT'
WHERE u.user_type = 'INDIVIDUAL' AND u.email NOT IN ('<EMAIL>', '<EMAIL>');

-- Create Default Root and Downloads Folders for Companies
INSERT INTO folder (created_at, deleted_at, is_active, modified_at, version, name, user_id, company_id, folder_type, parent_folder_id)
SELECT NOW(), NULL, true, NOW(), 0, 'Root', NULL, c.id, 'ROOT', NULL
FROM company c
WHERE c.name IN (
                 'Savills', 'CBRE Sydney', 'Cushman & Wakefield', 'Mirvac Limited', 'Stockland', 'Bawdens',
                 'CI Australia', 'Colliers International Sydney South West', 'Strathfield Partners'
    );

INSERT INTO folder (created_at, deleted_at, is_active, modified_at, version, name, user_id, company_id, folder_type, parent_folder_id)
SELECT NOW(), NULL, true, NOW(), 0, 'Downloads', NULL, c.id, 'DOWNLOADS', f.id
FROM company c
         JOIN folder f ON f.company_id = c.id AND f.folder_type = 'ROOT'
WHERE c.name IN (
                 'Savills', 'CBRE Sydney', 'Cushman & Wakefield', 'Mirvac Limited', 'Stockland', 'Bawdens',
                 'CI Australia', 'Colliers International Sydney South West', 'Strathfield Partners'
    );

-- Addresses for Company Users
INSERT INTO Address (address_line_1, address_line_2, suburb, state_id, zip_code_id, address_type, created_at, modified_at, version, is_active)
VALUES
    -- Savills User Addresses
    ('1 Farrer Place', 'User: Aaron McLean', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('1 Farrer Place', 'User: Aaron Weakley', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('1 Farrer Place', 'User: Adam Nichols', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('1 Farrer Place', 'User: Adam Thomas', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('1 Farrer Place', 'User: Adele Eagleton', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('1 Farrer Place', 'User: Adrian Bokolis', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('1 Farrer Place', 'User: Alan Beaver', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('1 Farrer Place', 'User: Aleksandra Gorgieva', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('1 Farrer Place', 'User: Alex Prenzel', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('1 Farrer Place', 'User: Alissan Serra', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('1 Farrer Place', 'User: Alister Wishart', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('1 Farrer Place', 'User: Allison Gurney', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('1 Farrer Place', 'User: Allison Gurney', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'BILLING', NOW(), NOW(), 0, true),
    -- CBRE Sydney User Addresses
    ('357-363 George St', 'User: Alex Mirzaian', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('357-363 George St', 'User: Amy Pfeiffer', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('357-363 George St', 'User: Ben Byford', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('357-363 George St', 'User: Tristan Winter', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('357-363 George St', 'User: Ben Cohen', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('357-363 George St', 'User: Tom Bowditch', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    -- Cushman & Wakefield User Addresses
    ('1 O''connell St', 'User: Antonia Foweraker', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('1 O''connell St', 'User: Rosie Origlia', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('1 O''connell St', 'User: Tim Courtnall', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('1 O''connell St', 'User: Yosh Mendis', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('1 O''connell St', 'User: James Linacre', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('1 O''connell St', 'User: Billy Dent', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('1 O''connell St', 'User: Richard Pearce', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    -- Mirvac Limited User Addresses
    ('200 George St', 'User: Greg McManus', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('200 George St', 'User: Johanna Watson', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('200 George St', 'User: Cassandra Nail', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('200 George St', 'User: Paul Bezzina', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('200 George St', 'User: Puneet Menegazzo', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('200 George St', 'User: Patrick Nott', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    -- Stockland User Addresses
    ('133 Castlereagh St', 'User: Andrew Whitson', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('133 Castlereagh St', 'User: Simon Shakesheff', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('133 Castlereagh St', 'User: John Schroder', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('133 Castlereagh St', 'User: Michael Rosmarin', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('133 Castlereagh St', 'User: Darren Rehn', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('133 Castlereagh St', 'User: Tiernan O''Rourke', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    -- Bawdens User Addresses
    ('18 Ross St', 'User: Barry Cawthorn', 'Parramatta', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2150' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('18 Ross St', 'User: Robert Ally', 'Parramatta', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2150' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('18 Ross St', 'User: Terry Cawd', 'Parramatta', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2150' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('18 Ross St', 'User: Peter Pacetta', 'Parramatta', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2150' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('18 Ross St', 'User: Graeme Scott', 'Parramatta', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2150' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    -- CI Australia User Addresses
    ('10 Spring St', 'User: Ben Kardachi', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('10 Spring St', 'User: Charlotte Hesketh', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('10 Spring St', 'User: Andrew Hunter', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('10 Spring St', 'User: Michael Stokes', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('10 Spring St', 'User: Rebecca Roberts', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('10 Spring St', 'User: Fergus Woollcombe', 'Sydney', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2000' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    -- Colliers User Addresses
    ('5 Emerald Hills Blvd', 'User: Gavin Bishop', 'Leppington', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2179' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('5 Emerald Hills Blvd', 'User: Bernard Peverill', 'Leppington', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2179' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('5 Emerald Hills Blvd', 'User: David Khoury', 'Leppington', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2179' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('5 Emerald Hills Blvd', 'User: Josh Heffernan', 'Leppington', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2179' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('5 Emerald Hills Blvd', 'User: Michael Crombie', 'Leppington', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2179' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('5 Emerald Hills Blvd', 'User: Tom Barnier', 'Leppington', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2179' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    -- Strathfield Partners User Addresses
    ('8 Churchill Ave', 'User: Robert Pignataro', 'Strathfield', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2135' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('8 Churchill Ave', 'User: Francesco Portolesi', 'Strathfield', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2135' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('8 Churchill Ave', 'User: Hawre Ahmad', 'Strathfield', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2135' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('8 Churchill Ave', 'User: Vanessa Kim', 'Strathfield', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2135' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('8 Churchill Ave', 'User: Tina Xiao Yan Tang', 'Strathfield', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2135' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true),
    ('8 Churchill Ave', 'User: James Jitao Zhao', 'Strathfield', (SELECT id FROM States WHERE state_abbr = 'NSW' LIMIT 1), (SELECT id FROM zip_codes WHERE zip_code = '2135' LIMIT 1), 'PRIMARY', NOW(), NOW(), 0, true);

-- Savills Users
INSERT INTO app_user (
    is_active, created_at, deleted_at, last_login, modified_at, version, contact_number, user_type,
    first_name, last_name, bio, email, keycloak_id, profile_picture_url,
    primary_address_id, billing_address_id
)
VALUES
    (
        true, NOW(), NULL, NULL, NOW(), 0, '61282156035', 'COMPANY',
        'Aaron', 'McLean', 'Director / Metropolitan & Regional Sales',
        '<EMAIL>', 'a1b2c3d4-e5f6-7890-abcd-ef**********',
        'NoPhoto_Profile.png',
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Aaron McLean' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Aaron McLean' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '61289134816', 'COMPANY',
        'Aaron', 'Weakley', 'Project Manager',
        '<EMAIL>', '604b2300-2ef9-461f-901a-8ffd0365a8be',
        'NoPhoto_Profile.png',
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Aaron Weakley' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Aaron Weakley' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '61282156082', 'COMPANY',
        'Adam', 'Nichols', 'Project Manager',
        '<EMAIL>', '93a290fb-1ac1-45fd-bfc8-b7318f60d115',
        'NoPhoto_Profile.png',
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Adam Nichols' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Adam Nichols' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '61282158948', 'COMPANY',
        'Adam', 'Thomas', 'Senior Director / Project Management',
        '<EMAIL>', '104a560b-6a82-4523-a6cd-119a9e1fcf97',
        'NoPhoto_Profile.png',
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Adam Thomas' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Adam Thomas' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '61282156069', 'COMPANY',
        'Adele', 'Eagleton', 'State Director / NSW Project Management',
        '<EMAIL>', '809094e2-fac2-4b9d-9272-98d82a9cb04e',
        'NoPhoto_Profile.png',
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Adele Eagleton' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Adele Eagleton' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '61282158873', 'COMPANY',
        'Adrian', 'Bokolis', 'Assistant Valuer / Valuation & Advisory',
        '<EMAIL>', '5913f604-5715-4a04-bd87-531546869b8a',
        'NoPhoto_Profile.png',
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Adrian Bokolis' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Adrian Bokolis' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '61416252837', 'COMPANY',
        'Alan', 'Beaver', 'Project Director / Project Management',
        '<EMAIL>', 'd22ca0af-44ba-4550-b245-45a869bdec23',
        'NoPhoto_Profile.png',
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Alan Beaver' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Alan Beaver' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '61282156021', 'COMPANY',
        'Aleksandra', 'Gorgieva', 'Project Manager / Project Management',
        '<EMAIL>', 'cf14384b-9543-4fd7-8c8b-26fe0509852b',
        'NoPhoto_Profile.png',
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Aleksandra Gorgieva' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Aleksandra Gorgieva' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '61289134811', 'COMPANY',
        'Alex', 'Prenzel', 'Associate Director / Project Management',
        '<EMAIL>', '7e2f9a31-2804-4912-80b8-9a9fa275dd26',
        'NoPhoto_Profile.png',
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Alex Prenzel' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Alex Prenzel' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '***********', 'COMPANY',
        'Alissan', 'Serra', 'Senior Project Manager / Business Planning Project Management',
        '<EMAIL>', '54d4b3e1-a1de-49fc-abc0-7e31902eb775',
        'NoPhoto_Profile.png',
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Alissan Serra' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Alissan Serra' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '***********', 'COMPANY',
        'Alister', 'Wishart', 'Facilities Manager / Asset Management',
        '<EMAIL>', '70b39be9-80dc-40fb-b368-20c94687e440',
        'NoPhoto_Profile.png',
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Alister Wishart' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Alister Wishart' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '61282158822', 'COMPANY',
        'Allison', 'Gurney', 'Executive Administrator / Corporate Services',
        '<EMAIL>', '0d997b3a-4dc2-462c-af52-36d0cf5ceef8',
        'NoPhoto_Profile.png',
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Allison Gurney' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '1 Farrer Place' AND address_line_2 = 'User: Allison Gurney' AND suburb = 'Sydney' AND address_type = 'BILLING' LIMIT 1)
    );

-- Company Member Role for Savills users
INSERT INTO user_roles (is_active, created_at, modified_at, role_id, user_id, version)
SELECT true, NOW(), NOW(), r.id, u.id, 0
FROM roles r
         JOIN app_user u ON u.email IN (
                                        '<EMAIL>', '<EMAIL>',
                                        '<EMAIL>', '<EMAIL>', '<EMAIL>',
                                        '<EMAIL>', '<EMAIL>', '<EMAIL>',
                                        '<EMAIL>', '<EMAIL>', '<EMAIL>'
    )
WHERE r.name = 'COMPANY_MEMBER';

-- Company Admin Role for Savills users
INSERT INTO user_roles (is_active, created_at, modified_at, role_id, user_id, version)
SELECT true, NOW(), NOW(), r.id, u.id, 0
FROM roles r
         JOIN app_user u ON u.email = '<EMAIL>'
WHERE r.name = 'COMPANY_ADMIN';

-- Make One User Company Admin from Savills
INSERT INTO company_membership (created_at, deleted_at, is_active, modified_at, version, role_id, user_id, company_id, status)
SELECT NOW(), NULL, true, NOW(), 0, r.id, u.id, c.id, 'ACTIVE'
FROM roles r
         JOIN app_user u ON u.email = '<EMAIL>'
         JOIN company c ON c.name = 'Savills'
WHERE r.name = 'COMPANY_ADMIN';

-- Savills users
INSERT INTO company_membership (created_at, deleted_at, is_active, modified_at, version, role_id, user_id, company_id, status)
SELECT NOW(), NULL, true, NOW(), 0, r.id, u.id, c.id, 'ACTIVE'
FROM roles r
         JOIN app_user u ON u.email IN (
                                        '<EMAIL>', '<EMAIL>',
                                        '<EMAIL>', '<EMAIL>', '<EMAIL>',
                                        '<EMAIL>', '<EMAIL>', '<EMAIL>',
                                        '<EMAIL>', '<EMAIL>', '<EMAIL>'
    )
         JOIN company c ON c.name = 'Savills'
WHERE r.name = 'COMPANY_MEMBER';

-- CBRE Sydney Users
INSERT INTO app_user (
    is_active, created_at, deleted_at, last_login, modified_at, version, contact_number, user_type,
    first_name, last_name, bio, email, keycloak_id, profile_picture_url,
    primary_address_id, billing_address_id
)
VALUES
    (
        true, NOW(), NULL, NULL, NOW(), 0, '61400523523', 'COMPANY',
        'Alex', 'Mirzaian', 'Associate Director',
        '<EMAIL>', 'ed6a474d-ddc3-4442-8d12-c2dcf9a335d6',
        'NoPhoto_Profile.png',
        (SELECT id FROM Address WHERE address_line_1 = '357-363 George St' AND address_line_2 = 'User: Alex Mirzaian' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '357-363 George St' AND address_line_2 = 'Levels 19 - 21' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '61401198401', 'COMPANY',
        'Amy', 'Pfeiffer', 'Sales and Leasing Agent',
        '<EMAIL>', 'd709e7ee-ed9f-4293-ad95-ea2b9776cba7',
        'NoPhoto_Profile.png',
        (SELECT id FROM Address WHERE address_line_1 = '357-363 George St' AND address_line_2 = 'User: Amy Pfeiffer' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '357-363 George St' AND address_line_2 = 'Levels 19 - 21' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, '2020-06-17 04:19:23', NOW(), 0, '61414722935', 'COMPANY',
        'Ben', 'Byford', 'Sales and Leasing Agent',
        '<EMAIL>', 'd63cfdc4-409d-4224-89e2-d4ad6b2e7866',
        'NoPhoto_Profile.png',
        (SELECT id FROM Address WHERE address_line_1 = '357-363 George St' AND address_line_2 = 'User: Ben Byford' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '357-363 George St' AND address_line_2 = 'Levels 19 - 21' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '61413874782', 'COMPANY',
        'Tristan', 'Winter', 'Sales and Leasing Agent',
        '<EMAIL>', 'f680762c-37c3-46a2-a7fe-547357f3e91f',
        'NoPhoto_Profile.png',
        (SELECT id FROM Address WHERE address_line_1 = '357-363 George St' AND address_line_2 = 'User: Tristan Winter' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '357-363 George St' AND address_line_2 = 'Levels 19 - 21' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        false, NOW(), NULL, '2020-05-28 07:30:23', NOW(), 0, '61293333409', 'COMPANY',
        'Ben', 'Cohen', 'Sales and Leasing Agent',
        '<EMAIL>', '0980f89b-ebc2-4f54-819a-850ed47db74d',
        'NoPhoto_Profile.png',
        (SELECT id FROM Address WHERE address_line_1 = '357-363 George St' AND address_line_2 = 'User: Ben Cohen' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '357-363 George St' AND address_line_2 = 'Levels 19 - 21' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
    ),
    (
        true, NOW(), NULL, NULL, NOW(), 0, '61424292404', 'COMPANY',
        'Tom', 'Bowditch', 'Negotiator',
        '<EMAIL>', '1efb39e7-69a7-48ba-adbc-1cc149e451a1',
        'NoPhoto_Profile.png',
        (SELECT id FROM Address WHERE address_line_1 = '357-363 George St' AND address_line_2 = 'User: Tom Bowditch' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
        (SELECT id FROM Address WHERE address_line_1 = '357-363 George St' AND address_line_2 = 'Levels 19 - 21' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
    );

-- Company Member Role for CBRE Sydney users
INSERT INTO user_roles (is_active, created_at, modified_at, role_id, user_id, version)
SELECT true, NOW(), NOW(), r.id, u.id, 0
FROM roles r
         JOIN app_user u ON u.email IN (
                                        '<EMAIL>',
                                        '<EMAIL>',
                                        '<EMAIL>',
                                        '<EMAIL>',
                                        '<EMAIL>'
    )
WHERE r.name = 'COMPANY_MEMBER';

-- Company Admin Role for CBRE Sydney users
INSERT INTO user_roles (is_active, created_at, modified_at, role_id, user_id, version)
SELECT true, NOW(), NOW(), r.id, u.id, 0
FROM roles r
         JOIN app_user u ON u.email = '<EMAIL>'
WHERE r.name = 'COMPANY_ADMIN';

-- Make Alex Mirzaian Company Admin for CBRE Sydney
INSERT INTO company_membership (created_at, deleted_at, is_active, modified_at, version, role_id, user_id, company_id, status)
SELECT NOW(), NULL, true, NOW(), 0, r.id, u.id, c.id, 'ACTIVE'
FROM roles r
         JOIN app_user u ON u.email = '<EMAIL>'
         JOIN company c ON c.name = 'CBRE Sydney'
WHERE r.name = 'COMPANY_ADMIN';

-- CBRE Sydney users as company members
INSERT INTO company_membership (created_at, deleted_at, is_active, modified_at, version, role_id, user_id, company_id, status)
SELECT NOW(), NULL, true, NOW(), 0, r.id, u.id, c.id,
       CASE WHEN u.email = '<EMAIL>' THEN 'INACTIVE' ELSE 'ACTIVE' END
FROM roles r
         JOIN app_user u ON u.email IN (
                                        '<EMAIL>',
                                        '<EMAIL>',
                                        '<EMAIL>',
                                        '<EMAIL>',
                                        '<EMAIL>'
    )
         JOIN company c ON c.name = 'CBRE Sydney'
WHERE r.name = 'COMPANY_MEMBER';

-- Cushman & Wakefield Users
INSERT INTO app_user (
    is_active, created_at, deleted_at, last_login, modified_at, version, contact_number, user_type,
    first_name, last_name, bio, email, keycloak_id, profile_picture_url,
    primary_address_id, billing_address_id
)
VALUES
    (
        false, NOW(), NULL, NULL, NOW(), 0, '61434355933', 'COMPANY',
        'Antonia', 'Foweraker', 'Director - Office Leasing, NSW',
        '<EMAIL>', 'a1b2c3d4-e5f6-7890-abcd-ef1234567892',
        'NoPhoto_Profile.png',
        (SELECT id FROM Address WHERE address_line_1 = '1 O''connell St' AND address_line_2 = 'User: Antonia Foweraker' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '1 O''connell St' AND address_line_2 = 'Level 22' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61434355934', 'COMPANY',
'Rosie', 'Origlia', 'Associate Director - Office Leasing',
'<EMAIL>', 'b2c3d4e5-f6a7-8901-bcde-f**********4',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '1 O''connell St' AND address_line_2 = 'User: Rosie Origlia' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '1 O''connell St' AND address_line_2 = 'Level 22' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61434355935', 'COMPANY',
'Tim', 'Courtnall', 'Director - Office Leasing',
'<EMAIL>', 'c3d4e5f6-a7b8-9012-cdef-345678901236',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '1 O''connell St' AND address_line_2 = 'User: Tim Courtnall' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '1 O''connell St' AND address_line_2 = 'Level 22' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61434355936', 'COMPANY',
'Yosh', 'Mendis', 'Associate Director - Office Leasing',
'<EMAIL>', 'd4e5f6a7-b8c9-0123-def0-456789012347',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '1 O''connell St' AND address_line_2 = 'User: Yosh Mendis' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '1 O''connell St' AND address_line_2 = 'Level 22' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61434355937', 'COMPANY',
'James', 'Linacre', 'Director - Office Leasing',
'<EMAIL>', 'e5f6a7b8-c9d0-1234-ef01-567890123458',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '1 O''connell St' AND address_line_2 = 'User: James Linacre' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '1 O''connell St' AND address_line_2 = 'Level 22' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61434355938', 'COMPANY',
'Billy', 'Dent', 'Associate Director - Office Leasing',
'<EMAIL>', 'f6a7b8c9-d0e1-2345-f012-678901234569',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '1 O''connell St' AND address_line_2 = 'User: Billy Dent' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '1 O''connell St' AND address_line_2 = 'Level 22' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61434355939', 'COMPANY',
'Richard', 'Pearce', 'Director - Office Leasing',
'<EMAIL>', 'a7b8c9d0-e1f2-3456-0123-789012345679',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '1 O''connell St' AND address_line_2 = 'User: Richard Pearce' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '1 O''connell St' AND address_line_2 = 'Level 22' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
);

-- Company Member Role for Cushman & Wakefield users
INSERT INTO user_roles (is_active, created_at, modified_at, role_id, user_id, version)
SELECT true, NOW(), NOW(), r.id, u.id, 0
FROM roles r
JOIN app_user u ON u.email IN (
'<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>'
)
WHERE r.name = 'COMPANY_MEMBER';

-- Company Admin Role for Cushman & Wakefield user
INSERT INTO user_roles (is_active, created_at, modified_at, role_id, user_id, version)
SELECT true, NOW(), NOW(), r.id, u.id, 0
FROM roles r
JOIN app_user u ON u.email = '<EMAIL>'
WHERE r.name = 'COMPANY_ADMIN';

-- Make Antonia Foweraker Company Admin for Cushman & Wakefield
INSERT INTO company_membership (created_at, deleted_at, is_active, modified_at, version, role_id, user_id, company_id, status)
SELECT NOW(), NULL, true, NOW(), 0, r.id, u.id, c.id, 'INACTIVE'
FROM roles r
JOIN app_user u ON u.email = '<EMAIL>'
JOIN company c ON c.name = 'Cushman & Wakefield'
WHERE r.name = 'COMPANY_ADMIN';

-- Cushman & Wakefield users as company members
INSERT INTO company_membership (created_at, deleted_at, is_active, modified_at, version, role_id, user_id, company_id, status)
SELECT NOW(), NULL, true, NOW(), 0, r.id, u.id, c.id, 'ACTIVE'
FROM roles r
JOIN app_user u ON u.email IN (
'<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>'
)
JOIN company c ON c.name = 'Cushman & Wakefield'
WHERE r.name = 'COMPANY_MEMBER';

-- Mirvac Limited Users
INSERT INTO app_user (
is_active, created_at, deleted_at, last_login, modified_at, version, contact_number, user_type,
first_name, last_name, bio, email, keycloak_id, profile_picture_url,
primary_address_id, billing_address_id
)
VALUES
(
true, NOW(), NULL, NULL, NOW(), 0, '61290808001', 'COMPANY',
'Greg', 'McManus', 'Development Manager',
'<EMAIL>', 'a1b2c3d4-e5f6-7890-abcd-ef1234567893',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '200 George St' AND address_line_2 = 'User: Greg McManus' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '200 George St' AND address_line_2 = 'Company Address' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61290808002', 'COMPANY',
'Johanna', 'Watson', 'Project Manager',
'<EMAIL>', 'b2c3d4e5-f6a7-8901-bcde-f**********5',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '200 George St' AND address_line_2 = 'User: Johanna Watson' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '200 George St' AND address_line_2 = 'Company Address' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61290808003', 'COMPANY',
'Cassandra', 'Nail', 'Leasing Manager',
'<EMAIL>', 'c3d4e5f6-a7b8-9012-cdef-345678901237',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '200 George St' AND address_line_2 = 'User: Cassandra Nail' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '200 George St' AND address_line_2 = 'Company Address' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61290808004', 'COMPANY',
'Paul', 'Bezzina', 'Development Manager',
'<EMAIL>', 'd4e5f6a7-b8c9-0123-def0-456789012348',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '200 George St' AND address_line_2 = 'User: Paul Bezzina' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '200 George St' AND address_line_2 = 'Company Address' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61290808005', 'COMPANY',
'Puneet', 'Menegazzo', 'Project Manager',
'<EMAIL>', 'e5f6a7b8-c9d0-1234-ef01-567890123459',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '200 George St' AND address_line_2 = 'User: Puneet Menegazzo' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '200 George St' AND address_line_2 = 'Company Address' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61290808006', 'COMPANY',
'Patrick', 'Nott', 'Leasing Manager',
'<EMAIL>', 'f6a7b8c9-d0e1-2345-f012-678901234570',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '200 George St' AND address_line_2 = 'User: Patrick Nott' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '200 George St' AND address_line_2 = 'Company Address' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
);

-- Company Member Role for Mirvac Limited users
INSERT INTO user_roles (is_active, created_at, modified_at, role_id, user_id, version)
SELECT true, NOW(), NOW(), r.id, u.id, 0
FROM roles r
JOIN app_user u ON u.email IN (
'<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>',
'<EMAIL>'
)
WHERE r.name = 'COMPANY_MEMBER';

-- Company Admin Role for Mirvac Limited user
INSERT INTO user_roles (is_active, created_at, modified_at, role_id, user_id, version)
SELECT true, NOW(), NOW(), r.id, u.id, 0
FROM roles r
JOIN app_user u ON u.email = '<EMAIL>'
WHERE r.name = 'COMPANY_ADMIN';

-- Make Greg McManus Company Admin for Mirvac Limited
INSERT INTO company_membership (created_at, deleted_at, is_active, modified_at, version, role_id, user_id, company_id, status)
SELECT NOW(), NULL, true, NOW(), 0, r.id, u.id, c.id, 'ACTIVE'
FROM roles r
JOIN app_user u ON u.email = '<EMAIL>'
JOIN company c ON c.name = 'Mirvac Limited'
WHERE r.name = 'COMPANY_ADMIN';

-- Mirvac Limited users as company members
INSERT INTO company_membership (created_at, deleted_at, is_active, modified_at, version, role_id, user_id, company_id, status)
SELECT NOW(), NULL, true, NOW(), 0, r.id, u.id, c.id, 'ACTIVE'
FROM roles r
JOIN app_user u ON u.email IN (
'<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>',
'<EMAIL>'
)
JOIN company c ON c.name = 'Mirvac Limited'
WHERE r.name = 'COMPANY_MEMBER';

-- Stockland Users
INSERT INTO app_user (
is_active, created_at, deleted_at, last_login, modified_at, version, contact_number, user_type,
first_name, last_name, bio, email, keycloak_id, profile_picture_url,
primary_address_id, billing_address_id
)
VALUES
(
true, NOW(), NULL, NULL, NOW(), 0, '61290352001', 'COMPANY',
'Andrew', 'Whitson', 'Development Manager',
'<EMAIL>', 'a1b2c3d4-e5f6-7890-abcd-ef1234567894',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '133 Castlereagh St' AND address_line_2 = 'User: Andrew Whitson' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '133 Castlereagh St' AND address_line_2 = 'Levels 2, 22 - 29' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61290352002', 'COMPANY',
'Simon', 'Shakesheff', 'Project Manager',
'<EMAIL>', 'b2c3d4e5-f6a7-8901-bcde-f**********6',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '133 Castlereagh St' AND address_line_2 = 'User: Simon Shakesheff' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '133 Castlereagh St' AND address_line_2 = 'Levels 2, 22 - 29' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61290352003', 'COMPANY',
'John', 'Schroder', 'Leasing Manager',
'<EMAIL>', 'c3d4e5f6-a7b8-9012-cdef-345678901238',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '133 Castlereagh St' AND address_line_2 = 'User: John Schroder' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '133 Castlereagh St' AND address_line_2 = 'Levels 2, 22 - 29' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61290352004', 'COMPANY',
'Michael', 'Rosmarin', 'Development Manager',
'<EMAIL>', 'd4e5f6a7-b8c9-0123-def0-456789012349',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '133 Castlereagh St' AND address_line_2 = 'User: Michael Rosmarin' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '133 Castlereagh St' AND address_line_2 = 'Levels 2, 22 - 29' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61290352005', 'COMPANY',
'Darren', 'Rehn', 'Project Manager',
'<EMAIL>', 'e5f6a7b8-c9d0-1234-ef01-567890123460',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '133 Castlereagh St' AND address_line_2 = 'User: Darren Rehn' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '133 Castlereagh St' AND address_line_2 = 'Levels 2, 22 - 29' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61290352006', 'COMPANY',
'Tiernan', 'O''Rourke', 'Leasing Manager',
'<EMAIL>', 'f6a7b8c9-d0e1-2345-f012-678901234571',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '133 Castlereagh St' AND address_line_2 = 'User: Tiernan O''Rourke' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '133 Castlereagh St' AND address_line_2 = 'Levels 2, 22 - 29' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
);

-- Company Member Role for Stockland users
INSERT INTO user_roles (is_active, created_at, modified_at, role_id, user_id, version)
SELECT true, NOW(), NOW(), r.id, u.id, 0
FROM roles r
JOIN app_user u ON u.email IN (
'<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>',
'<EMAIL>'
)
WHERE r.name = 'COMPANY_MEMBER';

-- Company Admin Role for Stockland user
INSERT INTO user_roles (is_active, created_at, modified_at, role_id, user_id, version)
SELECT true, NOW(), NOW(), r.id, u.id, 0
FROM roles r
JOIN app_user u ON u.email = '<EMAIL>'
WHERE r.name = 'COMPANY_ADMIN';

-- Make Andrew Whitson Company Admin for Stockland
INSERT INTO company_membership (created_at, deleted_at, is_active, modified_at, version, role_id, user_id, company_id, status)
SELECT NOW(), NULL, true, NOW(), 0, r.id, u.id, c.id, 'ACTIVE'
FROM roles r
JOIN app_user u ON u.email = '<EMAIL>'
JOIN company c ON c.name = 'Stockland'
WHERE r.name = 'COMPANY_ADMIN';

-- Stockland users as company members
INSERT INTO company_membership (created_at, deleted_at, is_active, modified_at, version, role_id, user_id, company_id, status)
SELECT NOW(), NULL, true, NOW(), 0, r.id, u.id, c.id, 'ACTIVE'
FROM roles r
JOIN app_user u ON u.email IN (
'<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>',
'<EMAIL>'
)
JOIN company c ON c.name = 'Stockland'
WHERE r.name = 'COMPANY_MEMBER';

-- Bawdens Users
INSERT INTO app_user (
is_active, created_at, deleted_at, last_login, modified_at, version, contact_number, user_type,
first_name, last_name, bio, email, keycloak_id, profile_picture_url,
primary_address_id, billing_address_id
)
VALUES
(
true, NOW(), NULL, NULL, NOW(), 0, '61296308001', 'COMPANY',
'Barry', 'Cawthorn', 'Managing Director',
'<EMAIL>', 'a1b2c3d4-e5f6-7890-abcd-ef1234567895',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '18 Ross St' AND address_line_2 = 'User: Barry Cawthorn' AND suburb = 'Parramatta' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '18 Ross St' AND address_line_2 = '201' AND suburb = 'Parramatta' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61296308002', 'COMPANY',
'Robert', 'Ally', 'Property Manager',
'<EMAIL>', 'b2c3d4e5-f6a7-8901-bcde-f**********7',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '18 Ross St' AND address_line_2 = 'User: Robert Ally' AND suburb = 'Parramatta' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '18 Ross St' AND address_line_2 = '201' AND suburb = 'Parramatta' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61296308003', 'COMPANY',
'Terry', 'Cawd', 'Sales Agent',
'<EMAIL>', 'c3d4e5f6-a7b8-9012-cdef-345678901239',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '18 Ross St' AND address_line_2 = 'User: Terry Cawd' AND suburb = 'Parramatta' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '18 Ross St' AND address_line_2 = '201' AND suburb = 'Parramatta' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61296308004', 'COMPANY',
'Peter', 'Pacetta', 'Leasing Agent',
'<EMAIL>', 'd4e5f6a7-b8c9-0123-def0-456789012350',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '18 Ross St' AND address_line_2 = 'User: Peter Pacetta' AND suburb = 'Parramatta' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '18 Ross St' AND address_line_2 = '201' AND suburb = 'Parramatta' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61296308005', 'COMPANY',
'Graeme', 'Scott', 'Property Manager',
'<EMAIL>', 'e5f6a7b8-c9d0-1234-ef01-567890123461',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '18 Ross St' AND address_line_2 = 'User: Graeme Scott' AND suburb = 'Parramatta' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '18 Ross St' AND address_line_2 = '201' AND suburb = 'Parramatta' AND address_type = 'PRIMARY' LIMIT 1)
);

-- Company Member Role for Bawdens users
INSERT INTO user_roles (is_active, created_at, modified_at, role_id, user_id, version)
SELECT true, NOW(), NOW(), r.id, u.id, 0
FROM roles r
JOIN app_user u ON u.email IN (
'<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>'
)
WHERE r.name = 'COMPANY_MEMBER';

-- Company Admin Role for Bawdens user
INSERT INTO user_roles (is_active, created_at, modified_at, role_id, user_id, version)
SELECT true, NOW(), NOW(), r.id, u.id, 0
FROM roles r
JOIN app_user u ON u.email = '<EMAIL>'
WHERE r.name = 'COMPANY_ADMIN';

-- Make Barry Cawthorn Company Admin for Bawdens
INSERT INTO company_membership (created_at, deleted_at, is_active, modified_at, version, role_id, user_id, company_id, status)
SELECT NOW(), NULL, true, NOW(), 0, r.id, u.id, c.id, 'ACTIVE'
FROM roles r
JOIN app_user u ON u.email = '<EMAIL>'
JOIN company c ON c.name = 'Bawdens'
WHERE r.name = 'COMPANY_ADMIN';

-- Bawdens users as company members
INSERT INTO company_membership (created_at, deleted_at, is_active, modified_at, version, role_id, user_id, company_id, status)
SELECT NOW(), NULL, true, NOW(), 0, r.id, u.id, c.id, 'ACTIVE'
FROM roles r
JOIN app_user u ON u.email IN (
'<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>'
)
JOIN company c ON c.name = 'Bawdens'
WHERE r.name = 'COMPANY_MEMBER';

-- CI Australia Users
INSERT INTO app_user (
is_active, created_at, deleted_at, last_login, modified_at, version, contact_number, user_type,
first_name, last_name, bio, email, keycloak_id, profile_picture_url,
primary_address_id, billing_address_id
)
VALUES
(
true, NOW(), NULL, NULL, NOW(), 0, '***********', 'COMPANY',
'Ben', 'Kardachi', 'Director',
'<EMAIL>', 'a1b2c3d4-e5f6-7890-abcd-ef1234567896',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '10 Spring St' AND address_line_2 = 'User: Ben Kardachi' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '10 Spring St' AND address_line_2 = 'Company Address' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '***********', 'COMPANY',
'Charlotte', 'Hesketh', 'Property Manager',
'<EMAIL>', 'b2c3d4e5-f6a7-8901-bcde-f**********8',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '10 Spring St' AND address_line_2 = 'User: Charlotte Hesketh' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '10 Spring St' AND address_line_2 = 'Company Address' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61282380003', 'COMPANY',
'Andrew', 'Hunter', 'Sales Agent',
'<EMAIL>', 'c3d4e5f6-a7b8-9012-cdef-345678901240',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '10 Spring St' AND address_line_2 = 'User: Andrew Hunter' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '10 Spring St' AND address_line_2 = 'Company Address' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61282380004', 'COMPANY',
'Michael', 'Stokes', 'Leasing Agent',
'<EMAIL>', 'd4e5f6a7-b8c9-0123-def0-456789012351',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '10 Spring St' AND address_line_2 = 'User: Michael Stokes' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '10 Spring St' AND address_line_2 = 'Company Address' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61282380005', 'COMPANY',
'Rebecca', 'Roberts', 'Property Manager',
'<EMAIL>', 'e5f6a7b8-c9d0-1234-ef01-567890123462',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '10 Spring St' AND address_line_2 = 'User: Rebecca Roberts' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '10 Spring St' AND address_line_2 = 'Company Address' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '***********', 'COMPANY',
'Fergus', 'Woollcombe', 'Sales Agent',
'<EMAIL>', 'f6a7b8c9-d0e1-2345-f012-678901234572',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '10 Spring St' AND address_line_2 = 'User: Fergus Woollcombe' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '10 Spring St' AND address_line_2 = 'Company Address' AND suburb = 'Sydney' AND address_type = 'PRIMARY' LIMIT 1)
);

-- Company Member Role for CI Australia users
INSERT INTO user_roles (is_active, created_at, modified_at, role_id, user_id, version)
SELECT true, NOW(), NOW(), r.id, u.id, 0
FROM roles r
JOIN app_user u ON u.email IN (
'<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>',
'<EMAIL>'
)
WHERE r.name = 'COMPANY_MEMBER';

-- Company Admin Role for CI Australia user
INSERT INTO user_roles (is_active, created_at, modified_at, role_id, user_id, version)
SELECT true, NOW(), NOW(), r.id, u.id, 0
FROM roles r
JOIN app_user u ON u.email = '<EMAIL>'
WHERE r.name = 'COMPANY_ADMIN';

-- Make Ben Kardachi Company Admin for CI Australia
INSERT INTO company_membership (created_at, deleted_at, is_active, modified_at, version, role_id, user_id, company_id, status)
SELECT NOW(), NULL, true, NOW(), 0, r.id, u.id, c.id, 'ACTIVE'
FROM roles r
JOIN app_user u ON u.email = '<EMAIL>'
JOIN company c ON c.name = 'CI Australia'
WHERE r.name = 'COMPANY_ADMIN';

-- CI Australia users as company members
INSERT INTO company_membership (created_at, deleted_at, is_active, modified_at, version, role_id, user_id, company_id, status)
SELECT NOW(), NULL, true, NOW(), 0, r.id, u.id, c.id, 'ACTIVE'
FROM roles r
JOIN app_user u ON u.email IN (
'<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>',
'<EMAIL>'
)
JOIN company c ON c.name = 'CI Australia'
WHERE r.name = 'COMPANY_MEMBER';

-- Colliers International Sydney South West Users
INSERT INTO app_user (
is_active, created_at, deleted_at, last_login, modified_at, version, contact_number, user_type,
first_name, last_name, bio, email, keycloak_id, profile_picture_url,
primary_address_id, billing_address_id
)
VALUES
(
true, NOW(), NULL, NULL, NOW(), 0, '***********', 'COMPANY',
'Gavin', 'Bishop', 'Director',
'<EMAIL>', 'a1b2c3d4-e5f6-7890-abcd-ef1234567897',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '5 Emerald Hills Blvd' AND address_line_2 = 'User: Gavin Bishop' AND suburb = 'Leppington' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '5 Emerald Hills Blvd' AND address_line_2 = 'Shop 1a' AND suburb = 'Leppington' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '***********', 'COMPANY',
'Bernard', 'Peverill', 'Property Manager',
'<EMAIL>', 'b2c3d4e5-f6a7-8901-bcde-f**********9',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '5 Emerald Hills Blvd' AND address_line_2 = 'User: Bernard Peverill' AND suburb = 'Leppington' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '5 Emerald Hills Blvd' AND address_line_2 = 'Shop 1a' AND suburb = 'Leppington' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61296021203', 'COMPANY',
'David', 'Khoury', 'Sales Agent',
'<EMAIL>', 'c3d4e5f6-a7b8-9012-cdef-345678901241',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '5 Emerald Hills Blvd' AND address_line_2 = 'User: David Khoury' AND suburb = 'Leppington' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '5 Emerald Hills Blvd' AND address_line_2 = 'Shop 1a' AND suburb = 'Leppington' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61296021204', 'COMPANY',
'Josh', 'Heffernan', 'Leasing Agent',
'<EMAIL>', 'd4e5f6a7-b8c9-0123-def0-456789012352',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '5 Emerald Hills Blvd' AND address_line_2 = 'User: Josh Heffernan' AND suburb = 'Leppington' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '5 Emerald Hills Blvd' AND address_line_2 = 'Shop 1a' AND suburb = 'Leppington' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61296021205', 'COMPANY',
'Michael', 'Crombie', 'Property Manager',
'<EMAIL>', 'e5f6a7b8-c9d0-1234-ef01-567890123463',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '5 Emerald Hills Blvd' AND address_line_2 = 'User: Michael Crombie' AND suburb = 'Leppington' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '5 Emerald Hills Blvd' AND address_line_2 = 'Shop 1a' AND suburb = 'Leppington' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61296021206', 'COMPANY',
'Tom', 'Barnier', 'Sales Agent',
'<EMAIL>', 'f6a7b8c9-d0e1-2345-f012-678901234573',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '5 Emerald Hills Blvd' AND address_line_2 = 'User: Tom Barnier' AND suburb = 'Leppington' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '5 Emerald Hills Blvd' AND address_line_2 = 'Shop 1a' AND suburb = 'Leppington' AND address_type = 'PRIMARY' LIMIT 1)
);

-- Company Member Role for Colliers users
INSERT INTO user_roles (is_active, created_at, modified_at, role_id, user_id, version)
SELECT true, NOW(), NOW(), r.id, u.id, 0
FROM roles r
JOIN app_user u ON u.email IN (
'<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>',
'<EMAIL>'
)
WHERE r.name = 'COMPANY_MEMBER';

-- Company Admin Role for Colliers user
INSERT INTO user_roles (is_active, created_at, modified_at, role_id, user_id, version)
SELECT true, NOW(), NOW(), r.id, u.id, 0
FROM roles r
JOIN app_user u ON u.email = '<EMAIL>'
WHERE r.name = 'COMPANY_ADMIN';

-- Make Gavin Bishop Company Admin for Colliers
INSERT INTO company_membership (created_at, deleted_at, is_active, modified_at, version, role_id, user_id, company_id, status)
SELECT NOW(), NULL, true, NOW(), 0, r.id, u.id, c.id, 'ACTIVE'
FROM roles r
JOIN app_user u ON u.email = '<EMAIL>'
JOIN company c ON c.name = 'Colliers International Sydney South West'
WHERE r.name = 'COMPANY_ADMIN';

-- Colliers users as company members
INSERT INTO company_membership (created_at, deleted_at, is_active, modified_at, version, role_id, user_id, company_id, status)
SELECT NOW(), NULL, true, NOW(), 0, r.id, u.id, c.id, 'ACTIVE'
FROM roles r
JOIN app_user u ON u.email IN (
'<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>',
'<EMAIL>'
)
JOIN company c ON c.name = 'Colliers International Sydney South West'
WHERE r.name = 'COMPANY_MEMBER';

-- Strathfield Partners Users
INSERT INTO app_user (
is_active, created_at, deleted_at, last_login, modified_at, version, contact_number, user_type,
first_name, last_name, bio, email, keycloak_id, profile_picture_url,
primary_address_id, billing_address_id
)
VALUES
(
true, NOW(), NULL, NULL, NOW(), 0, '61297632271', 'COMPANY',
'Robert', 'Pignataro', 'Principal',
'<EMAIL>', 'a1b2c3d4-e5f6-7890-abcd-ef1234567898',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '8 Churchill Ave' AND address_line_2 = 'User: Robert Pignataro' AND suburb = 'Strathfield' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '8 Churchill Ave' AND address_line_2 = 'Company Address' AND suburb = 'Strathfield' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61297632272', 'COMPANY',
'Francesco', 'Portolesi', 'Sales Agent',
'<EMAIL>', 'b2c3d4e5-f6a7-8901-bcde-f***********',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '8 Churchill Ave' AND address_line_2 = 'User: Francesco Portolesi' AND suburb = 'Strathfield' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '8 Churchill Ave' AND address_line_2 = 'Company Address' AND suburb = 'Strathfield' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61297632273', 'COMPANY',
'Hawre', 'Ahmad', 'Property Manager',
'<EMAIL>', 'c3d4e5f6-a7b8-9012-cdef-345678901242',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '8 Churchill Ave' AND address_line_2 = 'User: Hawre Ahmad' AND suburb = 'Strathfield' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '8 Churchill Ave' AND address_line_2 = 'Company Address' AND suburb = 'Strathfield' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61297632274', 'COMPANY',
'Vanessa', 'Kim', 'Leasing Agent',
'<EMAIL>', 'd4e5f6a7-b8c9-0123-def0-456789012353',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '8 Churchill Ave' AND address_line_2 = 'User: Vanessa Kim' AND suburb = 'Strathfield' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '8 Churchill Ave' AND address_line_2 = 'Company Address' AND suburb = 'Strathfield' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61297632275', 'COMPANY',
'Tina Xiao Yan', 'Tang', 'Sales Agent',
'<EMAIL>', 'e5f6a7b8-c9d0-1234-ef01-567890123464',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '8 Churchill Ave' AND address_line_2 = 'User: Tina Xiao Yan Tang' AND suburb = 'Strathfield' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '8 Churchill Ave' AND address_line_2 = 'Company Address' AND suburb = 'Strathfield' AND address_type = 'PRIMARY' LIMIT 1)
),
(
true, NOW(), NULL, NULL, NOW(), 0, '61297632276', 'COMPANY',
'James Jitao', 'Zhao', 'Property Manager',
'<EMAIL>', 'f6a7b8c9-d0e1-2345-f012-678901234574',
'NoPhoto_Profile.png',
(SELECT id FROM Address WHERE address_line_1 = '8 Churchill Ave' AND address_line_2 = 'User: James Jitao Zhao' AND suburb = 'Strathfield' AND address_type = 'PRIMARY' LIMIT 1),
(SELECT id FROM Address WHERE address_line_1 = '8 Churchill Ave' AND address_line_2 = 'Company Address' AND suburb = 'Strathfield' AND address_type = 'PRIMARY' LIMIT 1)
);

-- Company Member Role for Strathfield Partners users
INSERT INTO user_roles (is_active, created_at, modified_at, role_id, user_id, version)
SELECT true, NOW(), NOW(), r.id, u.id, 0
FROM roles r
JOIN app_user u ON u.email IN (
'<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>',
'<EMAIL>'
)
WHERE r.name = 'COMPANY_MEMBER';

-- Company Admin Role for Strathfield Partners user
INSERT INTO user_roles (is_active, created_at, modified_at, role_id, user_id, version)
SELECT true, NOW(), NOW(), r.id, u.id, 0
FROM roles r
JOIN app_user u ON u.email = '<EMAIL>'
WHERE r.name = 'COMPANY_ADMIN';

-- Make Robert Pignataro Company Admin for Strathfield Partners
INSERT INTO company_membership (created_at, deleted_at, is_active, modified_at, version, role_id, user_id, company_id, status)
SELECT NOW(), NULL, true, NOW(), 0, r.id, u.id, c.id, 'ACTIVE'
FROM roles r
JOIN app_user u ON u.email = '<EMAIL>'
JOIN company c ON c.name = 'Strathfield Partners'
WHERE r.name = 'COMPANY_ADMIN';

-- Strathfield Partners users as company members
INSERT INTO company_membership (created_at, deleted_at, is_active, modified_at, version, role_id, user_id, company_id, status)
SELECT NOW(), NULL, true, NOW(), 0, r.id, u.id, c.id, 'ACTIVE'
FROM roles r
JOIN app_user u ON u.email IN (
'<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>',
'<EMAIL>'
)
JOIN company c ON c.name = 'Strathfield Partners'
WHERE r.name = 'COMPANY_MEMBER';

-- Create Default Root and Downloads Folders for Company Users
INSERT INTO folder (created_at, deleted_at, is_active, modified_at, version, name, user_id, company_id, folder_type, parent_folder_id)
SELECT NOW(), NULL, true, NOW(), 0, 'Root', u.id, NULL, 'ROOT', NULL
FROM app_user u
WHERE u.user_type = 'COMPANY' AND u.email IN (
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>'
);

INSERT INTO folder (created_at, deleted_at, is_active, modified_at, version, name, user_id, company_id, folder_type, parent_folder_id)
SELECT NOW(), NULL, true, NOW(), 0, 'Downloads', u.id, NULL, 'DOWNLOADS', f.id
FROM app_user u
JOIN folder f ON f.user_id = u.id AND f.folder_type = 'ROOT'
WHERE u.user_type = 'COMPANY' AND u.email IN (
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>'
);

-- Insert Accounts for Company Users
INSERT INTO account (balance, is_active, company_id, created_at, deleted_at, modified_at, user_id, version, currency, account_type)
SELECT 0.00, true, NULL, NOW(), NULL, NOW(), u.id, 0, 'AUD', 'COMPANY'
FROM app_user u
WHERE u.user_type = 'COMPANY' AND u.email IN (
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>', '<EMAIL>',
'<EMAIL>', '<EMAIL>'
);
