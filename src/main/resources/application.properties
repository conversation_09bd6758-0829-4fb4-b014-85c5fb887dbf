
spring.application.name=areadocs
spring.main.allow-bean-definition-overriding=true



# Server Configuration
server.port=8082
server.compression.enabled=true
server.compression.mime-types=application/json,application/xml,text/html,text/plain
server.compression.min-response-size=1024

# API Configuration
api.base.url=https://anypoint.mulesoft.com/mocking/api/v1/sources/exchange/assets/81b365a6-d760-419b-9615-4642b83ca28e/serv-alert-eapi/1.0.9/m
api.client-id=abcdef123456abcdef123456abcdef12
api.client-secret=abcdef123456abcdef123456abcdef12
api.timeout=5000

# Logging levels
logging.level.org.zalando.logbook=TRACE
logging.level.org.zalando.logbook.Logbook=TRACE
logging.level.org.zalando.logbook.HttpLogWriter=TRACE
logging.level.org.zalando.logbook.LogbookAutoConfiguration=TRACE
# File Upload Settings
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# JSON Serialization
spring.jackson.serialization.FAIL_ON_EMPTY_BEANS=false
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=UTC

# Upload Directory
app.upload.dir=/tmp/areadocs/uploads

# Database Configuration
spring.datasource.url=*****************************************
spring.datasource.username=postgres
spring.datasource.password=postgres
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.hikari.connection-test-query=SELECT 1

# Connection Pool Configuration
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=30000

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.database-platform=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true
spring.jpa.open-in-view=false
spring.jpa.properties.hibernate.jdbc.batch_size=50

# Hibernate Envers Configuration
spring.jpa.properties.org.hibernate.envers.audit_table_suffix=_Audit
spring.jpa.properties.org.hibernate.envers.revision_field_name=revision_id
spring.jpa.properties.org.hibernate.envers.revision_type_field_name=revision_type
spring.jpa.properties.org.hibernate.envers.store_data_at_delete=true

# DevTools Configuration
spring.devtools.livereload.enabled=true
spring.devtools.remote.secret=my-secret-key
spring.devtools.restart.enabled=true
spring.devtools.restart.additional-paths=/app/src/main/java
spring.devtools.restart.poll-interval=500ms
spring.devtools.restart.quiet-period=200ms
spring.devtools.restart.log-condition=true
spring.devtools.restart.exclude=static/**,public/**,templates/**

# Logging Configuration
logging.level.root=INFO
logging.level.com.arealytics.areadocs=DEBUG
logging.level.org.hibernate.SQL=DEBUG


#Keycloak Details
keycloak.base-url=https://auth-dev.arealytics.com.au
spring.security.oauth2.client.registration.keycloak.client-id=areadocs-be
spring.security.oauth2.client.registration.keycloak.client-secret=0b2EVNu2c505ArzekwKZz6SPyV0Gv6vr
spring.security.oauth2.client.registration.keycloak.authorization-grant-type=authorization_code
spring.security.oauth2.client.registration.keycloak.scope=openid,profile,email
spring.security.oauth2.client.provider.keycloak.issuer-uri=${keycloak.base-url}/realms/areadocs
spring.security.oauth2.client.registration.keycloak.redirect-uri={baseUrl}/login/oauth2/code/{registrationId}
spring.security.oauth2.resourceserver.jwt.issuer-uri=${keycloak.base-url}/realms/areadocs
spring.security.oauth2.resourceserver.jwt.jwk-set-uri=${keycloak.base-url}/realms/areadocs/protocol/openid-connect/certs

arealytics.areadocs.redirect-uri=http://localhost:4200/login
arealytics.areadocs.url = http://localhost:4200
areadocs-fe-client-id=areadocs-fe


# Flyway configuration
spring.flyway.enabled=true
spring.flyway.locations=classpath:db/migration
spring.flyway.baseline-on-migrate=true
spring.flyway.baseline-version=1
spring.flyway.url=${spring.datasource.url}
spring.flyway.user=${spring.datasource.username}
spring.flyway.password=${spring.datasource.password}

# SERV API CONFIGURATION
serv.api.baseUrl= https://anypoint.mulesoft.com/mocking/api/v1/sources/exchange/assets/81b365a6-d760-419b-9615-4642b83ca28e/serv-ddp-discovery-eapi/1.3.0/m
serv.api.clientId=abcdef123456abcdef123456abcdef12
serv.api.clientSecret=abcdef123456abcdef123456abcdef12
serv.api.authToken=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c
serv.api.alertBaseUrl=https://anypoint.mulesoft.com/mocking/api/v1/sources/exchange/assets/81b365a6-d760-419b-9615-4642b83ca28e/serv-alert-eapi/1.0.9/m
serv.api.verifyBaseUrl=https://anypoint.mulesoft.com/mocking/api/v1/sources/exchange/assets/81b365a6-d760-419b-9615-4642b83ca28e/serv-verify-eapi/1.0.9/m
serv.api.ordersBaseUrl=https://anypoint.mulesoft.com/mocking/api/v1/sources/exchange/assets/81b365a6-d760-419b-9615-4642b83ca28e/serv-ddp-orders-eapi/1.3.0/m/orders/v1

# Enable mock responses when external APIs are unavailable
serv.api.useMockResponses=true

# AWS S3 Configuration
aws.access.key.id=********************
aws.secret.access.key=jaB1KhbI5MWVGYAiZVvTjvSPE2m+EcD9s8jksGUJ
aws.s3.bucket.name=areadocs-test
aws.region=us-east-1
aws.s3.acl=public-read

# S3 subfolders
s3.paths.document-uploads=document-files
s3.paths.user-profile-images-uploads=user-profile-images/

#Keycloak Details

keycloak.serverUrl=https://auth-dev.arealytics.com.au/
keycloak.realm=areadocs
keycloak.clientId=areadocs-be
keycloak.clientSecret=0b2EVNu2c505ArzekwKZz6SPyV0Gv6vr
keycloak.grantType=client_credentials

#swagger details
swagger.server.url=http://localhost:8082
swagger.server.description=Local Dev Server
# Content negotiation
spring.mvc.contentnegotiation.favor-path-extension=false
spring.mvc.contentnegotiation.favor-parameter=false
spring.mvc.contentnegotiation.media-types.json=application/json
spring.mvc.default-content-type=application/json

# Springdoc OpenAPI config
springdoc.default-produces-media-type=application/json
springdoc.default-consumes-media-type=application/json
# Platform Admin Configuration
platform.admin.email=<EMAIL>
platform.admin.password=Admin@123
platform.admin.role=PLATFORM_ADMIN

# Temporary fath for profile images
temp-profile-images-path=/tmp/areadocs/profile-images
