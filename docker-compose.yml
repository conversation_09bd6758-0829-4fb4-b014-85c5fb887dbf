version: '3.9'

services:
  # PostgreSQL Database
  db:
    image: postgres:17-alpine
    container_name: postgres-db
    environment:
      POSTGRES_DB: ${DB_DATABASE_NAME}
      POSTGRES_USER: ${DB_DEV_USERNAME}
      POSTGRES_PASSWORD: ${DB_DEV_PASSWORD}
    ports:
      - "${DB_PORT:-5432}:5432"
    volumes:
      - db-data-new:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
      - ./pg_hba.conf:/docker-entrypoint-initdb.d/pg_hba.conf  
        # - ./pg_hba.conf:/var/lib/postgresql/data/pg_hba.conf
    networks:
      - areadocs-net
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_DEV_USERNAME}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # Spring Boot Application
  app-backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: areadocs-backend
    ports:
      - "${SERVER_PORT:-8080}:8080"
    depends_on:
      - db
    environment:
      #SPRING_PROFILES_ACTIVE: ${SPRING_PROFILES_ACTIVE:-dev}
      SPRING_DATASOURCE_URL: jdbc:postgresql://db:5432/${DB_DATABASE_NAME}
      SPRING_DATASOURCE_USERNAME: ${DB_DEV_USERNAME}
      SPRING_DATASOURCE_PASSWORD: ${DB_DEV_PASSWORD}
      SERVER_PORT: 8080
        #JWT_SECRET: ${JWT_SECRET:-default-dev-secret-key}
        #JWT_EXPIRATION_MS: ${JWT_EXPIRATION_MS:-86400000}
        #CORS_ALLOWED_ORIGIN: ${CORS_ALLOWED_ORIGIN:-http://localhost:3000}
    volumes:
      - ${APP_UPLOAD_DIR}:/app/uploads
    networks:
      - areadocs-net
    restart: unless-stopped

  # pgAdmin for Database Management
  #pgadmin:
  #  image: dpage/pgadmin4
  #  container_name: pgadmin
  #  environment:
  #    PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
  #    PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-admin123}
        #  ports:
        #   - "${PGADMIN_PORT:-5050}:80"
        #depends_on:
        # - db
        #networks:
        # - areadocs-net
        #restart: unless-stopped

# Persistent Volumes
volumes:
  db-data-new:
    name: areadocs-postgres-data-new

# Network Configuration
networks:
  areadocs-net:
    driver: bridge
