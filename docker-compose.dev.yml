version: '3.9'

services:
  # PostgreSQL Database
  db:
    image: postgres:17-alpine
    container_name: postgres-db
    environment:
      POSTGRES_DB: ${DB_DATABASE_NAME:-areadocs}
      POSTGRES_USER: ${DB_DEV_USERNAME:-postgres}
      POSTGRES_PASSWORD: ${DB_DEV_PASSWORD:-postgres}
    ports:
      - "${DB_PORT:-5431}:5432"
    volumes:
      - ./pgdata:/var/lib/postgresql/data
    networks:
      - areadocs-net
    restart: unless-stopped

  # # Spring Boot Application
  # app-backend:
  #    build:
  #      context: .
  #      dockerfile: Dockerfile.dev
  #    container_name: areadocs-backend-dev
  #    develop:
  #      watch:
  #        - path: ./src
  #          target: /app/src
  #          action: sync # Changed from rebuild to sync
  #        - path: ./pom.xml
  #          target: /app/pom.xml
  #          action: rebuild # Rebuild only for pom.xml changes
  #    ports:
  #      - "${SERVER_PORT:-8080}:8080"
  #    depends_on:
  #      - db
  #    environment:
  #      SPRING_PROFILES_ACTIVE: dev
  #      SPRING_DATASOURCE_URL: jdbc:postgresql://db:5432/${DB_DATABASE_NAME:-areadocs}
  #      SPRING_DATASOURCE_USERNAME: ${DB_DEV_USERNAME:-postgres}
  #      SPRING_DATASOURCE_PASSWORD: ${DB_DEV_PASSWORD:-postgres}
  #      JWT_SECRET: ${JWT_SECRET:-default-dev-secret-key}
  #      JWT_EXPIRATION_MS: ${JWT_EXPIRATION_MS:-86400000}
  #      CORS_ALLOWED_ORIGIN: ${CORS_ALLOWED_ORIGIN:-http://localhost:3000}
  #    volumes:
  #      - ./src:/app/src
  #      - ./pom.xml:/app/pom.xml
  #      - ~/.m2:/root/.m2
  #    networks:
  #      - areadocs-net
  #    restart: unless-stopped

  pgadmin:
    image: dpage/pgadmin4
    container_name: pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-admin123}
    ports:
      - "${PGADMIN_PORT:-5050}:80"
    depends_on:
      - db
    networks:
      - areadocs-net
    restart: unless-stopped

networks:
  areadocs-net:
    driver: bridge
